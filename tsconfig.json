{
  "compilerOptions": {
    "module": "commonjs",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true, // ignore conflicting .d.ts in node_modules
    "jsx": "react-jsx",
    "target": "es2019",
    "noImplicitAny": true,
    "moduleResolution": "node",
    "sourceMap": true,
    "outDir": "dist",
    "baseUrl": ".",

    "paths": {
      "*": ["node_modules/*", "src/types/*"],
      "types/*": ["src/types/*"]
    },

    "inlineSources": true,

    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/",
    "isolatedModules": true
  },
  "include": [
    "src/app.ts",
    "src/server.ts",
    "src/jobs/*",
    "src/jobs/cron/*.ts",
    "src/tmp-scripts/*.ts",
    "src/scripts/**/*",
    "src/slack/**/*"
  ],
  "exclude": ["*.js, **/*.js"]
}
