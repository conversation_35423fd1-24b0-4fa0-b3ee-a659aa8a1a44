import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";
import CreditTicketCronService from "../services/creditTicketCronService";
import CronJob from "./cronJob";

class CreditReportingCronJob extends CronJob {
  cronName = CronJobNameEnum.CREDIT_REPORTING;

  /**
   * @description Cron running multiple times per day, reporting our open credit.
   */
  async processFn(): Promise<void> {
    logger.info("🤑 Checking our daily credits...", {
      module: `cron:${this.cronName}`
    });
    await CreditTicketCronService.reportOpenCredit();
    logger.info("✅ Completed checking our daily credits.", {
      module: `cron:${this.cronName}`
    });
  }
}

new CreditReportingCronJob().run();
