import logger from "../../external-services/loggerService";
import AccountService from "../../services/accountService";
import DataVerificationService from "../../services/dataVerificationService";
import { CronJobNameEnum } from "../configs/cronNames";
import CorporateEventCronService from "../services/corporateEventCronService";
import MandateCronService from "../services/mandateCronService";
import PortfolioCronService from "../services/portfolioCronService";
import CronJob from "./cronJob";

class NightlyCronJob extends CronJob {
  cronName = CronJobNameEnum.NIGHTLY;

  /**
   * @description Cron running on a nightly schedule.
   */
  async processFn(): Promise<void> {
    logger.info("🤑 Identifying portfolio holdings value & cash to Mixpanel...", {
      module: `cron:${this.cronName}`
    });
    await PortfolioCronService.identifyPortfolioHoldingsCashValue();
    logger.info("✅ Completed identifying portfolio holdings value & cash to Mixpanel.", {
      module: `cron:${this.cronName}`
    });

    logger.info("💼 Syncing active wealthkernel mandates...", { module: `cron:${this.cronName}` });
    await MandateCronService.syncActiveWkMandates();
    logger.info("✅ Wealthkernel active mandates synced", { module: `cron:${this.cronName}` });

    logger.info("🖥️ Initiating syncing of suspended WK accounts...", { module: `cron:${this.cronName}` });
    await AccountService.syncAllSuspendedWkAccounts();
    logger.info("✅ Completed processing of suspended WK accounts", { module: `cron:${this.cronName}` });

    logger.info("🧔 Running our data verification checks...", { module: `cron:${this.cronName}` });

    logger.info("💰 Checking our Wealthkernel portfolio balance...", { module: `cron:${this.cronName}` });
    await DataVerificationService.checkWealthyhoodCashBalance();
    logger.info("✅ Checked our Wealthkernel portfolio balance...", { module: `cron:${this.cronName}` });

    logger.info("🔎 Checking transactions that are stagnant...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkStagnantTransactions();
    logger.info("✅ Checked transactions that are stagnant!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking credit tickets that are stagnant...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkStagnantCreditTickets();
    logger.info("✅ Checked credit tickets that are stagnant!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking payouts that are stagnant...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkStagnantPayouts();
    logger.info("✅ Checked payouts that are stagnant!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking automations that are stagnant...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkStagnantAutomations();
    logger.info("✅ Checked automations that are stagnant!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking rewards that are stagnant...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkStagnantRewards();
    logger.info("✅ Checked rewards that are stagnant!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking user data requests that are stagnant...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkStagnantUserDataRequests();
    logger.info("✅ Checked user data requests that are stagnant!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking bank accounts that are stagnant...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkStagnantBankAccounts();
    logger.info("✅ Checked bank accounts that are stagnant!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🙈 Checking Mailchimp state...", { module: `cron:${this.cronName}` });
    await DataVerificationService.checkMailchimpState();
    logger.info("✅ Checked Mailchimp state!", { module: `cron:${this.cronName}` });

    logger.info("🔎 Checking users that are non-converted but have settled investments...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkNonConvertedUsers();
    logger.info("✅ Checked users that are non-converted but have settled investments!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking users that have summaries but have not the latest summary they should have...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkUsersWithMissingSummaries();
    logger.info("✅ Checked users that have summaries but have not the latest summary they should have!", {
      module: `cron:${this.cronName}`
    });

    logger.info(
      "🔎 Checking users that have passed KYC have a correct model state (number of portfolios, accounts, addresses)...",
      {
        module: `cron:${this.cronName}`
      }
    );
    await DataVerificationService.checkModelState();
    logger.info("✅ Checked users model state!", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking that subscriptions have valid nextChargeAt field", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkSubscriptionNextChargeDate();
    logger.info("✅ Checked that subscriptions have valid nextChargeAt field", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Searching for potentially duplicate repeating top-ups...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.findDuplicateRepeatingTopUps();
    logger.info("✅ Completed search for potentially duplicate repeating top-ups.", {
      module: `cron:${this.cronName}`
    });

    logger.info("🔎 Checking for potentially unpublished finimize content entries...", {
      module: `cron:${this.cronName}`
    });
    await DataVerificationService.checkUnpublishedFinimizeContentEntries();
    logger.info("✅ Checked for potentially unpublished finimize content entries.", {
      module: `cron:${this.cronName}`
    });

    logger.info("✅ Completed data verification checks", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating all new asset ISIN changes...", {
      module: `cron:${this.cronName}`
    });
    await CorporateEventCronService.createAllAssetIsinChanges();
    logger.info("✅ Completed creating all new asset ISIN changes.", {
      module: `cron:${this.cronName}`
    });
  }
}

new NightlyCronJob().run();
