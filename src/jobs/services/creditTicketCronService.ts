import { CreditTicket, CreditTicketDocument } from "../../models/CreditTicket";
import Decimal from "decimal.js";
import CreditTicketService from "../../services/creditTicketService";
import { ProviderEnum } from "../../configs/providersConfig";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { DepositCashTransaction } from "../../models/Transaction";
import { DepositMethodEnum } from "../../types/transactions";
import DateUtil from "../../utils/dateUtil";
import { DevengoService } from "../../external-services/devengoService";
import ProviderService from "../../services/providerService";
import { entitiesConfig } from "@wealthyhood/shared-configs";

class CreditTicketCronService {
  /**
   * PUBLIC METHODS
   */

  /**
   * @description Syncs all credit tickets that have pending WK internal transfers.
   */
  public async syncCreditTicketInternalTransfers(): Promise<void> {
    // Sync credit tickets that were submitted at least 15 minutes ago, to act as a fallback to webhook mechanism
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

    const creditTicketsWithPendingInternalTransfers = await CreditTicket.find({
      status: "Pending",
      "deposit.providers.wealthkernel.status": { $in: ["Requested", "Accepted"] },
      "deposit.providers.wealthkernel.id": { $exists: true },
      "deposit.providers.wealthkernel.submittedAt": { $lt: fifteenMinutesAgo }
    }).populate("portfolio");

    for (let i = 0; i < creditTicketsWithPendingInternalTransfers.length; i++) {
      const creditTicket = creditTicketsWithPendingInternalTransfers[i];

      await CreditTicketService.syncCreditTicketWithPendingInternalTransferSafely(creditTicket);
    }
  }

  /**
   * @description Creates WK internal transfers for all credit tickets that have been created and have no submitted transfers yet.
   */
  public async createCreditTicketInternalTransfers(): Promise<void> {
    // Create internal transfers for credit tickets that were createdAt at least 15 minutes ago, to act as a fallback to webhook mechanism
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

    const creditTicketsWithUnsubmittedInternalTransfers: CreditTicketDocument[] = await CreditTicket.find({
      status: "Pending",
      "deposit.activeProviders": ProviderEnum.WEALTHKERNEL,
      createdAt: { $lt: fifteenMinutesAgo },
      $or: [
        {
          "deposit.providers.wealthkernel.id": {
            $exists: false
          }
        },
        {
          "deposit.providers.wealthkernel.id": {
            $eq: undefined
          }
        }
      ]
    }).populate("portfolio");

    for (let i = 0; i < creditTicketsWithUnsubmittedInternalTransfers.length; i++) {
      await CreditTicketService.createInternalTransferForCreditTicketSafely(
        creditTicketsWithUnsubmittedInternalTransfers[i]
      );
    }
  }

  /**
   * For credit tickets reporting, we report several pieces of information:
   * 1. How much open credit amount have, currently.
   * 2. Current balance of Devengo collection account(s) + in-flight payments.
   * 3. Current balance of Wealthkernel funding portfolio.
   * 4. Whether there are any open (Pending or Credited) credit tickets for more than 2 business days.
   * 5. Whether the total EU deposit amount for the last two business days matches the credited (non-Rejected) amount.
   */
  public async reportOpenCredit(): Promise<void> {
    const twoBusinessDaysAgo = DateUtil.getStartOfDay(DateUtil.getDateNWorkDaysAgo(new Date(Date.now()), 2));
    const startOfYesterday = DateUtil.getStartOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 1));

    const [openCreditTickets, oldOpenTickets, recentCreditedTickets, recentDeposits, inFlightDeposits] =
      await Promise.all([
        CreditTicket.find({
          status: { $in: ["Credited", "Pending", "CreditPendingNameCheck"] },
          "deposit.providers.wealthkernel.status": { $in: ["Accepted", "Completed"] }
        }),
        CreditTicket.find({
          status: { $in: ["Pending", "Credited", "CreditPendingNameCheck"] },
          createdAt: { $lt: twoBusinessDaysAgo }
        }),
        CreditTicket.find({
          createdAt: { $gte: startOfYesterday },
          status: { $in: ["Credited", "Settled", "CreditPendingNameCheck"] }
        }),
        DepositCashTransaction.find({
          createdAt: { $gte: startOfYesterday },
          depositMethod: DepositMethodEnum.BANK_TRANSFER
        }),
        DepositCashTransaction.find({
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed",
          "providers.wealthkernel.status": { $ne: "Settled" }
        })
      ]);

    const totalOpenCreditAmount = openCreditTickets
      .map(({ consideration }) => consideration.amount)
      .reduce((sum: Decimal, value: number) => sum.plus(value), new Decimal(0))
      .div(100);
    const totalRecentDeposits = recentDeposits
      .map(({ consideration }) => consideration.amount)
      .reduce((sum: Decimal, value: number) => sum.plus(value), new Decimal(0))
      .div(100);
    const totalRecentCredited = recentCreditedTickets
      .map(({ consideration }) => consideration.amount)
      .reduce((sum: Decimal, value: number) => sum.plus(value), new Decimal(0))
      .div(100);

    const { account } = await DevengoService.Instance.getAccount(
      process.env.DEVENGO_BANK_TRANSFER_COLLECTION_ACCOUNT_ID
    );
    const devengoCollectionBalance = new Decimal(account.balance.total.cents).div(100);
    const inFlightPaymentsAmount = inFlightDeposits
      .map(({ consideration }) => consideration.amount)
      .reduce((sum: Decimal, value: number) => sum.plus(value), new Decimal(0))
      .div(100)
      .plus(devengoCollectionBalance);

    // Retrieve Wealthkernel funding cash balance (cents)
    const wkFundingCashCents = await ProviderService.getBrokerageService(
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    ).retrieveWealthyhoodFundingCashBalance();
    const wkFundingCashBalance = new Decimal(wkFundingCashCents).div(100);

    eventEmitter.emit(events.creditTickets.creditedAmountReported.eventId, {
      totalOpenCreditAmount,
      oldOpenTickets: oldOpenTickets.length,
      depositVsCreditedMatch: totalRecentDeposits.equals(totalRecentCredited),
      totalRecentDeposits,
      totalRecentCredited,
      devengoCollectionBalance,
      wkFundingCashBalance,
      inFlightPaymentsAmount
    });
  }
}

export default new CreditTicketCronService();
