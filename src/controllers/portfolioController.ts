import { CustomRequest, CustomResponse } from "custom";
import Decimal from "decimal.js";
import { Response } from "express";
import logger from "../external-services/loggerService";
import {
  AllocationCreationFlowEnum,
  HoldingsType,
  PortfolioDocument,
  PortfolioModeEnum
} from "../models/Portfolio";
import { UserDocument } from "../models/User";
import PortfolioService, { PendingOrdersType, PortfolioAllocationMethodEnum } from "../services/portfolioService";
import CurrencyUtil from "../utils/currencyUtil";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import validator from "validator";
import { BadRequestError } from "../models/ApiErrors";
import { investmentsConfig, investmentUniverseConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { TransactionService } from "../services/transactionService";
import { DepositCashTransactionDocument } from "../models/Transaction";
import BankAccountValidationUtil from "../utils/bankAccountValidationUtil";
import { GiftDocument } from "../models/Gift";
import GiftService from "../services/giftService";
import RestUtil from "../utils/restUtil";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import ConfigUtil from "../utils/configUtil";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import { TransactionRepository } from "../repositories/transactionRepository";

const {
  MIN_ALLOWED_INVESTMENT,
  MIN_ALLOWED_WITHDRAWAL,
  MIN_ALLOWED_SAVINGS_INVESTMENT,
  MIN_ALLOWED_SAVINGS_WITHDRAWAL
} = investmentsConfig;

type FillClientDisplayFields = {
  prices: boolean;
};

export default class PortfolioController {
  /**
   * @description Handles a portfolio update by submitting pending orders
   * passed in the request body.
   *
   * The endpoint can only accept a single 'sell' or 'buy' order.
   */
  public static readonly submitOrder = async (req: CustomRequest, res: Response): Promise<Response> => {
    const portfolioId = req.params.id;
    const pendingOrders: PendingOrdersType = req.body.pendingOrders;
    const paymentMethod = req.query.paymentMethod ?? "cash";
    const [asset, pendingOrder] = Object.entries(pendingOrders)[0];

    let gift: GiftDocument;
    if (paymentMethod === "gift") {
      ParamsValidationUtil.isObjectIdParamValid("gift", req.body.gift);
      gift = await GiftService.getGift(req.body.gift);
      if (!gift) {
        throw new BadRequestError("A gift with that ID doesn't exist!");
      }

      const giftAmount = gift.consideration.amount;
      // Check gift value is the same as the order amount
      if (giftAmount !== Decimal.mul(pendingOrder.money, 100).toNumber()) {
        throw new BadRequestError(
          `You want to invest ${CurrencyUtil.formatCurrency(
            pendingOrder.money,
            req.user.currency,
            ConfigUtil.getDefaultUserLocale(req.user.residencyCountry)
          )} but your gift is of value ${CurrencyUtil.formatCurrency(
            Decimal.div(giftAmount, 100).toNumber(),
            req.user.currency,
            ConfigUtil.getDefaultUserLocale(req.user.residencyCountry)
          )}`
        );
      }
    }

    const executeEtfOrdersInRealtime =
      ParamsValidationUtil.isBooleanParamValid(
        "executeEtfOrdersInRealtime",
        req.query.executeEtfOrdersInRealtime as string,
        {
          isRequired: false
        }
      ) ?? req.user.isRealtimeETFExecutionEnabled;

    const assetTransaction = await PortfolioService.submitOrder(
      portfolioId,
      asset as investmentUniverseConfig.AssetType,
      pendingOrder,
      {
        pendingGift: gift,
        executeEtfOrdersInRealtime
      }
    );
    return res.status(200).json(assetTransaction);
  };

  /**
   * @description Handles a portfolio update which is pending deposit,
   * by submitting the pending order passed in the request body.
   * This endpoint only supports a single buy order.
   */
  public static readonly submitOrderPendingDeposit = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const portfolioId = req.params.id;

    ParamsValidationUtil.isObjectIdParamValid("pendingDeposit", req.body.pendingDeposit);
    const pendingOrders: PendingOrdersType = req.body.pendingOrders;

    const [asset, pendingOrder] = Object.entries(pendingOrders)[0];
    if (pendingOrder.side == "sell") {
      throw new BadRequestError("Endpoint does not support sell orders", "Operation failed");
    }

    const pendingDeposit: DepositCashTransactionDocument = await TransactionRepository.getDepositCashTransaction(
      req.body.pendingDeposit
    );
    if (!pendingDeposit) {
      throw new BadRequestError("A deposit with that ID doesn't exist!");
    }

    const pendingDepositAmount = pendingDeposit.consideration.amount;
    // Check deposit value is the same as the order amount
    if (pendingDepositAmount !== Decimal.mul(pendingOrder.money, 100).toNumber()) {
      throw new BadRequestError(
        `You want to 1-step deposit & invest ${CurrencyUtil.formatCurrency(pendingOrder.money, req.user.currency, ConfigUtil.getDefaultUserLocale(req.user.residencyCountry))} but you deposited ${CurrencyUtil.formatCurrency(
          Decimal.div(pendingDepositAmount, 100).toNumber(),
          req.user.currency,
          ConfigUtil.getDefaultUserLocale(req.user.residencyCountry)
        )}`
      );
    }

    const executeEtfOrdersInRealtime =
      ParamsValidationUtil.isBooleanParamValid(
        "executeEtfOrdersInRealtime",
        req.query.executeEtfOrdersInRealtime as string,
        {
          isRequired: false
        }
      ) ?? req.user.isRealtimeETFExecutionEnabled;

    const assetTransaction = await PortfolioService.submitOrder(
      portfolioId,
      asset as investmentUniverseConfig.AssetType,
      pendingOrder,
      {
        pendingDeposit,
        executeEtfOrdersInRealtime
      }
    );

    return res.status(200).send(assetTransaction);
  };

  /**
   * @description Handles cash-to-portfolio conversion during portfolio top-up.
   * Steps:
   * - Safety checks to allow action to take place
   * - Asset transaction creation
   * - Creation of corresponding buy orders (wealthkernel & db storage)
   * - Linking of orders to parent asset transaction
   * - Deduct available investor cash
   */
  public static readonly buyPortfolio = async (req: CustomRequest, res: CustomResponse): Promise<Response> => {
    const user: UserDocument = req.user;

    logger.info(`Initiating portfolio buy for portfolio ${req.params.id}`, {
      module: "PortfolioController",
      method: "buyPortfolio",
      userEmail: user.email,
      data: {
        body: req.body
      }
    });

    // 1. Safety checks
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res, true);
    const orderAmount = ParamsValidationUtil.isNumericParamValid("orderAmount", req.body.orderAmount);
    const allocationMethod = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "allocationMethod",
      req.query.allocationMethod,
      PortfolioAllocationMethodEnum,
      {
        isRequired: true
      }
    ) as PortfolioAllocationMethodEnum;

    if (
      allocationMethod === PortfolioAllocationMethodEnum.TARGET_ALLOCATION &&
      !portfolio.isTargetAllocationSetup
    ) {
      throw new BadRequestError("Target allocation is not set up for this portfolio");
    } else if (allocationMethod === PortfolioAllocationMethodEnum.HOLDINGS && !portfolio.holdings.length) {
      throw new BadRequestError("Portfolio has no holdings");
    }

    const paymentMethod = req.query.paymentMethod ?? "cash";
    let gift: GiftDocument;
    if (paymentMethod === "cash") {
      const availableCash = portfolio?.cash?.[user.currency]?.available ?? 0;
      if (orderAmount > availableCash) {
        throw new BadRequestError(
          `You want to invest ${CurrencyUtil.formatCurrency(orderAmount, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))} buy you only have ${CurrencyUtil.formatCurrency(availableCash, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))} deposited`
        );
      }
    } else if (paymentMethod === "gift") {
      ParamsValidationUtil.isObjectIdParamValid("gift", req.body.gift);
      gift = await GiftService.getGift(req.body.gift);
      if (!gift) {
        throw new BadRequestError("A gift with that ID doesn't exist!");
      }

      const giftAmount = gift.consideration.amount;
      // Check gift value is the same as the order amount
      if (giftAmount !== Decimal.mul(orderAmount, 100).toNumber()) {
        throw new BadRequestError(
          `You want to invest ${CurrencyUtil.formatCurrency(orderAmount, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))} but your gift is of value ${CurrencyUtil.formatCurrency(
            Decimal.div(giftAmount, 100).toNumber(),
            user.currency,
            ConfigUtil.getDefaultUserLocale(user.residencyCountry)
          )}`
        );
      }
    }

    if (orderAmount < MIN_ALLOWED_INVESTMENT) {
      logger.warn(
        `Attempted to invest with less than ${CurrencyUtil.formatCurrency(MIN_ALLOWED_INVESTMENT, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}`,
        {
          module: "PortfolioController",
          method: "buyPortfolio",
          userEmail: user.email
        }
      );
      throw new BadRequestError(
        `You need to invest at least ${CurrencyUtil.formatCurrency(MIN_ALLOWED_INVESTMENT, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}.`
      );
    }

    const executeEtfOrdersInRealtime =
      ParamsValidationUtil.isBooleanParamValid(
        "executeEtfOrdersInRealtime",
        req.query.executeEtfOrdersInRealtime as string,
        {
          isRequired: false
        }
      ) ?? req.user.isRealtimeETFExecutionEnabled;

    const [transaction, monthlyInvestmentPromptData] = await Promise.all([
      PortfolioService.buyAssetsForPortfolio(portfolio, orderAmount, {
        allocationMethod: allocationMethod,
        pendingGift: gift,
        executeEtfOrdersInRealtime
      }),
      (async () => {
        // Skip fetching if user has converted portfolio
        if (user.isConvertingPortfolio || user.hasConvertedPortfolio) {
          return null;
        }

        return TransactionService.getMonthlyInvestmentPromptData(user.id);
      })()
    ]);

    const shouldPromptToMonthly = TransactionService.shouldPromptForMonthlyInvestment(user, transaction.id, {
      ...monthlyInvestmentPromptData,
      isGiftUsed: !!gift
    });

    return res.status(200).json({
      ...transaction,
      shouldPromptToMonthly
    });
  };

  /**
   * @description Handles pending deposit to portfolio conversion during 1-step deposit & invest.
   * Steps:
   * - Safety checks to allow action to take place
   * - Asset transaction creation (with pending deposit reference and PendingDeposit status)
   * - Creation of corresponding buy orders (only db storage)
   * - Linking of orders to parent asset transaction
   */
  public static readonly buyPortfolioPendingDeposit = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const user: UserDocument = req.user;
    const portfolioId = req.params.id;

    logger.info(`Initiating portfolio buy pending deposit for portfolio ${portfolioId}`, {
      module: "PortfolioController",
      method: "buyPortfolioPendingDeposit",
      userEmail: user.email,
      data: {
        body: req.body
      }
    });

    const portfolio = await RestUtil.getPortfolioFromResponse(req, res, true);

    // 1. Safety checks
    ParamsValidationUtil.isNumericParamValid("orderAmount", req.body.orderAmount);
    ParamsValidationUtil.isObjectIdParamValid("pendingDeposit", req.body.pendingDeposit);
    const allocationMethod = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "allocationMethod",
      req.query.allocationMethod,
      PortfolioAllocationMethodEnum,
      {
        isRequired: true
      }
    ) as PortfolioAllocationMethodEnum;

    if (
      allocationMethod === PortfolioAllocationMethodEnum.TARGET_ALLOCATION &&
      !portfolio.isTargetAllocationSetup
    ) {
      throw new BadRequestError("Target allocation is not set up for this portfolio");
    } else if (allocationMethod === PortfolioAllocationMethodEnum.HOLDINGS && !portfolio.holdings.length) {
      throw new BadRequestError("Portfolio has no holdings");
    }

    const pendingDepositId = req.body.pendingDeposit;
    const pendingDeposit: DepositCashTransactionDocument =
      await TransactionRepository.getDepositCashTransaction(pendingDepositId);
    if (!pendingDeposit) {
      throw new BadRequestError("A deposit with that ID doesn't exist!");
    }

    const orderAmount = parseFloat(req.body.orderAmount);

    const pendingDepositAmount = pendingDeposit.consideration.amount;
    // Check deposit value is the same as the order amount
    if (pendingDepositAmount !== Decimal.mul(orderAmount, 100).toNumber()) {
      throw new BadRequestError(
        `You want to 1-step deposit & invest ${CurrencyUtil.formatCurrency(orderAmount, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))} but you deposited ${CurrencyUtil.formatCurrency(
          Decimal.div(pendingDepositAmount, 100).toNumber(),
          user.currency,
          ConfigUtil.getDefaultUserLocale(user.residencyCountry)
        )}`
      );
    }

    if (orderAmount < MIN_ALLOWED_INVESTMENT) {
      logger.warn(
        `Attempted to invest with less than ${CurrencyUtil.formatCurrency(MIN_ALLOWED_INVESTMENT, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}`,
        {
          module: "PortfolioController",
          method: "buyPortfolioPendingDeposit",
          userEmail: user.email
        }
      );
      throw new BadRequestError(
        `You need to invest at least ${CurrencyUtil.formatCurrency(MIN_ALLOWED_INVESTMENT, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}.`
      );
    }

    const executeEtfOrdersInRealtime =
      ParamsValidationUtil.isBooleanParamValid(
        "executeEtfOrdersInRealtime",
        req.query.executeEtfOrdersInRealtime as string,
        {
          isRequired: false
        }
      ) ?? req.user.isRealtimeETFExecutionEnabled;

    const [transaction, monthlyInvestmentPromptData] = await Promise.all([
      PortfolioService.buyAssetsForPortfolio(portfolio, orderAmount, {
        allocationMethod: allocationMethod,
        pendingDeposit,
        executeEtfOrdersInRealtime
      }),
      (async () => {
        // Skip fetching if user has converted portfolio
        if (user.isConvertingPortfolio || user.hasConvertedPortfolio) {
          return null;
        }

        return TransactionService.getMonthlyInvestmentPromptData(user.id);
      })()
    ]);

    const shouldPromptToMonthly = TransactionService.shouldPromptForMonthlyInvestment(user, transaction.id, {
      ...monthlyInvestmentPromptData,
      isGiftUsed: false // No gift involved in pending deposit
    });

    return res.status(200).json({
      ...transaction,
      shouldPromptToMonthly
    });
  };

  /**
   * @deprecated after the addition of realtime ETF execution.
   *
   * @description Handles portfolio-to-cash conversion during portfolio selling.
   * - Safety checks to allow action to take place
   * - Asset transaction creation
   * - Creation of corresponding sell orders (wealthkernel & db storage)
   * - Linking of orders to parent asset transaction
   * - Deduct available investor cash
   */
  public static readonly sellPortfolio = async (req: CustomRequest, res: CustomResponse): Promise<Response> => {
    throw new BadRequestError("Portfolio sells are not allowed.");
  };

  /**
   * @deprecated after the addition of realtime ETF execution.
   *
   * @param req
   * @param res
   */
  public static readonly sellWholePortfolio = async (req: CustomRequest, res: Response): Promise<Response> => {
    throw new BadRequestError("Portfolio sells are not allowed.");
  };

  public static readonly createOrUpdatePortfolioAllocation = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const { allocation } = req.body;
    if (!allocation) {
      throw new BadRequestError("Param 'allocation' is required", "Invalid parameter");
    }

    const invalidAssetKeys = Object.keys(allocation).filter(
      (assetKey: investmentUniverseConfig.AssetType) => !InvestmentUniverseUtil.isAssetActive(assetKey)
    );
    if (invalidAssetKeys.length > 0) {
      throw new BadRequestError(`Invalid asset keys '${invalidAssetKeys}'`, "Invalid parameter");
    }

    const invalidAssetValues = Object.values(allocation).filter(
      (value: number) => !validator.isNumeric(value.toString())
    );
    if (invalidAssetValues.length > 0) {
      throw new BadRequestError(`Invalid percentages values '${invalidAssetValues}'`, "Invalid parameter");
    }

    if (
      Object.values(allocation)
        .map((value) => parseFloat(value.toString()))
        .reduce((sum, value) => sum + value, 0) != 100
    ) {
      throw new BadRequestError("Percentages of 'allocation' do not sum to 100", "Invalid parameter");
    }

    let allocationCreationFlow: AllocationCreationFlowEnum;
    if (req.body.flow) {
      // set allocationCreationFlow field only if it's set, otherwise ignore
      allocationCreationFlow = req.body.flow as AllocationCreationFlowEnum;
      ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "allocationCreationFlow",
        allocationCreationFlow,
        AllocationCreationFlowEnum,
        { isRequired: false }
      );
    }

    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    return res
      .status(200)
      .json(
        await PortfolioService.createOrUpdatePortfolioAllocation(portfolio, allocation, allocationCreationFlow)
      );
  };

  /**
   * @description Saves the investor's personalisation preferences to his/hers portfolio,
   * and creates a real portfolio with these preferences. If any preference is missing,
   * then default values are set. The default preferences are:
   * {
   *   assetClasses: AssetClassArray,
   *   geography: "global",
   *   sectors: [],
   *   risk: RISK_CONFIG.default / RISK_CONFIG.maxLevel
   * }
   *
   * @param req The request, where
   *    req.params.id is the ID of the portfolio to be updated, and
   *    req.body.personalisationPreferences are the preferences
   * @param res The response
   * @returns updated portfolio
   */
  public static readonly submitPersonalisationPreferences = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const { personalisationPreferences } = req.body;

    if (!personalisationPreferences) {
      throw new BadRequestError("Param 'personalisationPreferences' is required", "Invalid parameter");
    }

    ["assetClasses", "geography", "sectors", "risk"].forEach((prop) => {
      if (!personalisationPreferences[prop]) {
        throw new BadRequestError(`Missing field '${prop}'`, "Invalid parameter");
      }
    });

    ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "personalisationPreferences.geography",
      personalisationPreferences.geography,
      investmentUniverseConfig.InvestmentGeographyArray
    );

    personalisationPreferences.assetClasses.forEach((key: investmentUniverseConfig.AssetClassType) =>
      ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "personalisationPreferences.assetClasses",
        key,
        investmentUniverseConfig.AssetClassArray
      )
    );

    personalisationPreferences.sectors.forEach((key: investmentUniverseConfig.InvestmentSectorType) =>
      ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "personalisationPreferences.sectors",
        key,
        investmentUniverseConfig.InvestmentSectorArray
      )
    );

    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    return res
      .status(200)
      .json(await PortfolioService.submitPersonalisationPreferences(portfolio, personalisationPreferences));
  };

  public static readonly getPortfolios = async (req: CustomRequest, res: Response): Promise<Response> => {
    const params: {
      owner?: string;
      mode?: PortfolioModeEnum;
      wealthkernelExists?: boolean;
    } = {};

    const mode = req.query.mode;
    if (mode) {
      ParamsValidationUtil.isStringParamFromAllowedValuesValid("mode", mode, PortfolioModeEnum);
      params.mode = mode as PortfolioModeEnum;
    }

    const wealthkernelExists = req.query.wealthkernelExists as string;
    if (wealthkernelExists) {
      params.wealthkernelExists = ParamsValidationUtil.isBooleanParamValid(
        "wealthkernelExists",
        wealthkernelExists
      );
    }

    params.owner = req.user.id;

    const sort = req.query.sort as string;
    const populateTickers = req.query.populateTicker
      ? ParamsValidationUtil.isBooleanParamValid("populateTicker", req.query.populateTicker as string)
      : true;

    const portfolios = await PortfolioService.getPortfolios(params, sort, populateTickers);
    const filteredPortfolios = portfolios.filter((portfolio) => portfolio.isReal);

    return res.status(200).json(
      filteredPortfolios.map((portfolio) => {
        if (populateTickers) {
          return PortfolioController._fillClientDisplayFields(req.user, portfolio, { prices: true });
        } else return portfolio;
      })
    );
  };

  public static readonly getPortfolio = async (req: CustomRequest, res: CustomResponse): Promise<Response> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res, true);
    return res.status(200).json(portfolio);
  };

  public static readonly getPortfolioPricesByTenor = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const portfolioId = req.params.id;
    ParamsValidationUtil.isObjectIdParamValid("id", portfolioId);

    const portfolioPricesByTenor = await PortfolioService.getPricesByTenor(portfolioId, req.user.currency);

    return res.status(200).json(portfolioPricesByTenor);
  };

  public static readonly getPortfolioWithReturnsByTenor = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    const portfolioWithReturns = await PortfolioService.getPortfolioWithReturnsByTenor(portfolio);

    return res.status(200).json(
      PortfolioController._fillClientDisplayFields(req.user, portfolioWithReturns, {
        prices: true
      })
    );
  };

  public static readonly getAvailableHoldings = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res, true);

    const availableHoldings = await PortfolioService.getAvailableHoldings(portfolio);

    return res.status(200).json(
      PortfolioController._fillPortfolioHoldingsClientDisplayFields(req.user, availableHoldings, {
        prices: true
      })
    );
  };

  /**
   * Withdraws an amount from the user's portfolio's available cash.
   */
  public static readonly withdraw = async (req: CustomRequest, res: Response): Promise<Response> => {
    const amount = ParamsValidationUtil.isNumericParamValid("amount", req.body.amount);
    const bankAccountId = ParamsValidationUtil.isObjectIdParamValid("bankAccountId", req.body.bankAccountId);
    await BankAccountValidationUtil.validateBankAccountIsActive(bankAccountId);

    const portfolioId = req.params.id;

    if (amount < MIN_ALLOWED_WITHDRAWAL) {
      throw new BadRequestError(`Amount cannot be less than ${MIN_ALLOWED_WITHDRAWAL}`, "Invalid parameter");
    }

    const portfolio = await TransactionService.withdraw(portfolioId, amount, { bankAccountId });
    return res.status(200).json(portfolio);
  };

  public static readonly isPortfolioImbalanced = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res, true);

    return res.status(200).json({
      isImbalanced: await PortfolioService.isPortfolioImbalanced(portfolio)
    });
  };

  /**
   * @description Initiates a rebalancing transaction for the given portfolio.
   */
  public static readonly rebalancePortfolio = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res, true);

    const rebalanceTransaction = await PortfolioService.rebalancePortfolio(
      portfolio,
      portfolio.initialHoldingsAllocation
    );

    return res.status(200).json(rebalanceTransaction);
  };

  /**
   * @description Returns the pending transactions that lead to cash flows for a portfolio
   */
  public static readonly getPendingCashFlows = async (req: CustomRequest, res: Response): Promise<Response> => {
    const portfolioId = req.params.id;

    const transactions = await TransactionService.getPendingCashflowTransactions(portfolioId);

    return res.status(200).json(transactions);
  };

  public static readonly getAssetRestrictionDetails = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);
    const assetId = req.query.assetId as investmentUniverseConfig.AssetType;

    ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "assetId",
      assetId,
      investmentUniverseConfig.AssetArrayConst
    );

    const assetRestriction = await PortfolioService.getAssetRestrictionDetails(portfolio, assetId);

    return res.status(200).json(assetRestriction);
  };

  public static readonly getRestrictedHoldings = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<Response> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    const assetRestriction = await PortfolioService.getRestrictedHoldings(portfolio);

    return res.status(200).json(assetRestriction);
  };

  public static readonly topupSavings = async (req: CustomRequest, res: CustomResponse): Promise<Response> => {
    const user: UserDocument = req.user;
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    logger.info(`Initiating savings topup for portfolio ${portfolio.id}`, {
      module: "PortfolioController",
      method: "topupSavings",
      userEmail: user.email,
      data: {
        body: req.body
      }
    });

    ParamsValidationUtil.isNumericParamValid("orderAmount", req.body.orderAmount);
    const orderAmount = parseFloat(req.body.orderAmount);
    const availableCash = portfolio?.cash?.[user.currency]?.available ?? 0;
    if (orderAmount > availableCash) {
      throw new BadRequestError(
        `You want to invest ${CurrencyUtil.formatCurrency(orderAmount, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))} buy you only have ${CurrencyUtil.formatCurrency(availableCash, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))} deposited`
      );
    }

    if (orderAmount < MIN_ALLOWED_SAVINGS_INVESTMENT) {
      logger.warn(
        `Attempted to topup savings with less than ${CurrencyUtil.formatCurrency(MIN_ALLOWED_SAVINGS_INVESTMENT, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}`,
        {
          module: "PortfolioController",
          method: "topupSavings",
          userEmail: user.email
        }
      );
      throw new BadRequestError(
        `You need to invest in savings at least ${CurrencyUtil.formatCurrency(MIN_ALLOWED_SAVINGS_INVESTMENT, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}.`
      );
    }

    const savingsProductId = req.body.savingsProductId as savingsUniverseConfig.SavingsProductType;
    ParamsValidationUtil.isSavingProductValidValid("savingsProductId", savingsProductId, { isRequired: true });

    const savingsTopupTransaction = await PortfolioService.topupSavings(portfolio, savingsProductId, orderAmount);

    return res.status(200).json(savingsTopupTransaction);
  };

  public static readonly withdrawSavings = async (req: CustomRequest, res: CustomResponse): Promise<Response> => {
    const user: UserDocument = req.user;
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    logger.info(`Initiating savings withdrawal for portfolio ${portfolio.id}`, {
      module: "PortfolioController",
      method: "withdrawSavings",
      userEmail: user.email,
      data: {
        body: req.body
      }
    });

    ParamsValidationUtil.isNumericParamValid("orderAmount", req.body.orderAmount);
    const orderAmount = parseFloat(req.body.orderAmount);

    if (orderAmount < MIN_ALLOWED_SAVINGS_WITHDRAWAL) {
      logger.warn(
        `Attempted to withdraw savings of value less than ${CurrencyUtil.formatCurrency(MIN_ALLOWED_SAVINGS_WITHDRAWAL, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}`,
        {
          module: "PortfolioController",
          method: "withdrawSavings",
          userEmail: user.email
        }
      );
      throw new BadRequestError(
        `You need to withdraw savings of value at least ${CurrencyUtil.formatCurrency(MIN_ALLOWED_SAVINGS_WITHDRAWAL, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}.`
      );
    }

    const savingsProductId = req.body.savingsProductId as savingsUniverseConfig.SavingsProductType;
    ParamsValidationUtil.isSavingProductValidValid("savingsProductId", savingsProductId, { isRequired: true });

    const savingsWithdrawalTransaction = await PortfolioService.withdrawSavings(
      portfolio,
      savingsProductId,
      orderAmount
    );

    return res.status(200).json(savingsWithdrawalTransaction);
  };

  /**
   * Enhances a portfolio object with properties useful for the clients.
   * @param user
   * @param portfolio The portfolio to fill.
   * @param fields
   * @returns An enhanced portfolio with additional display properties
   * @public
   */
  private static _fillClientDisplayFields(
    user: UserDocument,
    portfolio: PortfolioDocument,
    fields: FillClientDisplayFields = {
      prices: true
    }
  ): PortfolioDocument {
    const sanitizedPortfolio = JSON.parse(JSON.stringify(portfolio));

    if (fields.prices && portfolio.currentTicker) {
      return {
        ...sanitizedPortfolio,
        calculatedPrice: portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset)),
        currentTicker: {
          ...sanitizedPortfolio.currentTicker,
          price: portfolio.currentTicker.getPrice(user.currency)
        },
        holdings: PortfolioController._fillPortfolioHoldingsClientDisplayFields(user, portfolio.holdings, fields)
      };
    }

    return sanitizedPortfolio;
  }

  /**
   * Enhances a portfolio holdings array with properties useful for the clients.
   * @param user
   * @param holdings
   * @param fields
   * @returns An enhanced portfolio with additional display properties
   * @public
   */
  private static _fillPortfolioHoldingsClientDisplayFields(
    user: UserDocument,
    holdings: HoldingsType[],
    fields: FillClientDisplayFields = {
      prices: true
    }
  ): HoldingsType[] {
    if (fields.prices) {
      return holdings.map((holding: HoldingsType) => {
        const sanitizedHolding = JSON.parse(JSON.stringify(holding));
        return {
          ...sanitizedHolding,
          asset: {
            ...sanitizedHolding.asset,
            currentTicker: {
              ...sanitizedHolding.asset.currentTicker,
              price: holding.asset.currentTicker.getPrice(user.currency)
            }
          } as InvestmentProductDocument
        };
      });
    }
  }
}
