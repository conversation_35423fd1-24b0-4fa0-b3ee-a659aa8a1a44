import { CustomRequest } from "custom";
import { Response } from "express";
import { Readable } from "stream";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../external-services/cloudflareService";
import { AddressDocument } from "../models/Address";
import { BadRequestError } from "../models/ApiErrors";
import { RewardStatusArray, RewardStatusType } from "../models/Reward";
import { UserPopulationFieldsEnum } from "../models/User";
import InvestmentProductService from "../services/investmentProductService";
import RewardService from "../services/rewardService";
import DbUtil from "../utils/dbUtil";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import RestUtil from "../utils/restUtil";
import StatementUtil, { TradeTypeEnum } from "../utils/statementUtil";

export default class RewardController {
  public static async getRewards(req: CustomRequest, res: Response): Promise<Response> {
    const status = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "status",
      req.query.status,
      RewardStatusArray,
      {
        isRequired: false
      }
    ) as RewardStatusType;
    const hasViewedAppModal = ParamsValidationUtil.isBooleanParamValid(
      "hasViewedAppModal",
      req.query.hasViewedAppModal as string,
      { isRequired: false }
    );
    const restrictedOnly = ParamsValidationUtil.isBooleanParamValid(
      "restrictedOnly",
      req.query.restrictedOnly as string,
      {
        isRequired: false
      }
    );

    const targetUser = req.user.id as string;
    return res.status(200).json(
      await RewardService.getRewards({
        targetUser,
        hasViewedAppModal,
        status,
        restrictedOnly
      })
    );
  }

  public static async updateReward(req: CustomRequest, res: Response): Promise<Response> {
    const rewardId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const [hasViewedAppModal, accepted] = [
      ParamsValidationUtil.isBooleanParamValid("hasViewedAppModal", req.body.hasViewedAppModal as string, {
        isRequired: false
      }),
      ParamsValidationUtil.isBooleanParamValid("accepted", req.body.accepted as string, {
        isRequired: false
      })
    ];

    await RewardService.updateReward(rewardId, { hasViewedAppModal, accepted });
    return res.sendStatus(204);
  }

  public static readonly generateTradeConfirmation = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const user = req.user;

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.ADDRESSES);
    const address = user.addresses[0] as AddressDocument;

    const reward = await RestUtil.getRewardFromResponse(req, res);
    const investmentProduct = await InvestmentProductService.getInvestmentProduct(reward.asset, false);

    if (reward.status !== "Settled") {
      throw new BadRequestError("Trade confirmation can only be generated for settled rewards!");
    }

    const data = StatementUtil.generateTradeConfirmationPDF(
      user,
      address,
      reward,
      TradeTypeEnum.REWARD,
      investmentProduct
    );

    const { fileUri } = await CloudflareService.Instance.uploadObject(
      BucketsEnum.TRADE_CONFIRMATIONS,
      StatementUtil.generateTradeConfirmationFilePath(reward.id),
      Readable.from(data),
      {
        contentType: ContentTypeEnum.APPLICATION_PDF
      }
    );

    return res.status(200).json({ fileUri });
  };
}
