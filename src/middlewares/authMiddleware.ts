import { CustomRequest } from "custom";
import { NextFunction, Request, Response } from "express";
import { expressjwt, GetVerificationKey } from "express-jwt";
import jwksRsa from "jwks-rsa";
import semver from "semver";
import { captureException, setUser } from "@sentry/node";
import { ApiErrorResponse } from "../models/ApiErrorResponse";
import { PlatformType, User } from "../models/User";
import logger from "../external-services/loggerService";
import { envIsDev } from "../utils/environmentUtil";

// jwt middlewawre produces errors of the following type
// base on documentation
type JwtErrorType = {
  name: string;
  message: string;
  code: string;
  status: number;
};

type OptionsType = {
  scopeKey?: string; // user property name to check for the scope. req[requestProperty][scopeKey]
  requireAll?: boolean; // all scopes must be included. If false: at least 1
  requestProperty?: string; // request property to read in order to retrieve access token payload
};

/**
 *
 * @description This class represents a middleware for authorization with jwt bearer tokens usage.
 * In order to achieve a proper authorization the middlewares of this class should be called in following order:
 *
 * AuthMiddleware.validateJwt,
 * AuthMiddleware.setUserRoles,
 * AuthMiddleware.canAccessScope("<scope to access api>"),
 * AuthMiddleware.findUserByAuth0Id,
 * AuthMiddleware.errorHandler,
 *
 * @link https://www.npmjs.com/package/express-jwt-scope
 *
 */
export default class AuthMiddleware {
  private static readonly REQ_PROP_ACCESS_TOKEN = "accessTokenPayload";
  private static readonly REQ_PROP_IDENTITY_TOKEN = "identityTokenPayload";

  /**
   * @description Authentication middleware. When used, the access Token must exist and be verified against
   * the Auth0 JSON Web Key Set.
   *
   * This middleware does the following checks:
   *  - Check that authorization header exists and has valid bearer format
   *  - Validates access token format
   *  - Validates access token signature, by using public key of provider (uri provided)
   *  - Validates tha access token has not expired
   *  - Validates the audience
   *  - Validates the issuer
   *
   * After validation access token payload is injected to request in the property defined (in 'requestProperty' key)
   *
   */
  public static readonly validateAccessToken = (req: Request, res: Response, next: NextFunction) => {
    const authDomain = AuthMiddleware._getAuthDomain(req);

    return expressjwt({
      // Dynamically provide a signing key based on the kid in the header and
      // the signing keys provided by the JWKS endpoint.
      secret: jwksRsa.expressJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        // When running locally, we expose the JWKS from our API
        jwksUri: `${
          envIsDev() ? process.env.DOMAIN_URL : `https://${AuthMiddleware._getAuthDomain(req)}`
        }/.well-known/jwks.json`
      }) as GetVerificationKey,
      //  Validate the audience and the issuer.
      audience: process.env.AUTH0_AUDIENCES?.split(","),
      issuer: `https://${authDomain}/`,
      algorithms: ["RS256"],
      requestProperty: AuthMiddleware.REQ_PROP_ACCESS_TOKEN
    })(req, res, next);
  };

  /**
   * @description Authentication middleware for the validation of the Auth0 identity JWT.
   * When used, the identity Token must exist and be verified against the Auth0 JSON Web Key Set.
   *
   * This middleware does the following checks:
   *  - Check that identity field header exists and has valid bearer format
   *  - Validates identity token format
   *  - Validates identity token signature, by using public key of provider (uri provided)
   *  - Validates tha identity token has not expired
   *  - Validates the audience (has to be the application client id)
   *  - Validates the issuer
   *
   * After validation identity token payload is injected to request in the property defined (in 'requestProperty' key)
   *
   */
  public static readonly validateIdentityToken = (req: Request, res: Response, next: NextFunction) => {
    const AUDIENCE_CONFIG: Record<"ios" | "android", string> = {
      ios: process.env.AUTH0_IOS_CLIENT_ID,
      android: process.env.AUTH0_ANDROID_CLIENT_ID
    };
    const platform = (req.headers.platform || req.headers.Platform) as "ios" | "android";
    const authDomain = AuthMiddleware._getAuthDomain(req);

    return expressjwt({
      // Dynamically provide a signing key based on the kid in the header and
      // the signing keys provided by the JWKS endpoint.
      secret: jwksRsa.expressJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: `https://${authDomain}/.well-known/jwks.json`
      }) as GetVerificationKey,
      //  Validate the audience and the issuer.
      audience: AUDIENCE_CONFIG[platform],
      issuer: `https://${authDomain}/`,
      algorithms: ["RS256"],
      getToken: function (req) {
        const identityHeader = (req.headers.identity as string) || (req.headers.Identity as string);
        if (identityHeader && identityHeader.split(" ")[0] === "Bearer") {
          return identityHeader.split(" ")[1];
        }
        return null;
      },
      requestProperty: AuthMiddleware.REQ_PROP_IDENTITY_TOKEN
    })(req, res, next);
  };

  public static readonly setUserCreatedAt = (req: CustomRequest, res: Response, next: NextFunction) => {
    const createdAt = (req.identityTokenPayload as any)[`${process.env.AUTH0_RETURN_TO}/createdAt`];
    req.identityTokenPayload.createdAt = createdAt ?? new Date();
    next();
  };

  /**
   * @description This middleware reads the platform of the request from headers and copies them in the defined
   * property 'platform'.
   * @param req
   * @param res
   * @param next
   */
  public static readonly setPlatform = (req: CustomRequest, res: Response, next: NextFunction) => {
    req.platform = req.headers["platform"] as PlatformType;
    next();
  };

  /**
   * @description This middleware reads the roles of user (if any) from the default property of provider
   * and copies them in the defined property 'roles' of access token payload model.
   * @param req
   * @param res
   * @param next
   */
  public static readonly setUserRoles = (req: CustomRequest, res: Response, next: NextFunction) => {
    const roles = (req.accessTokenPayload as any)[`${process.env.AUTH0_RETURN_TO}/roles`];
    req.accessTokenPayload.roles = roles ?? [];
    next();
  };

  /**
   * @description This middleware checks if required scopes to access the api exist
   * in scopes of access token payload.
   * @param scope
   * @returns
   */
  public static readonly canAccessScope = (scope: string) =>
    AuthMiddleware._jwtScope(scope, {
      requestProperty: AuthMiddleware.REQ_PROP_ACCESS_TOKEN
    });

  /**
   * @description This middleware handles errors occured and transforms them into json responses.
   * @param err
   * @param req
   * @param res
   * @param next
   * @returns
   */
  public static readonly handleErrors = (
    err: JwtErrorType,
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ) => {
    const { status, code: message, message: description } = err;
    return res.status(err.status ?? 500).json(new ApiErrorResponse({ status, message, description }));
  };

  /**
   * @description This middleware searches the database for a user with the resolved id from the request header 'external-user-id'.
   * If user exists is being injected to the request at property 'user' otherwise an error is being produced.
   * @param req
   * @param res
   * @param next
   */
  public static readonly findExternalUserById = async (req: CustomRequest, res: Response, next: NextFunction) => {
    const errorNext = () => {
      return next({
        code: "invalid_token",
        status: 401,
        name: "UserError",
        message: "User not found"
      } as JwtErrorType);
    };
    try {
      const userId = req.headers["external-user-id"];
      const user = await User.findById(userId);
      if (user) {
        setUser({ email: user.email });
        req.user = user;
        next();
      } else {
        errorNext();
      }
    } catch (err) {
      captureException(err);
      errorNext();
    }
  };

  /**
   * @description This middleware searches the database for a user with the resolved email from the identity request header.
   * If user exists is being injected to the request at property 'user' otherwise an error is being produced.
   * @param req
   * @param res
   * @param next
   */
  public static readonly findUserUsingIdToken = async (req: CustomRequest, res: Response, next: NextFunction) => {
    const errorNext = () => {
      return next({
        code: "invalid_token",
        status: 401,
        name: "UserError",
        message: "User not found"
      } as JwtErrorType);
    };

    try {
      const email = req.identityTokenPayload.email;
      const user = await User.findOne({ email });
      if (user) {
        setUser({ email: user.email });
        req.user = user;
        next();
      } else {
        errorNext();
      }
    } catch (err) {
      captureException(err);
      logger.error("Error when retrieving user", {
        module: "AuthMiddleware",
        method: "findUserUsingIdToken",
        userEmail: req.identityTokenPayload?.email ?? "",
        data: {
          error: err
        }
      });
      errorNext();
    }
  };

  private static _getAuthDomain(req: Request): string {
    const version = req.headers.version as string;

    const AUTH_DOMAIN_CONFIG: Record<"ios" | "android" | "web", string> = {
      ios: process.env.AUTH0_IOS_DOMAIN,
      android:
        version && semver.gt(version, "1.1.14") ? process.env.AUTH0_ANDROID_DOMAIN : process.env.AUTH0_DOMAIN,
      web: process.env.AUTH0_DOMAIN
    };
    let platform: "ios" | "android" | "web" = (req.headers.platform || req.headers.Platform) as "ios" | "android";
    if (!platform) {
      platform = "web";
    }

    return AUTH_DOMAIN_CONFIG[platform];
  }

  /**
   * @param allowScopes Allowed or required scopes. String must be 'space' separated
   * @param options
   * @returns {Function}
   */
  private static readonly _jwtScope = (
    allowScopes: string | string[],
    options: OptionsType
  ): ((req: any, res: Response, next: NextFunction) => void) => {
    if (!allowScopes) {
      throw new Error("Parameter allowScopes must be a string or an array of strings.");
    }
    const optionsToUse = {
      scopeKey: "scope",
      requireAll: false,
      requestProperty: AuthMiddleware.REQ_PROP_ACCESS_TOKEN,
      ...options
    };
    const requestProperty = optionsToUse.requestProperty;
    const scopes = Array.isArray(allowScopes) ? allowScopes : allowScopes.split(" ");

    return (req: any, res: Response, next: NextFunction) => {
      const errorNext = () => {
        return next({
          code: "insufficient_scope",
          status: 403,
          name: "ScopeError",
          message: "Insufficient scope"
        } as JwtErrorType);
      };

      if (scopes.length === 0) {
        return next();
      }

      if (!req[requestProperty]) {
        return errorNext();
      }

      let userScopes: string[] = [];
      const scopeKey = optionsToUse.scopeKey;

      //  Allow 'space' seperated string value or array value
      if (typeof req[requestProperty][scopeKey] === "string") {
        userScopes = req[requestProperty][scopeKey].split(" ");
      } else if (Array.isArray(req[requestProperty][scopeKey])) {
        userScopes = req[requestProperty][scopeKey];
      } else {
        return errorNext();
      }

      const isAllowed = optionsToUse.requireAll
        ? scopes.every((scope) => userScopes.includes(scope))
        : scopes.some((scope) => userScopes.includes(scope));

      return isAllowed ? next() : errorNext();
    };
  };
}
