import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { RevertRewardTransaction } from "../models/Transaction";
import ProviderService from "../services/providerService";
import { UserDocument } from "../models/User";
import { CompanyEntityEnum } from "@wealthyhood/shared-configs/dist/entities";
import { InternalTransferType } from "../external-services/wealthkernelService";
import { PortfolioDocument } from "../models/Portfolio";
import { TransactionService } from "../services/transactionService";

class ProcessStagnantRevertRewardTransactionsScriptRunner extends ScriptRunner {
  scriptName = "process-stagnant-revert-reward-transactions";

  async processFn(): Promise<void> {
    logger.info("Processing stagnant revert reward transactions...", {
      module: `script:${this.scriptName}`
    });

    try {
      const stagnantTransactions = await RevertRewardTransaction.find({
        status: "PendingWealthkernelCharge"
      }).populate("owner portfolio");

      logger.info(`✅ Fetched ${stagnantTransactions.length} stagnant revert reward transactions`, {
        module: `script:${this.scriptName}`,
        data: {
          stagnantTransactions
        }
      });

      for (const transaction of stagnantTransactions) {
        logger.info(`Processing transaction with id ${transaction.id}`, {
          module: `script:${this.scriptName}`,
          data: {
            transaction
          }
        });

        try {
          const user = transaction.owner as UserDocument;
          const portfolio = transaction.portfolio as PortfolioDocument;
          const brokerageService = ProviderService.getBrokerageService(user.companyEntity);
          const isUkUser = user.companyEntity === CompanyEntityEnum.WEALTHYHOOD_UK;
          const params = isUkUser
            ? { clientReference: transaction.id }
            : { fromPortfolioId: portfolio.providers.wealthkernel.id };
          const wealthkernelCharges = await brokerageService.listCharges(params);

          if (isUkUser && (!wealthkernelCharges || wealthkernelCharges.length !== 1)) {
            logger.error(
              `RevertRewardTransaction with id ${transaction.id} does not have the expected wealthkernel charges`,
              {
                module: `script:${this.scriptName}`,
                data: {
                  wealthkernelCharges
                }
              }
            );
          } else if (wealthkernelCharges && wealthkernelCharges.length > 0) {
            const wealthkernelCharge = isUkUser
              ? wealthkernelCharges[0]
              : wealthkernelCharges.find((charge) => {
                  const internalTransfer = charge as InternalTransferType;
                  return internalTransfer.consideration.amount === transaction.consideration.amount / 100;
                });

            if (!wealthkernelCharge) {
              logger.error(`No expected internal transfer was found for transaction with id ${transaction.id}`, {
                module: `script:${this.scriptName}`,
                data: {
                  wealthkernelCharges
                }
              });
            } else {
              logger.info(`Successfully found expected charge for transaction with id ${transaction.id}`, {
                module: `script:${this.scriptName}`,
                data: {
                  wealthkernelCharge
                }
              });

              await RevertRewardTransaction.updateOne(
                { _id: transaction.id },
                {
                  "providers.wealthkernel.id": wealthkernelCharge.id,
                  "providers.wealthkernel.submittedAt": new Date(Date.now())
                }
              );

              await TransactionService.updateRevertRewardStatusByWkChargeId(
                wealthkernelCharge.id,
                wealthkernelCharge.status
              );

              logger.info(`Successfully updated revert reward transaction with id ${transaction.id}`, {
                module: `script:${this.scriptName}`,
                data: {
                  transaction
                }
              });
            }
          }
        } catch (error) {
          logger.error(`Failed to process transaction with id  ${transaction.id}`, {
            module: `script:${this.scriptName}`,
            data: {
              message: error.message,
              error
            }
          });
        }
      }

      logger.info("✅ Stagnant revert reward transactions have been successfully updated!", {
        module: `script:${this.scriptName}`
      });
    } catch (error) {
      logger.error("Failed to process stagnant revert reward transactions", {
        module: `script:${this.scriptName}`,
        data: {
          message: error.message,
          error
        }
      });
    }
  }
}

new ProcessStagnantRevertRewardTransactionsScriptRunner().run();
