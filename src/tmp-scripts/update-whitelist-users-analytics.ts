import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import analytics, { UserTraitType } from "../external-services/segmentAnalyticsService";
import { whitelistConfig, countriesConfig } from "@wealthyhood/shared-configs";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

const userCutoff = "<EMAIL>"; //last email of previous batch

/**
 * Script to update all whitelist users on Mixpanel and MailChimp
 *
 * This script:
 * 1. Gets all whitelist emails from shared config (whitelistConfig.WHITELISTED_EU_EMAILS)
 * 2. Finds users with those emails in the database
 * 3. Updates them on Mixpanel using analytics.identify with joinedWithCode: "whitelisted"
 * 4. Updates them on MailChimp using analytics.identify with waiting: "Given Access"
 *
 */
class UpdateWhitelistUsersAnalytics extends <PERSON>riptRunner {
  scriptName = "update-whitelist-users-analytics";

  async processFn(): Promise<void> {
    console.log("🚀 Script processFn started");
    logger.info("Starting update of whitelist users on Mixpanel/MailChimp...", {
      module: `script:${this.scriptName}`
    });

    // Get all whitelist emails from shared config
    const whitelistEmails = whitelistConfig.WHITELISTED_EU_EMAILS;

    const indexOfCutoff = whitelistEmails.indexOf(userCutoff);
    const emailsToProcess = whitelistEmails.slice(indexOfCutoff + 1);

    logger.info(`Found ${emailsToProcess.length} whitelist emails to process`, {
      module: `script:${this.scriptName}`,
      data: {
        emailCount: whitelistEmails.length
      }
    });

    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    const mailchimpTraits: UserTraitType = {
      waiting: "Given Access"
    };
    const mixpanelTraits: UserTraitType = {
      joinedWithCode: "whitelisted"
    };

    // Find all users who have a whitelist email
    await User.find({
      email: { $in: emailsToProcess }
    })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (users) => {
          users.forEach((user) => {
            try {
              analytics.identify(user, mailchimpTraits, { All: false, MailChimp: true });
              analytics.identify(user, mixpanelTraits, { All: false, Mixpanel: true });

              successCount++;
              processedCount++;

              logger.info(`Updated user ${user.email} (${user._id})`, {
                module: `script:${this.scriptName}`,
                data: {
                  userId: user._id,
                  email: user.email
                }
              });
            } catch (err) {
              errorCount++;
              processedCount++;

              logger.error(`Failed to update user ${user.email} (${user._id})`, {
                module: `script:${this.scriptName}`,
                data: {
                  userId: user._id,
                  email: user.email,
                  error: err
                }
              });
            }
          });

          // Add delay so that the events queue gets cleared before proceeding with the next batch
          await delay(3000);
        },
        { batchSize: 10 }
      );

    logger.info("✅ Finished updating whitelist users on Mixpanel/MailChimp!", {
      module: `script:${this.scriptName}`,
      data: {
        processed: processedCount,
        success: successCount,
        errors: errorCount
      }
    });

    // Add delay so that we make sure the events get sent before they method exits
    await delay(10000);
  }
}

new UpdateWhitelistUsersAnalytics().run();
