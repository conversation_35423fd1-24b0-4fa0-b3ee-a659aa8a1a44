import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User, UserDocument } from "../models/User";
import ProviderService from "../services/providerService";
import { PassportDetails } from "../services/sumsubBasedKycService";
import { titleCaseString } from "../utils/stringUtil";
import { FormattedPassportDetails } from "../services/kycOperationService";
import { KycOperation } from "../models/KycOperation";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

class BackfillUserIdDocuments extends ScriptRunner {
  scriptName = "backfill-user-id-documents";

  async processFn(): Promise<void> {
    console.log("🚀 Script processFn started");
    logger.info("Starting backfilling user id documents...", {
      module: `script:${this.scriptName}`
    });

    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    const usersWithUnretrievedDocument: { id: string; email: string }[] = [];

    await KycOperation.find({
      "providers.sumsub.status": "completed"
    })
      .populate("owner")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (kycOperations) => {
          const updatePromises = kycOperations.map(async (kycOperation) => {
            processedCount++;

            const user = kycOperation.owner as UserDocument;

            if (!user) {
              return;
            }

            // In case we want to rerun it in the future
            if (user.idDocument?.countryCode || user.idDocument?.documentType || user.idDocument?.value) {
              return;
            }

            try {
              const applicant = await ProviderService.getKycService(user.companyEntity).retrieveApplicant(user.id);

              if (
                !applicant ||
                !applicant.passportDetails ||
                !applicant.passportDetails.idDocs ||
                applicant.passportDetails.idDocs.length === 0
              ) {
                usersWithUnretrievedDocument.push({ id: user.id, email: user.email });
                return;
              }

              const formattedPassportDetails = this._formatExtractedPassportDetails(applicant.passportDetails);

              await User.findOneAndUpdate(
                {
                  _id: user.id
                },
                {
                  "idDocument.countryCode": formattedPassportDetails.documentCountryCode,
                  "idDocument.documentType": formattedPassportDetails.documentType,
                  "idDocument.value": formattedPassportDetails.documentNumber
                }
              );

              successCount++;

              logger.info(`Updated user ${user.email} (${user._id})`, {
                module: `script:${this.scriptName}`,
                data: {
                  userId: user._id,
                  email: user.email
                }
              });
            } catch (err) {
              errorCount++;

              logger.error(`Failed to update user ${user.id} (${user._id})`, {
                module: `script:${this.scriptName}`,
                data: {
                  userId: user._id,
                  email: user.email,
                  error: err
                }
              });
            }
          });

          await Promise.all(updatePromises);

          // Add small delay so that we don't hit sumsub api limits
          await delay(1000);
        },
        { batchSize: 10 }
      );

    logger.info(`Users we could not retrieve the id documents from`, {
      module: `script:${this.scriptName}`,
      data: {
        usersWithUnretrievedDocument: usersWithUnretrievedDocument.map((user) => {
          return {
            id: user.id,
            email: user.email
          };
        })
      }
    });

    logger.info("✅ Finished backfilling user id documents!", {
      module: `script:${this.scriptName}`,
      data: {
        processed: processedCount,
        success: successCount,
        error: errorCount,
        noIdDocumentRetrieved: usersWithUnretrievedDocument.length,
        usersWithUnretrievedDocument
      }
    });
  }

  private _formatExtractedPassportDetails(passportDetails: PassportDetails): FormattedPassportDetails {
    const idDocument = passportDetails.idDocs?.[0];
    return {
      firstName: titleCaseString(passportDetails.firstName),
      lastName: titleCaseString(passportDetails.lastName),
      dateOfBirth: passportDetails.dateOfBirth,
      nationality: passportDetails.nationality,
      documentNumber: idDocument?.number,
      documentType: idDocument?.idDocType,
      documentCountryCode: idDocument?.country
    };
  }
}

new BackfillUserIdDocuments().run();
