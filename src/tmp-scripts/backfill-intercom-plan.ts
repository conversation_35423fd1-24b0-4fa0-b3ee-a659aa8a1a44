import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import analytics from "../external-services/segmentAnalyticsService";
import { User, UserDocument } from "../models/User";
import { Subscription } from "../models/Subscription";
import { plansConfig } from "@wealthyhood/shared-configs/dist";

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

class BackfillIntercomPlanScriptRunner extends ScriptRunner {
  scriptName = "backfill-intercom-plan";

  async processFn(): Promise<void> {
    logger.info("Backfilling Intercom plan for users with active plan...", {
      module: `script:${this.scriptName}`
    });

    let processed = 0;
    let success = 0;
    let failed = 0;

    await Subscription.find({ active: true })
      .populate("owner")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (subscription) => {
        const user = subscription.owner as UserDocument;
        const planCongig = plansConfig.getPriceConfig(user.companyEntity)[subscription.price];
        try {
          analytics.identify(
            user,
            { plan: planCongig.plan, planRecurrence: planCongig.recurrence },
            { All: false, Intercom: true }
          );

          success++;
        } catch (err) {
          failed++;
          logger.error("Failed to backfill Intercom plan for user", {
            module: `script:${this.scriptName}`,
            data: { userId: user._id, email: user.email, error: err }
          });
        } finally {
          processed++;
          if (processed % 20 === 0) {
            logger.info(`Processed ${processed} users so far...`, {
              module: `script:${this.scriptName}`
            });
            // Small pause to avoid hitting rate limits
            await delay(5000);
          }
        }
      });

    // Add delay so that we make sure the last events get sent before the method exits
    await delay(5000);

    logger.info("Backfill of Intercom plan complete", {
      module: `script:${this.scriptName}`,
      data: { processed, success, failed }
    });
  }
}

new BackfillIntercomPlanScriptRunner().run();
