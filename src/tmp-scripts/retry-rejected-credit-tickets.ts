import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { CreditTicket } from "../models/CreditTicket";
import DbUtil from "../utils/dbUtil";
import mongoose from "mongoose";
import { DepositCashTransactionDocument } from "../models/Transaction";
import { WealthkernelService } from "../external-services/wealthkernelService";
import { UserDocument } from "../models/User";

class RetryRejectedCreditTickets extends ScriptRunner {
  scriptName = "retry-rejected-credit-tickets";

  async processFn(): Promise<void> {
    logger.info("Finding rejected credit tickets...", {
      module: `script:${this.scriptName}`
    });

    const rejectedCreditTickets = await CreditTicket.aggregate([
      {
        $match: {
          status: "Rejected",
          createdAt: {
            $gte: new Date("2025-10-02T00:00:00.000+00:00")
          }
        }
      },
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "linkedCreditTicket",
          as: "transaction"
        }
      },
      {
        $match: {
          "transaction.providers.wealthkernel.status": {
            $ne: "Settled"
          }
        }
      }
    ]);

    logger.info(`Found ${rejectedCreditTickets.length} rejected credit tickets`, {
      module: `script:${this.scriptName}`
    });

    for (const rejectedCreditTicket of rejectedCreditTickets) {
      const creditTicket = await CreditTicket.findById(rejectedCreditTicket._id).populate("linkedDeposit owner");
      const deposit = creditTicket?.linkedDeposit as DepositCashTransactionDocument;
      const owner = creditTicket?.owner as UserDocument;

      if (["<EMAIL>"].includes(owner.email)) {
        continue;
      }

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        logger.info(`Retrying rejected credit ticket ${rejectedCreditTicket._id}...`, {
          module: `script:${this.scriptName}`
        });

        await creditTicket?.updateOne({ status: "Pending" }, { session });

        await deposit?.updateOne({ $unset: { providers: true } }, { session });

        await WealthkernelService.EUInstance.cancelDeposit(deposit.providers.wealthkernel.id);
      });

      logger.info(`Retried rejected credit ticket ${rejectedCreditTicket._id}...`, {
        module: `script:${this.scriptName}`
      });
    }
  }
}

new RetryRejectedCreditTickets().run();
