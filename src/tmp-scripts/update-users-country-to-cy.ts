import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import UserService from "../services/userService";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

const emailsToUpdate = ["<EMAIL>", "<EMAIL>"];

class UpdateUsersCountryToCY extends ScriptRunner {
  scriptName = "update-users-country-to-cy";

  async processFn(): Promise<void> {
    console.log("🚀 Script processFn started");
    logger.info("Starting update of user country to CY...", {
      module: `script:${this.scriptName}`
    });

    let processedCount = 0;

    const users = await User.find({
      email: { $in: emailsToUpdate }
    });

    for (const user of users) {
      await UserService.submitResidencyCountry(user.id, "CY");
      processedCount++;
    }

    logger.info("✅ Finished updating user country to CY!", {
      module: `script:${this.scriptName}`,
      data: {
        processed: processedCount
      }
    });

    // Add delay so that we make sure the events get sent before they method exits
    await delay(10000);
  }
}

new UpdateUsersCountryToCY().run();
