import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DepositMethodEnum } from "../types/transactions";
import { DepositCashTransaction } from "../models/Transaction";
import ProviderService from "../services/providerService";
import { UserDocument } from "../models/User";

class CreateGRExpectationsForAllPendingBankTransfersScriptRunner extends ScriptRunner {
  scriptName = "create-gr-expectations-for-all-pending-bank-transfers";

  async processFn(): Promise<void> {
    logger.info("Creating GR expectations for all pending bank transfers...", {
      module: `script:${this.scriptName}`
    });

    const pendingBankTransfers = await DepositCashTransaction.find({
      status: "Pending",
      depositMethod: DepositMethodEnum.BANK_TRANSFER,
      "providers.wealthkernel.status": "Created",
      "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed"
    });

    for (const deposit of pendingBankTransfers) {
      logger.info(`Going to create GR expectations for deposit ${deposit.id}...`, {
        module: `script:${this.scriptName}`,
        data: {
          depositId: deposit.id,
          amount: deposit.consideration.amount,
          oldDepositExpectation: deposit.providers.wealthkernel.id
        }
      });

      await deposit.populate([{ path: "portfolio" }, { path: "bankAccount" }, { path: "owner" }]);

      const user = deposit.owner as UserDocument;
      const wkDepositResponse = await ProviderService.getBrokerageService(user.companyEntity).createDeposit(
        deposit,
        { overrideReference: "GR" }
      );

      const wkDepositId = wkDepositResponse.id;

      const updatedDeposit = await DepositCashTransaction.findByIdAndUpdate(
        deposit.id,
        {
          "providers.wealthkernel": { id: wkDepositId, status: "Created" }
        },
        { new: true }
      );

      logger.info(`Finished creating GR expectations for deposit ${deposit.id}...`, {
        module: `script:${this.scriptName}`,
        data: {
          depositId: deposit.id,
          amount: deposit.consideration.amount,
          oldDepositExpectation: deposit.providers.wealthkernel.id,
          newDepositExpectation: updatedDeposit.providers.wealthkernel.id
        }
      });
    }

    logger.info("Finished creating GR expectations for all pending bank transfers!", {
      module: `script:${this.scriptName}`
    });
  }
}

new CreateGRExpectationsForAllPendingBankTransfersScriptRunner().run();
