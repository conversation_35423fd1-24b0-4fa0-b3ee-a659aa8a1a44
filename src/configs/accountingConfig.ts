import { AccountingClientSegment, LedgerAccounts } from "../types/accounting";

export const clientSegmentToLedgerAccountMapping: Record<AccountingClientSegment, LedgerAccounts> = {
  [AccountingClientSegment.DOMESTIC_GR]: LedgerAccounts.CLIENT_DOMESTIC,
  [AccountingClientSegment.EU_EEA_EXCLUDING_GR]: LedgerAccounts.CLIENT_EU_EEA,
  [AccountingClientSegment.REST_OF_WORLD]: LedgerAccounts.CLIENT_INTERNATIONAL
};

// Cash accounts to WealthKernel portfolio mapping for cash reconciliation
export const CASH_ACCOUNTS_WK_MAPPING: Record<LedgerAccounts, string | undefined> = {
  [LedgerAccounts.CLIENT_DOMESTIC]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.CLIENT_EU_EEA]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.CLIENT_INTERNATIONAL]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS]: undefined, // aggregated balance (multiple portfolios)
  [LedgerAccounts.INTERMEDIARY_DEPOSITS_1]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.INTERMEDIARY_DEPOSITS_2]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.INTERMEDIARY_WITHDRAWALS]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.ASSETS_ACTIVE]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.ASSETS_PASSIVE]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.PAYABLES_TO_BROKER]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.BROKER_FEE_EXPENSE]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.BONUS_ACCOUNT]: undefined, // Not used for cash reconciliation
  [LedgerAccounts.BONUS_EXPENSE]: undefined, // Not used for cash reconciliation (FIXME: add to cash reconciliation)
  [LedgerAccounts.CUSTODY_FEES_WH]: process.env.WEALTHKERNEL_WH_CUSTODY_FEES_PORTFOLIO_ID_EU as string,
  [LedgerAccounts.COMMISSION_FEES_WH]: process.env.WEALTHKERNEL_WH_COMMISSION_FEES_PORTFOLIO_ID_EU as string,
  [LedgerAccounts.MMF_DIVIDEND_FEES_WH]: process.env
    .WEALTHKERNEL_WH_DIVIDEND_COMMISSION_FEES_PORTFOLIO_ID_EU as string
};

export const ledgerAccountNames: Record<LedgerAccounts, string> = {
  [LedgerAccounts.CLIENT_DOMESTIC]: "Πελάτης Εσωτερικού",
  [LedgerAccounts.CLIENT_EU_EEA]: "Πελάτης Ζώνης Ευρώ",
  [LedgerAccounts.CLIENT_INTERNATIONAL]: "Πελάτης Λοιπές Χώρες",
  [LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS]: "Clients Accounts (Omnibus)",
  [LedgerAccounts.INTERMEDIARY_DEPOSITS_1]: "Intermediary Deposits #1",
  [LedgerAccounts.INTERMEDIARY_DEPOSITS_2]: "Intermediary Deposits #2",
  [LedgerAccounts.INTERMEDIARY_WITHDRAWALS]: "Intermediary Withdrawals",
  [LedgerAccounts.ASSETS_ACTIVE]: "Assets – Active",
  [LedgerAccounts.ASSETS_PASSIVE]: "Assets – Passive",
  [LedgerAccounts.PAYABLES_TO_BROKER]: "Payables to Broker",
  [LedgerAccounts.BROKER_FEE_EXPENSE]: "Broker Fee Expense",
  [LedgerAccounts.BONUS_ACCOUNT]: "Bonus Account",
  [LedgerAccounts.BONUS_EXPENSE]: "Bonus Expense",
  [LedgerAccounts.CUSTODY_FEES_WH]: "Custody Fees Wealthyhood",
  [LedgerAccounts.COMMISSION_FEES_WH]: "Wealthyhood Commission Fees",
  [LedgerAccounts.MMF_DIVIDEND_FEES_WH]: "MMF Dividend Fees Wealthyhood"
};
