import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildBankAccount, buildDepositCashTransaction, buildUser } from "../../tests/utils/generateModels";
import request from "supertest";
import app from "../../app";
import {
  WealthkernelService,
  BANK_ACCOUNT_NAME_CHAR_LIMIT,
  CurrencyEnum
} from "../../external-services/wealthkernelService";
import { faker } from "@faker-js/faker";
import { BankAccount, BankAccountDocument } from "../../models/BankAccount";
import { UserDocument } from "../../models/User";
import {
  buildWealthkernelBankAccountResponse,
  buildWealthkernelDepositResponse
} from "../../tests/utils/generateWealthkernel";
import DateUtil from "../../utils/dateUtil";
import { DepositMethodEnum } from "../../types/transactions";
import { DepositCashTransaction, DepositCashTransactionDocument } from "../../models/Transaction";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";

const olderDate = new Date("2022-04-03");
const DATE = new Date("2022-08-31T11:00:00Z");

describe("AdminBankAccountRoutes", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("AdminBankAccountRoutes"));
  afterEach(async () => {
    jest.restoreAllMocks();
    await clearDb();
  });
  afterAll(async () => await closeDb());

  describe("/bank-accounts", () => {
    describe("POST /bank-accounts/create-wealthkernel", () => {
      describe("when we have a single bank account that already has a WK ID", () => {
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "addBankAccount");
          jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts");
        });
        it("should return 204, not hit Wealthkernel for any operations and not edit our bank account", async () => {
          const bankAccountWithWKId: BankAccountDocument = await buildBankAccount({
            providers: { wealthkernel: { id: faker.string.uuid() } }
          });
          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(bankAccountWithWKId);
        });
      });

      describe("when we have a single bank account for a user that does not have a party ID", () => {
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "addBankAccount");
          jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts");
        });
        it("should return 204, not hit Wealthkernel for any operations and not edit our bank account", async () => {
          const user: UserDocument = await buildUser();
          const bankAccountWithoutWKId = user.bankAccounts[0];

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(bankAccountWithoutWKId);
        });
      });

      describe("when we have a single bank account which was created less than 10 minutes ago", () => {
        const wealthkernelBankAccount = buildWealthkernelBankAccountResponse({
          id: faker.string.uuid()
        });
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(wealthkernelBankAccount);
          jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        });
        it("should return 204, not hit Wealthkernel for any operations and not edit our bank account", async () => {
          await buildUser(
            {
              providers: { wealthkernel: { id: faker.string.uuid() } }
            },
            false
          );
          const bankAccount = await buildBankAccount({ createdAt: new Date() });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(bankAccount);
        });
      });

      describe("when we have a single bank account that does not have WK as an active provider", () => {
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
          jest.spyOn(WealthkernelService.UKInstance, "addBankAccount");
        });

        it("should return 204, not hit Wealthkernel for any operations and not edit our bank account", async () => {
          const user: UserDocument = await buildUser(
            {
              providers: { wealthkernel: { id: faker.string.uuid() } }
            },
            false
          );
          const bankAccount = await buildBankAccount({
            owner: user.id,
            createdAt: olderDate,
            activeProviders: []
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(bankAccount);
        });
      });

      describe("when we have a single bank account that has been already created in WK", () => {
        const wealthkernelBankAccount = buildWealthkernelBankAccountResponse();

        beforeEach(async () => {
          jest
            .spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts")
            .mockResolvedValue([wealthkernelBankAccount]);
        });
        it("should return 204 and update our bank account to have the WK bank account ID", async () => {
          const user: UserDocument = await buildUser(
            {
              providers: { wealthkernel: { id: wealthkernelBankAccount.partyId } }
            },
            false
          );
          await buildBankAccount({ owner: user.id, createdAt: olderDate, providers: {} });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          const newBankAccounts = await BankAccount.find({ owner: user.id });
          expect(newBankAccounts.length).toEqual(1);
          expect(newBankAccounts[0]?.providers?.wealthkernel?.id).toEqual(wealthkernelBankAccount.id);
        });
      });

      describe("when we have a single bank account that has not been created in WK", () => {
        const wealthkernelBankAccount = buildWealthkernelBankAccountResponse({
          id: faker.string.uuid()
        });
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(wealthkernelBankAccount);
          jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        });
        describe("and the full name of the owner is less than 50 characters", () => {
          it("should return 204, create a new WK account, update our bank account to have a reference to it and pass the fullname of the user as bankAccount name", async () => {
            const user: UserDocument = await buildUser(
              {
                providers: { wealthkernel: { id: faker.string.uuid() } }
              },
              false
            );
            await buildBankAccount({ owner: user.id, createdAt: olderDate, providers: {} });

            await user.populate("bankAccounts");
            expect(user.bankAccounts[0].name.length).toBeLessThan(BANK_ACCOUNT_NAME_CHAR_LIMIT);

            const response = await hitCreateWealthkernel();
            expect(response.status).toEqual(204);

            expect(WealthkernelService.UKInstance.addBankAccount).toBeCalledWith({
              partyId: user.providers.wealthkernel.id,
              name: user.fullName,
              accountNumber: user.bankAccounts[0].number,
              sortCode: user.bankAccounts[0].sortCode,
              currency: CurrencyEnum.GBP,
              countryCode: "GB"
            });
            const newBankAccounts = await BankAccount.find({ owner: user.id });
            expect(newBankAccounts.length).toEqual(1);
            expect(newBankAccounts[0].toObject()).toEqual(
              expect.objectContaining({
                providers: {
                  wealthkernel: {
                    id: wealthkernelBankAccount.id,
                    status: "Pending"
                  }
                }
              })
            );
          });
        });

        describe("and the full name of the owner is over the Wealthkernel limit of 50 characters", () => {
          it("should return 204, create a new WK account with the first 50 characters of the name and update our bank account to have a reference to it", async () => {
            const user: UserDocument = await buildUser(
              {
                firstName: faker.string.sample(BANK_ACCOUNT_NAME_CHAR_LIMIT / 2 + 10),
                lastName: faker.string.sample(BANK_ACCOUNT_NAME_CHAR_LIMIT / 2 + 10),
                providers: { wealthkernel: { id: faker.string.uuid() } }
              },

              false
            );
            await buildBankAccount({
              owner: user.id,
              createdAt: olderDate,
              providers: {}
            });

            await user.populate("bankAccounts");

            expect(user.fullName.length).toBeGreaterThan(BANK_ACCOUNT_NAME_CHAR_LIMIT);

            const response = await hitCreateWealthkernel();
            expect(response.status).toEqual(204);

            const shortName = user.fullName.substring(0, 50);
            expect(shortName.length).toBe(50);
            expect(WealthkernelService.UKInstance.addBankAccount).toBeCalledWith({
              partyId: user.providers.wealthkernel.id,
              name: shortName,
              accountNumber: user.bankAccounts[0].number,
              sortCode: user.bankAccounts[0].sortCode,
              currency: CurrencyEnum.GBP,
              countryCode: "GB"
            });
            const newBankAccounts = await BankAccount.find({ owner: user.id });
            expect(newBankAccounts.length).toEqual(1);
            expect(newBankAccounts[0]?.providers?.wealthkernel?.id).toEqual(wealthkernelBankAccount.id);
          });
        });
      });
    });

    describe("POST /bank-accounts/sync-wealthkernel", () => {
      let minimumCreationTime: Date;
      describe("when we have a single bank account that does not have a WK ID", () => {
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts");
          Date.now = jest.fn(() => DATE.valueOf());
          minimumCreationTime = DateUtil.getDateOfMinutesAgo(16);
        });
        it("should return 204, not hit Wealthkernel for any operations and not edit our bank account", async () => {
          const bankAccountWithoutWkId: BankAccountDocument = await buildBankAccount({
            createdAt: minimumCreationTime,
            providers: {}
          });
          const response = await hitSyncWealthkernel();
          expect(response.status).toEqual(204);

          const allBankAccounts = await BankAccount.find({ owner: bankAccountWithoutWkId.owner });
          expect(allBankAccounts.length).toEqual(1);
          expect(allBankAccounts[0]?.providers?.wealthkernel?.id).toBeUndefined();
        });
      });

      describe("when we have a single bank account that has no status", () => {
        const wealthkernelBankAccount = buildWealthkernelBankAccountResponse();
        beforeEach(async () => {
          jest
            .spyOn(WealthkernelService.UKInstance, "retrieveBankAccount")
            .mockResolvedValue(wealthkernelBankAccount);
        });
        it("should return 204 and update the status of our bank account", async () => {
          const user = await buildUser();
          const pendingBankAccount: BankAccountDocument = await buildBankAccount({
            providers: {
              wealthkernel: { id: wealthkernelBankAccount.id }
            },
            owner: user,
            createdAt: minimumCreationTime
          });
          const response = await hitSyncWealthkernel();
          expect(response.status).toEqual(204);

          const updatedBankAccount: BankAccountDocument = await BankAccount.findById(pendingBankAccount.id);
          expect(updatedBankAccount.toObject()).toEqual(
            expect.objectContaining({
              providers: {
                wealthkernel: {
                  id: wealthkernelBankAccount.id,
                  status: wealthkernelBankAccount.status
                }
              }
            })
          );
        });
      });
    });

    describe("POST /bank-accounts/:id/wealthyhood-status", () => {
      describe("when we have a pending bank account and the updated status is 'Active' and we have pending deposits for the bank account", () => {
        let bankAccount: BankAccountDocument;
        let deposit: DepositCashTransactionDocument;

        beforeEach(async () => {
          jest.spyOn(WealthkernelService.EUInstance, "retrieveDeposit").mockResolvedValue(
            buildWealthkernelDepositResponse({
              status: "Settled"
            })
          );

          const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
          bankAccount = await buildBankAccount({
            owner: user.id,
            activeProviders: [ProviderEnum.WEALTHYHOOD],
            providers: {
              wealthyhood: { status: "Pending" }
            }
          });
          deposit = await buildDepositCashTransaction({
            owner: user.id,
            bankAccount: bankAccount.id,
            status: "Pending",
            depositMethod: DepositMethodEnum.BANK_TRANSFER,
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Created" }
            }
          });
        });

        it("should return 200 and set the provider status as Active and sync the deposit status", async () => {
          const response = await request(app)
            .post(`/api/admin/m2m/bank-accounts/${bankAccount.id}/wealthyhood-status`)
            .send({ status: "Active" })
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);

          const updatedBankAccount = await BankAccount.findById(bankAccount.id);
          expect(updatedBankAccount?.providers?.wealthyhood?.status).toBe("Active");

          const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
          expect(updatedDeposit?.providers?.wealthkernel?.status).toBe("Settled");
        });
      });
    });
  });
});

async function hitCreateWealthkernel() {
  return request(app)
    .post("/api/admin/m2m/bank-accounts/create-wealthkernel")
    .send({})
    .set("Accept", "application/json");
}

async function hitSyncWealthkernel() {
  return request(app)
    .post("/api/admin/m2m/bank-accounts/sync-wealthkernel")
    .send({})
    .set("Accept", "application/json");
}

async function expectNoActionsWereTaken(initialBankAccount: BankAccountDocument) {
  const newBankAccounts = await BankAccount.find({});

  expect(WealthkernelService.UKInstance.addBankAccount).toBeCalledTimes(0);
  expect(WealthkernelService.UKInstance.retrieveBankAccounts).toBeCalledTimes(0);
  expect(newBankAccounts.length).toEqual(1);
  expect(newBankAccounts[0]?.providers?.wealthkernel?.id).toEqual(initialBankAccount?.providers?.wealthkernel?.id);
}
