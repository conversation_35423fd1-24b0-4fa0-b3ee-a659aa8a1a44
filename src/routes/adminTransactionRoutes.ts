import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminTransactionController from "../controllers/adminTransactionController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getTransactions));
router.get("/assets", ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getAssetTransactionsPaginated));
router.get("/assets/:id", ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getAssetTransaction));
router.get(
  "/deposits",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getDepositCashTransactionsPaginated)
);
router.get(
  "/deposits/:id",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getDepositCashTransaction)
);
router.get(
  "/withdrawals",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getWithdrawalCashTransactionsPaginated)
);
router.get(
  "/withdrawals",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getWithdrawalCashTransactionsPaginated)
);
router.get(
  "/withdrawals/:id",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getWithdrawalCashTransaction)
);
router.get("/:id", ErrorMiddleware.catchAsyncErrors(AdminTransactionController.getTransaction));

/**
 * POST REQUESTS
 */
router.post(
  "/rebalances/process",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.processRebalanceTransactions)
);
router.post(
  "/assets/convert-users-with-pending-deposits",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.convertUsersThatHadTransactionsPendingDeposits)
);
router.post(
  "/deposits/sync-truelayer",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.syncPendingTruelayerDeposits)
);
router.post(
  "/deposits/sync-wealthkernel",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.syncPendingWealthkernelDeposits)
);
router.post("/deposits/:id/reverse", ErrorMiddleware.catchAsyncErrors(AdminTransactionController.reverseDeposit));
router.post(
  "/dividends/fetch-wealthkernel",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.fetchWealthkernelDividends)
);
router.post(
  "/withdrawals/create-wealthkernel",
  ErrorMiddleware.catchAsyncErrors(AdminTransactionController.createMissingWealthkernelWithdrawals)
);

export default router;
