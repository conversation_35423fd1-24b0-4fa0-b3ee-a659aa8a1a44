import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildInvestmentProduct,
  buildOrder,
  buildUser
} from "../../tests/utils/generateModels";
import { InvestmentProductDocument } from "../InvestmentProduct";
import { OrderDocument, OrderDTOInterface, OrderSubmissionIntentEnum, UnitPriceType } from "../Order";
import { AssetTransactionDocument } from "../Transaction";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("Order", () => {
  beforeAll(async () => await connectDb("Order"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  // Helper to create a base OrderDTOInterface with defaults for buildOrder
  const createBaseOrderData = async (overrides: Partial<OrderDTOInterface>): Promise<OrderDTOInterface> => {
    const user = await buildUser({}, false);
    const transaction = await buildAssetTransaction({ owner: user._id });

    const defaults: OrderDTOInterface = {
      side: "Buy",
      status: "Pending",
      consideration: {
        currency: "GBP",
        amount: 10000,
        originalAmount: 10000
      },
      quantity: 10,
      settlementCurrency: "GBP",
      activeProviders: [ProviderEnum.WEALTHKERNEL],
      submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
      transaction: transaction._id,
      unitPrice: { currency: "GBP", amount: 10 },
      exchangeRate: 1,
      fees: {
        fx: { currency: "GBP", amount: 0 }
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      isin: ASSET_CONFIG["equities_us"].isin
    };

    const finalConsideration = { ...defaults.consideration, ...overrides.consideration };
    const finalFees = { ...defaults.fees, ...overrides.fees };

    const mergedOverrides = {
      ...defaults,
      ...overrides,
      consideration: finalConsideration,
      fees: finalFees
    };

    return mergedOverrides as OrderDTOInterface;
  };

  describe("displayUnitPrice", () => {
    it("should calculate displayUnitPrice correctly for a BUY order with originalAmount, fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000, // €10 in cents
          amount: 900,
          currency: "EUR"
        },
        quantity: 1,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: {
            amount: 1, // €1
            currency: "EUR"
          }
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 9 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 9,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for BUY order using consideration.amount if originalAmount is missing, with fee", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: undefined,
          amount: 900, // €9 in cents
          currency: "EUR"
        },
        quantity: 1,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: {
            amount: 1, // €1
            currency: "EUR"
          }
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 8 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 8,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for a BUY order with originalAmount, NO fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000, // €10 in cents
          amount: 1000,
          currency: "EUR"
        },
        quantity: 1,
        fees: { fx: { currency: "EUR", amount: 0 } /* No realtimeExecution fee */ },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 10 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 10,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate take into account for displayUnitPrice of BUY order the realtime & FX fees", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 8000, // £80 in cents (before any fees)
          amount: 7840, // After fx fee (0.80) and realtimeExecution fee (0.80) = 8000 - 160 = 7840
          currency: "GBP"
        },
        quantity: 10,
        fees: {
          fx: { currency: "GBP", amount: 0.8 }, // £0.80 fx fee
          realtimeExecution: {
            amount: 0.8, // £0.80 realtime fee
            currency: "GBP"
          }
        },
        exchangeRate: 1.25, // GBP to USD
        unitPrice: { currency: "USD", amount: 9.8 }, // Price in USD
        settlementCurrency: "USD"
      });
      const order = await buildOrder(orderData);

      // Calculation: (originalAmount - realtimeExecution) * exchangeRate / quantity / 100
      // (8000 - 80) * 1.25 / 10 / 100 = 7920 * 1.25 / 10 / 100 = 9.9
      const expectedUnitPrice: UnitPriceType = {
        amount: 9.9,
        currency: "USD"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice correctly for a SELL order with amount, fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Sell",
        consideration: {
          originalAmount: 1000, // €10 in cents
          amount: 1000, // €10 in cents
          currency: "EUR"
        },
        quantity: 1,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: {
            amount: 1, // €1
            currency: "EUR"
          }
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 11 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 11,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for a SELL order with amount, NO fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Sell",
        consideration: {
          originalAmount: 1000, // €10 in cents
          amount: 1000, // €10 in cents
          currency: "EUR"
        },
        quantity: 1,
        fees: { fx: { currency: "EUR", amount: 0 }, realtimeExecution: { currency: "EUR", amount: 0 } },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 10 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 10,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for SELL order with FX fees and exchangeRate", async () => {
      const orderData = await createBaseOrderData({
        side: "Sell",
        consideration: {
          originalAmount: 10000, // $100 in cents (before fx fee)
          amount: 9920, // After fx fee deduction
          currency: "GBP"
        },
        quantity: 10,
        fees: {
          fx: { currency: "GBP", amount: 0.8 }, // £0.80 fx fee
          realtimeExecution: {
            amount: 0.8, // £0.80 realtime fee
            currency: "GBP"
          }
        },
        exchangeRate: 1.25, // GBP to USD
        unitPrice: { currency: "USD", amount: 10.1 }, // Price in USD
        settlementCurrency: "USD"
      });
      const order = await buildOrder(orderData);

      // Calculation: (originalAmount + realtimeExecution) * exchangeRate / quantity / 100
      // (10000 + 80) * 1.25 / 10 / 100 = 10080 * 1.25 / 10 / 100 = 12.6
      const expectedUnitPrice: UnitPriceType = {
        amount: 12.6,
        currency: "USD"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for BUY order with a different exchangeRate", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 8000, // £80 in cents
          currency: "GBP"
        },
        quantity: 10,
        fees: {
          fx: { currency: "GBP", amount: 0 },
          realtimeExecution: {
            amount: 0.8, // £0.80
            currency: "GBP"
          }
        },
        exchangeRate: 1.25, // GBP_TO_USD rate
        unitPrice: { currency: "USD", amount: 9.9 },
        settlementCurrency: "USD"
      });
      const order: OrderDocument = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 9.9,
        currency: "USD"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should return underlying unitPrice if consideration.amount is missing (and originalAmount for Buy)", async () => {
      const fallbackUnitPrice = { amount: 12.34, currency: "EUR" as currenciesConfig.MainCurrencyType };
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: undefined,
          amount: undefined,
          currency: "EUR"
        },
        quantity: 1,
        unitPrice: fallbackUnitPrice,
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);
      expect(order.displayUnitPrice).toEqual(fallbackUnitPrice);
    });

    it("should return underlying unitPrice if quantity is missing or zero", async () => {
      const fallbackUnitPrice = { amount: 15.67, currency: "EUR" as currenciesConfig.MainCurrencyType };
      let orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000,
          amount: 1000,
          currency: "EUR"
        },
        quantity: undefined,
        unitPrice: fallbackUnitPrice,
        settlementCurrency: "EUR"
      });
      let order = await buildOrder(orderData);
      expect(order.displayUnitPrice).toEqual(fallbackUnitPrice);

      orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000,
          amount: 1000,
          currency: "EUR"
        },
        quantity: 0,
        unitPrice: fallbackUnitPrice,
        settlementCurrency: "EUR"
      });
      order = await buildOrder(orderData);
      expect(order.displayUnitPrice).toEqual(fallbackUnitPrice);
    });

    it("should use FALLBACK_EXCHANGE_RATE (1) if order.exchangeRate is missing", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000,
          amount: 1000,
          currency: "EUR"
        },
        quantity: 1,
        fees: { fx: { currency: "EUR", amount: 0 } },
        exchangeRate: undefined,
        unitPrice: { currency: "EUR", amount: 10 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 10,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should subtract remainder from orderAmount for BUY orders", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 10000, // €100 in cents
          amountSubmitted: 9900, // €99 in cents
          amount: 9800, // €98 in cents (settled)
          currency: "EUR"
        },
        quantity: 10,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: { currency: "EUR", amount: 0.5 } // €0.50 realtime fee
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 9.75 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      // Calculation: (originalAmount - realtimeExecutionFee - remainder) / quantity / 100
      // originalAmount = 10000 cents
      // realtimeExecutionFee = 0.5 * 100 = 50 cents
      // remainder = amountSubmitted - amount = 9900 - 9800 = 100 cents
      // orderAmount = 10000 - 50 - 100 = 9850 cents
      // unitPrice = 9850 / 10 / 100 = 9.85
      const expectedUnitPrice: UnitPriceType = {
        amount: 9.85,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should not subtract remainder for SELL orders", async () => {
      const orderData = await createBaseOrderData({
        side: "Sell",
        consideration: {
          originalAmount: 9500, // €95 in cents
          amount: 9500, // €95 in cents
          currency: "EUR"
        },
        quantity: 10,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: { currency: "EUR", amount: 0.5 } // €0.50 realtime fee
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 9.55 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      // Calculation for SELL: (originalAmount + realtimeExecutionFee) / quantity / 100
      // originalAmount = 9500 cents
      // realtimeExecutionFee = 0.5 * 100 = 50 cents
      // orderAmount = 9500 + 50 = 9550 cents (no remainder subtraction for sell)
      // unitPrice = 9550 / 10 / 100 = 9.55
      const expectedUnitPrice: UnitPriceType = {
        amount: 9.55,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should handle BUY orders with no remainder", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 10000, // €100 in cents
          amountSubmitted: 9950, // €99.50 in cents
          amount: 9950, // €99.50 in cents (no remainder)
          currency: "EUR"
        },
        quantity: 10,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: { currency: "EUR", amount: 0.5 } // €0.50 realtime fee
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 9.95 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      // Calculation: (originalAmount - realtimeExecutionFee - remainder) / quantity / 100
      // originalAmount = 10000 cents
      // realtimeExecutionFee = 0.5 * 100 = 50 cents
      // remainder = amountSubmitted - amount = 9950 - 9950 = 0 cents
      // orderAmount = 10000 - 50 - 0 = 9950 cents
      // unitPrice = 9950 / 10 / 100 = 9.95
      const expectedUnitPrice: UnitPriceType = {
        amount: 9.95,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });
  });

  describe("getDisplayAmount", () => {
    let mockInvestmentProduct: InvestmentProductDocument;
    let mockTransaction: AssetTransactionDocument;

    beforeEach(async () => {
      // Create a generic investment product for tests that need it
      mockInvestmentProduct = await buildInvestmentProduct(true, {
        assetId: "equities_us", // or any relevant assetId
        price: 10, // 10 units of currency
        currentTicker: {
          // Ensure currentTicker is populated
          getPrice: (currency: currenciesConfig.MainCurrencyType) => {
            if (currency === "EUR") return 10; // €10
            if (currency === "GBP") return 8.5; // £8.50
            return 10; // Default
          }
        }
      });
      // Create a generic transaction
      const user = await buildUser({}, false);
      mockTransaction = await buildAssetTransaction({ owner: user._id, status: "Pending" });
    });

    describe("for SELL orders", () => {
      it("should return undefined if transaction status is Rejected", async () => {
        mockTransaction.status = "Rejected";
        await mockTransaction.save();
        const orderData = await createBaseOrderData({ side: "Sell", transaction: mockTransaction._id });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("EUR", mockTransaction, mockInvestmentProduct);
        expect(displayAmount).toBeUndefined();
      });

      it("should return undefined if transaction status is Cancelled", async () => {
        mockTransaction.status = "Cancelled";
        await mockTransaction.save();
        const orderData = await createBaseOrderData({ side: "Sell", transaction: mockTransaction._id });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("EUR", mockTransaction, mockInvestmentProduct);
        expect(displayAmount).toBeUndefined();
      });

      it("should return undefined if transaction status is DepositFailed", async () => {
        mockTransaction.status = "DepositFailed";
        await mockTransaction.save();
        const orderData = await createBaseOrderData({ side: "Sell", transaction: mockTransaction._id });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("EUR", mockTransaction, mockInvestmentProduct);
        expect(displayAmount).toBeUndefined();
      });

      it("should calculate estimated amount if consideration.amount is missing (pending sell)", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: undefined, currency: "EUR" }, // No consideration.amount
          quantity: 5,
          transaction: mockTransaction._id // mockTransaction status is "Pending" by default here
        });
        const order = await buildOrder(orderData);
        // Expected: investmentProduct.currentTicker.getPrice("EUR") * quantity * 100
        // 10 (price) * 5 (quantity) * 100 = 5000 cents
        const displayAmount = order.getDisplayAmount?.("EUR", mockTransaction, mockInvestmentProduct);
        expect(displayAmount).toBe(5000);
      });

      it("should return consideration.amount if present (settled sell)", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: 12345, currency: "EUR" }, // 123.45 EUR in cents
          quantity: 5,
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("EUR", mockTransaction, mockInvestmentProduct);
        expect(displayAmount).toBe(12345);
      });
    });

    describe("for BUY orders", () => {
      it("should return originalAmount minus remainder when all amounts are present", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: 5000, amountSubmitted: 4900, amount: 4800, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("GBP", mockTransaction, mockInvestmentProduct);
        // Current implementation prioritizes originalAmount minus remainder
        // remainder = amountSubmitted - amount = 4900 - 4800 = 100
        // displayAmount = originalAmount - remainder = 5000 - 100 = 4900
        expect(displayAmount).toBe(4900);
      });

      it("should return originalAmount minus remainder when amount is missing", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: 5000, amountSubmitted: 4900, amount: undefined, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("GBP", mockTransaction, mockInvestmentProduct);
        // Current implementation prioritizes originalAmount minus remainder
        // Since amount is undefined, remainder = 0 (no settlement difference)
        // displayAmount = originalAmount - remainder = 5000 - 0 = 5000
        expect(displayAmount).toBe(5000);
      });

      it("should return amountSubmitted minus remainder when originalAmount is missing", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: undefined, amountSubmitted: 4801, amount: 4800, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("GBP", mockTransaction, mockInvestmentProduct);
        // When originalAmount is missing, falls back to amountSubmitted minus remainder
        // remainder = amountSubmitted - amount = 4801 - 4800 = 1
        // displayAmount = amountSubmitted - remainder = 4801 - 1 = 4800
        expect(displayAmount).toBe(4800);
      });

      it("should return originalAmount minus remainder when both amount and amountSubmitted are missing", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: 5000, amountSubmitted: undefined, amount: undefined, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("GBP", mockTransaction, mockInvestmentProduct);
        // When both amount and amountSubmitted are missing, remainder = 0
        // displayAmount = originalAmount - remainder = 5000 - 0 = 5000
        expect(displayAmount).toBe(5000);
      });

      it("should return amount when both originalAmount and amountSubmitted are missing", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: undefined, amountSubmitted: undefined, amount: 4800, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount?.("GBP", mockTransaction, mockInvestmentProduct);
        // When originalAmount and amountSubmitted are missing, falls back to amount
        expect(displayAmount).toBe(4800);
      });
    });
  });

  describe("amountForReturnsAndUpBy", () => {
    describe("for BUY orders", () => {
      it("should return originalAmount minus realtime fee and remainder", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: 10000, amountSubmitted: 9900, amount: 9800, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 },
            realtimeExecution: { currency: "GBP", amount: 0.5 } // £0.50 in whole currency
          }
        });
        const order = await buildOrder(orderData);

        // Calculation: originalAmount - realtimeExecutionFee - remainder
        // originalAmount = 10000 cents
        // realtimeExecutionFee = 0.5 * 100 = 50 cents
        // remainder = amountSubmitted - amount = 9900 - 9800 = 100 cents
        // result = 10000 - 50 - 100 = 9850 cents
        expect(order.amountForReturnsAndUpBy).toBe(9850);
      });

      it("should return amountSubmitted minus realtime fee and remainder when originalAmount is missing", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: undefined, amountSubmitted: 9900, amount: 9800, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 },
            realtimeExecution: { currency: "GBP", amount: 0.5 } // £0.50 in whole currency
          }
        });
        const order = await buildOrder(orderData);

        // Calculation: amountSubmitted - realtimeExecutionFee - remainder
        // amountSubmitted = 9900 cents
        // realtimeExecutionFee = 0.5 * 100 = 50 cents
        // remainder = amountSubmitted - amount = 9900 - 9800 = 100 cents
        // result = 9900 - 50 - 100 = 9750 cents
        expect(order.amountForReturnsAndUpBy).toBe(9750);
      });

      it("should return amount minus realtime fee and remainder when originalAmount and amountSubmitted are missing", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: undefined, amountSubmitted: undefined, amount: 9800, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 },
            realtimeExecution: { currency: "GBP", amount: 0.5 } // £0.50 in whole currency
          }
        });
        const order = await buildOrder(orderData);

        // Calculation: amount - realtimeExecutionFee - remainder
        // amount = 9800 cents
        // realtimeExecutionFee = 0.5 * 100 = 50 cents
        // remainder = 0 (no amountSubmitted to compare with)
        // result = 9800 - 50 - 0 = 9750 cents
        expect(order.amountForReturnsAndUpBy).toBe(9750);
      });

      it("should handle orders with no realtime execution fee", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: 10000, amountSubmitted: 9900, amount: 9800, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 }
            // No realtimeExecution fee
          }
        });
        const order = await buildOrder(orderData);

        // Calculation: originalAmount - 0 - remainder
        // originalAmount = 10000 cents
        // realtimeExecutionFee = 0 cents
        // remainder = amountSubmitted - amount = 9900 - 9800 = 100 cents
        // result = 10000 - 0 - 100 = 9900 cents
        expect(order.amountForReturnsAndUpBy).toBe(9900);
      });

      it("should account for displayFxFee in calculation for BUY orders", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: 10000, amountSubmitted: 9900, amount: 9800, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0.02 },
            realtimeExecution: { currency: "GBP", amount: 0.5 }
          },
          displayFxFee: 0.1
        });
        const order = await buildOrder(orderData);

        // Calculation: originalAmount - realtimeExecutionFee - displayFxFee - remainder
        // originalAmount = 10000 cents
        // realtimeExecutionFee = 0.5 * 100 = 50 cents
        // displayFxFee = 0.1 * 100 = 10 cents
        // remainder = amountSubmitted - amount = 9900 - 9800 = 100 cents
        // result = 10000 - 50 - 10 - 100 = 9840 cents
        expect(order.amountForReturnsAndUpBy).toBe(9840);
      });
    });

    describe("for SELL orders", () => {
      it("should return consideration amount plus realtime fee", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: 9500, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 },
            realtimeExecution: { currency: "GBP", amount: 0.5 } // £0.50 in whole currency
          }
        });
        const order = await buildOrder(orderData);

        // Calculation: consideration.amount + realtimeExecutionFee
        // consideration.amount = 9500 cents
        // realtimeExecutionFee = 0.5 * 100 = 50 cents
        // result = 9500 + 50 = 9550 cents
        expect(order.amountForReturnsAndUpBy).toBe(9550);
      });

      it("should handle sell orders with no realtime execution fee", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: 9500, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 }
            // No realtimeExecution fee
          }
        });
        const order = await buildOrder(orderData);

        // Calculation: consideration.amount + 0
        // consideration.amount = 9500 cents
        // realtimeExecutionFee = 0 cents
        // result = 9500 + 0 = 9500 cents
        expect(order.amountForReturnsAndUpBy).toBe(9500);
      });

      it("should handle sell orders with missing consideration amount", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: undefined, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 },
            realtimeExecution: { currency: "GBP", amount: 0.5 } // £0.50 in whole currency
          }
        });
        const order = await buildOrder(orderData);

        // Calculation: 0 + realtimeExecutionFee
        // consideration.amount = 0 (undefined defaults to 0)
        // realtimeExecutionFee = 0.5 * 100 = 50 cents
        // result = 0 + 50 = 50 cents
        expect(order.amountForReturnsAndUpBy).toBe(50);
      });

      it("should account for displayFxFee in calculation for SELL orders", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: 9500, currency: "GBP" },
          fees: {
            fx: { currency: "GBP", amount: 0 },
            realtimeExecution: { currency: "GBP", amount: 0.5 }
          },
          displayFxFee: 0.1
        });
        const order = await buildOrder(orderData);

        // Calculation: consideration.amount + realtimeExecutionFee + displayFxFee
        // consideration.amount = 9500 cents
        // realtimeExecutionFee = 0.5 * 100 = 50 cents
        // displayFxFee = 0.1 * 100 = 10 cents
        // result = 9500 + 50 + 10 = 9560 cents
        expect(order.amountForReturnsAndUpBy).toBe(9560);
      });
    });
  });

  describe("displayUserFriendlyId", () => {
    it("should return the persisted userFriendlyId when it exists", async () => {
      const orderData = await createBaseOrderData({});
      const order = await buildOrder(orderData);

      expect(order.displayUserFriendlyId).toBe("ORD-" + order.userFriendlyId?.toUpperCase());
    });
  });

  describe("remainder", () => {
    it("should return 0 for sell orders", async () => {
      const orderData = await createBaseOrderData({
        side: "Sell",
        consideration: {
          currency: "GBP" as currenciesConfig.MainCurrencyType,
          amount: 1000,
          amountSubmitted: 1100
        }
      });
      const order = await buildOrder(orderData);

      expect(order.remainder).toBe(0);
    });

    it("should return 0 for buy orders with no remainder", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          currency: "GBP" as currenciesConfig.MainCurrencyType,
          amount: 1000,
          amountSubmitted: 1000
        }
      });
      const order = await buildOrder(orderData);

      expect(order.remainder).toBe(0);
    });

    it("should return the remainder amount for buy orders", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          currency: "GBP" as currenciesConfig.MainCurrencyType,
          amount: 950, // settled amount
          amountSubmitted: 1000 // submitted amount
        }
      });
      const order = await buildOrder(orderData);

      expect(order.remainder).toBe(50); // 1000 - 950
    });

    it("should handle orders with missing amountSubmitted", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          currency: "GBP" as currenciesConfig.MainCurrencyType,
          amount: 1000
        }
      });
      const order = await buildOrder(orderData);

      expect(order.remainder).toBe(0);
    });

    it("should return 0 for orders with missing amountSubmitted", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          currency: "GBP" as currenciesConfig.MainCurrencyType,
          amountSubmitted: 1000
        }
      });
      const order = await buildOrder(orderData);

      expect(order.remainder).toBe(0);
    });
  });
});
