import { ProviderEnum } from "../../configs/providersConfig";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildReward } from "../../tests/utils/generateModels";

describe("Reward", () => {
  beforeAll(async () => {
    await connectDb("Reward");
  });

  afterEach(async () => {
    await clearDb();
  });

  afterAll(async () => {
    await closeDb();
  });

  describe("remainder", () => {
    it("should return 0 when the executed amount is missing", async () => {
      const reward = await buildReward({
        consideration: {
          currency: "GBP",
          amount: 1000,
          bonusAmount: 1100,
          orderAmount: 1000
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL]
        }
      });

      expect(reward.remainder).toBe(0);
    });

    it("should return the difference between order amount and executed amount", async () => {
      const reward = await buildReward({
        consideration: {
          currency: "GBP",
          amount: 1000,
          bonusAmount: 1100,
          orderAmount: 1000
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              amount: 950,
              status: "Matched"
            }
          }
        }
      });

      expect(reward.remainder).toBe(50);
    });

    it("should return 0 when the executed amount matches the order amount", async () => {
      const reward = await buildReward({
        consideration: {
          currency: "GBP",
          amount: 1000,
          bonusAmount: 1100,
          orderAmount: 1000
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              amount: 1000,
              status: "Matched"
            }
          }
        }
      });

      expect(reward.remainder).toBe(0);
    });

    it("should not return negative values when executed amount is greater than order amount", async () => {
      const reward = await buildReward({
        consideration: {
          currency: "GBP",
          amount: 1000,
          bonusAmount: 1100,
          orderAmount: 1000
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              amount: 1100,
              status: "Matched"
            }
          }
        }
      });

      expect(reward.remainder).toBe(0);
    });
  });
});
