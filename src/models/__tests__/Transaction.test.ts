import { currenciesConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildCreditTicket,
  buildDepositCashTransaction,
  buildPortfolio,
  buildUser
} from "../../tests/utils/generateModels";
import { CreditTicketStatusType } from "../CreditTicket";
import { DepositCashTransactionDocument } from "../Transaction";
import { UserDocument } from "../User";
import { PortfolioDocument } from "../Portfolio";
import { DepositMethodEnum } from "../../types/transactions";
import DateUtil from "../../utils/dateUtil";
import { faker } from "@faker-js/faker";

describe("Transaction", () => {
  beforeAll(async () => await connectDb("Transaction"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  // Helper to create a base deposit with defaults
  const createBaseDepositData = async (overrides: Partial<DepositCashTransactionDocument> = {}) => {
    const user = await buildUser({}, false);
    const portfolio = await buildPortfolio({ owner: user._id });

    const defaults = {
      owner: user._id,
      portfolio: portfolio._id,
      depositMethod: DepositMethodEnum.OPEN_BANKING,
      consideration: {
        currency: "EUR" as currenciesConfig.MainCurrencyType,
        amount: 10000 // €100 in cents
      },
      activeProviders: [ProviderEnum.WEALTHKERNEL],
      status: "Pending" as const,
      ...overrides
    };

    return defaults;
  };

  describe("deposit.inInstantMoneyFlow", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({}, false);
      portfolio = await buildPortfolio({ owner: user._id });
    });

    it("should return false when deposit has no linked credit ticket", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: undefined // No credit ticket
      });

      const deposit = await buildDepositCashTransaction(depositData);
      expect(deposit.inInstantMoneyFlow).toBe(false);
    });

    it("should return true when linked credit ticket status is 'Credited'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Credited" as CreditTicketStatusType,
        creditedAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should return true when linked credit ticket status is 'Settled'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Settled" as CreditTicketStatusType,
        creditedAt: new Date(),
        settledAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should return false when linked credit ticket status is 'Rejected'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Rejected" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBe(false);
    });

    it("should return undefined when linked credit ticket status is 'Pending'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBeUndefined();
    });

    it("should handle credit ticket transitions from Pending to Credited", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      // Initially pending, should return undefined
      expect(deposit.inInstantMoneyFlow).toBeUndefined();

      // Update credit ticket to Credited
      creditTicket.status = "Credited";
      creditTicket.creditedAt = new Date();
      await creditTicket.save();

      // Re-populate and check again
      await deposit.populate("linkedCreditTicket");
      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should handle credit ticket transitions from Credited to Settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Credited" as CreditTicketStatusType,
        creditedAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      // Initially credited, should return true
      expect(deposit.inInstantMoneyFlow).toBe(true);

      // Update credit ticket to Settled
      creditTicket.status = "Settled";
      creditTicket.settledAt = new Date();
      await creditTicket.save();

      // Re-populate and check again - should still be true
      await deposit.populate("linkedCreditTicket");
      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should handle credit ticket transitions from Pending to Rejected", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      // Initially pending, should return undefined
      expect(deposit.inInstantMoneyFlow).toBeUndefined();

      // Update credit ticket to Rejected
      creditTicket.status = "Rejected";
      await creditTicket.save();

      // Re-populate and check again
      await deposit.populate("linkedCreditTicket");
      expect(deposit.inInstantMoneyFlow).toBe(false);
    });
  });

  describe("deposit.isMoneyReceived", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({}, false);
      portfolio = await buildPortfolio({ owner: user._id });
    });

    it("should return true when direct debit and bank transfer deposit has a credited credit ticket", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Credited" as CreditTicketStatusType,
        creditedAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.isMoneyReceived).toBe(true);
    });

    it("should return true when direct debit deposit is Settled", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        depositMethod: DepositMethodEnum.DIRECT_DEBIT,
        status: "Settled" as const
      });

      const deposit = await buildDepositCashTransaction(depositData);

      expect(deposit.isMoneyReceived).toBe(true);
    });

    it("should return false when direct debit and bank transfer deposit has no credited ticket and no confirmed collection incoming payment", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
        linkedCreditTicket: undefined
      });

      const deposit = await buildDepositCashTransaction(depositData);

      expect(deposit.isMoneyReceived).toBe(false);
    });
  });

  describe("deposit.isDirectDebitPaymentCollected", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({}, false);
      portfolio = await buildPortfolio({ owner: user._id });
    });

    it("should return true when collectionRequestDate is in the past", async () => {
      const YESTERDAY = DateUtil.getDateOfDaysAgo(new Date(), 1);

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        depositMethod: DepositMethodEnum.DIRECT_DEBIT,
        directDebit: {
          activeProviders: [ProviderEnum.GOCARDLESS],
          collectionRequestDate: YESTERDAY,
          providers: { gocardless: { id: faker.string.uuid(), status: "submitted" } }
        }
      });

      const deposit = await buildDepositCashTransaction(depositData);
      expect(deposit.isDirectDebitPaymentCollected).toBe(true);
    });

    it("should return true when GoCardless status is confirmed or paid_out", async () => {
      const depositConfirmed = await buildDepositCashTransaction(
        await createBaseDepositData({
          owner: user._id,
          portfolio: portfolio._id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: { gocardless: { id: faker.string.uuid(), status: "confirmed" } }
          }
        })
      );
      expect(depositConfirmed.isDirectDebitPaymentCollected).toBe(true);

      const depositPaidOut = await buildDepositCashTransaction(
        await createBaseDepositData({
          owner: user._id,
          portfolio: portfolio._id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: { gocardless: { id: faker.string.uuid(), status: "paid_out" } }
          }
        })
      );
      expect(depositPaidOut.isDirectDebitPaymentCollected).toBe(true);
    });

    it("should return true when Wealthkernel direct debit status is Collected or Completed", async () => {
      const depositCollected = await buildDepositCashTransaction(
        await createBaseDepositData({
          owner: user._id,
          portfolio: portfolio._id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Collected" } }
          }
        })
      );
      expect(depositCollected.isDirectDebitPaymentCollected).toBe(true);

      const depositCompleted = await buildDepositCashTransaction(
        await createBaseDepositData({
          owner: user._id,
          portfolio: portfolio._id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Completed" } }
          }
        })
      );
      expect(depositCompleted.isDirectDebitPaymentCollected).toBe(true);
    });

    it("should return false when collectionRequestDate is in the future and no confirming statuses present", async () => {
      const TOMORROW = DateUtil.getDateAfterNdays(new Date(), 1);

      const deposit = await buildDepositCashTransaction(
        await createBaseDepositData({
          owner: user._id,
          portfolio: portfolio._id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            collectionRequestDate: TOMORROW,
            providers: { gocardless: { id: faker.string.uuid(), status: "submitted" } }
          }
        })
      );
      expect(deposit.isDirectDebitPaymentCollected).toBe(false);
    });

    it("should return false when collectionRequestDate is in the past but transaction is Cancelled", async () => {
      const YESTERDAY = DateUtil.getDateOfDaysAgo(new Date(), 1);

      const deposit = await buildDepositCashTransaction(
        await createBaseDepositData({
          owner: user._id,
          portfolio: portfolio._id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            collectionRequestDate: YESTERDAY,
            providers: { gocardless: { id: faker.string.uuid(), status: "submitted" } }
          },
          status: "Cancelled"
        })
      );
      expect(deposit.isDirectDebitPaymentCollected).toBe(false);
    });
  });

  describe("deposit.displayDate", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({}, false);
      portfolio = await buildPortfolio({ owner: user._id });
    });

    it("should return credit ticket's credited date as display date if a credit ticket exists and is credited", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Credited" as CreditTicketStatusType,
        creditedAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(creditTicket.creditedAt);
    });

    it("should return credit ticket's credited date as display date if a credit ticket exists and is settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Settled" as CreditTicketStatusType,
        creditedAt: new Date(),
        settledAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(creditTicket.creditedAt);
    });

    it("should return transaction's settled date as display date if a credit ticket exists, it is not credited/settled, and the transaction has been settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Rejected" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id,
        settledAt: new Date()
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(deposit.settledAt);
    });

    it("should return transaction's created date as display date if a credit ticket exists, it is not credited/settled, and the transaction has not been settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(deposit.createdAt);
    });

    it("should return transaction's created date as display date if a credit ticket does not exist exist and the transaction has not been settled", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: undefined
      });

      const deposit = await buildDepositCashTransaction(depositData);

      expect(deposit.displayDate).toEqual(deposit.createdAt);
    });

    it("should return transaction's settled date as display date if a credit ticket does not exist exist and the transaction has been settled", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: undefined,
        settledAt: new Date()
      });

      const deposit = await buildDepositCashTransaction(depositData);

      expect(deposit.displayDate).toEqual(deposit.settledAt);
    });
  });
});
