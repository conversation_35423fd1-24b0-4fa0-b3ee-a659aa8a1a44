import {
  banksConfig,
  cashbacksConfig,
  currenciesConfig,
  dividendsConfig,
  entitiesConfig,
  investmentsConfig,
  investmentUniverseConfig,
  plansConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import Stripe from "stripe";
import events from "../event-handlers/events";
import logger from "../external-services/loggerService";
import eventEmitter from "../loaders/eventEmitter";
import {
  AssetDividendTransaction,
  AssetDividendTransactionDTOInterface,
  AssetTransaction,
  AssetTransactionDocument,
  AssetTransactionInterfaceDTO,
  CashbackTransaction,
  CashbackTransactionDocument,
  ChargeMethodType,
  ChargeTransaction,
  ChargeTransactionDocument,
  ChargeTransactionDTOInterface,
  ChargeTypeType,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  DepositCashTransactionInterfaceDTO,
  DividendTransaction,
  DividendTransactionDocument,
  DividendTransactionDTOInterface,
  FeesType,
  PortfolioTransactionCategoryType,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  RebalanceTransactionInterfaceDTO,
  RebalanceTransactionStatusType,
  RevertRewardTransaction,
  RevertRewardTransactionDocument,
  RevertRewardTransactionInterfaceDTO,
  SavingsDividendTransaction,
  SavingsDividendTransactionDocument,
  SavingsDividendTransactionDTOInterface,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  SavingsTopupTransactionDTOInterface,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument,
  SavingsWithdrawalTransactionDTOInterface,
  StockSplitTransaction,
  StockSplitTransactionDocument,
  Transaction,
  TransactionCategoryType,
  TransactionConsiderationType,
  TransactionDocument,
  TransactionPopulationFieldsEnum,
  TransactionStatusType,
  TruelayerPayloadType,
  WealthyhoodDividendTransaction,
  WealthyhoodDividendTransactionDocument,
  WealthyhoodDividendTransactionDTOInterface,
  WithdrawalCashTransaction,
  WithdrawalCashTransactionDocument
} from "../models/Transaction";
import { DepositMethodEnum, WithdrawalMethodEnum, TransferWithIntermediaryStageEnum } from "../types/transactions";
import validator from "validator";
import { User, UserDocument, UserPopulationFieldsEnum } from "../models/User";
import {
  HoldingsType,
  InitialHoldingsAllocationType,
  Portfolio,
  PortfolioDocument,
  PortfolioPopulationFieldsEnum
} from "../models/Portfolio";
import { BadRequestError, InternalServerError, NotFoundError } from "../models/ApiErrors";
import Decimal from "decimal.js";
import PortfolioService, { PendingOrderType, PortfolioAllocationMethodEnum } from "./portfolioService";
import mongoose, { ObjectId, QueryOptions } from "mongoose";
import {
  Order,
  OrderDocument,
  OrderDTOInterface,
  OrderInterface,
  OrderSubmissionIntentEnum
} from "../models/Order";
import {
  BonusStatusType,
  BonusType,
  ChargeStatusType,
  DepositStatusType,
  InternalTransferStatusType,
  TransactionStatusType as WkTransactionStatusType,
  TransactionType,
  WealthkernelService,
  WithdrawalRequestType,
  WithdrawalStatusType
} from "../external-services/wealthkernelService";
import { ApiResponse, PaginatedTransactionsResponse } from "apiResponse";
import {
  BeneficiaryEnum,
  FailureStatusType,
  PaymentStatusTypeV1,
  PaymentStatusTypeV3,
  TruelayerPaymentsClient,
  TruelayerPaymentVersionType,
  TruelayerPayProvidersType
} from "../external-services/truelayerService";
import {
  AssetTransactionsFilter,
  ChargeTransactionsFilter,
  DepositTransactionsFilter,
  TransactionsFilter
} from "filters";
import ConfigUtil from "../utils/configUtil";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import PaginationUtil from "../utils/paginationUtil";
import { captureException } from "@sentry/node";
import DbUtil from "../utils/dbUtil";
import { BankAccount, BankAccountDocument } from "../models/BankAccount";
import { customAlphabet } from "nanoid";
import OrderService from "./orderService";
import DateUtil from "../utils/dateUtil";
import PortfolioUtil from "../utils/portfolioUtil";
import InvestmentProductService from "./investmentProductService";
import { AssetType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { InvestmentProduct, InvestmentProductDocument } from "../models/InvestmentProduct";
import UserService from "./userService";
import RewardService from "./rewardService";
import { Reward, RewardDocument } from "../models/Reward";
import { SubscriptionDocument } from "../models/Subscription";
import CreditTicketService from "./creditTicketService";
import { MandateDocument, MandatePopulationFieldsEnum } from "../models/Mandate";
import {
  AutomationDocument,
  AutomationPopulationFieldsEnum,
  SavingsTopUpAutomationDocument,
  TopUpAutomationDocument
} from "../models/Automation";
import {
  TrackAssetDividendSuccessPropertiesType,
  TrackCustodyChargeSuccess,
  TrackDepositPropertiesType,
  TrackNotificationDividendSuccess,
  TrackNotificationOrderSettled,
  TrackNotificationSavingsDividendCreation,
  TrackPropertiesType,
  TrackSavingsDividendChargeSuccess,
  TrackSubscriptionChargeSuccess,
  TrackTransactionInfoCategoryType,
  TrackTransactionInfoType,
  TrackWealthyhoodDividendPropertiesType,
  TrackWithdrawalPropertiesType
} from "../external-services/segmentAnalyticsService";
import { aggregateFees, calculateFXRateWithSpread, getZeroFees } from "../utils/feesUtil";
import SubscriptionService from "./subscriptionService";
import {
  DailyPortfolioSavingsTicker,
  DailyPortfolioTicker,
  DailySavingsProductTicker
} from "../models/DailyTicker";
import { PartialRecord } from "utils";
import BankAccountService from "./bankAccountService";
import { randomUUID } from "crypto";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import { PaymentMethodDocument } from "../models/PaymentMethod";
import { PlatformCategoryEnum } from "../configs/platformConfig";
import { ForeignCurrencyRatesType } from "currencies";
import { InvestmentProductsDictType } from "investmentProducts";
import { SavingsProductDocument } from "../models/SavingsProduct";
import CurrencyUtil from "../utils/currencyUtil";
import {
  ExtendedAssetTransactionCategoryEnum,
  getBuyOrderAmount,
  getExtendedPortfolioTransactionCategory
} from "../utils/transactionUtil";
import { MainCurrencyToWealthkernelCurrency } from "../configs/currenciesConfig";
import { IncomingPaymentStatusType, OutgoingPaymentStatusType } from "../external-services/devengoService";
import { ProviderEnum } from "../configs/providersConfig";
import { DepositActionEnum } from "../configs/depositsConfig";
import ExecutionWindowUtil from "../utils/executionWindowUtil";
import { TruelayerDepositCreationDataType } from "./openBankingPaymentService";
import { EventType } from "../external-services/goCardlessPaymentsService";
import { PaymentStatusType } from "../external-services/saltedgeService";
import { OrderUtil } from "../utils/orderUtil";
import UserUtil from "../utils/userUtil";
import { RedisClientService } from "../loaders/redis";
import WealthkernelUtil from "../utils/wealthkernelUtil";
import { DEFAULT_SAVINGS_PRODUCT_CONFIG } from "../configs/savingsConfig";
import { TransactionPreview } from "previews";
import { HoldingsDictType } from "holdings";
import AutomationService from "./automationService";
import { getAssetIdFromIsin } from "../utils/investmentUniverseUtil";
import { CreditTicketDocument } from "../models/CreditTicket";
import { CreditTicketRepository } from "../repositories/creditTicketRepository";
import { FXSpreadSide } from "../types/fees";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;
const { CASHBACK_RATES, MINIMUM_AMOUNT_FOR_CASHBACK } = cashbacksConfig;
const { WEALTHYHOOD_DIVIDEND_RATES, MAXIMUM_PORTFOLIO_VALUE_FOR_DIVIDEND, MINIMUM_PORTFOLIO_VALUE_FOR_DIVIDEND } =
  dividendsConfig;
const { ASSET_CONFIG } = investmentUniverseConfig;
const { MIN_ALLOWED_WITHDRAWAL, MIN_ALLOWED_ASSET_INVESTMENT } = investmentsConfig;
const { PriceArrayConst } = plansConfig;
const WK_BENEFICIARY_NAME = process.env.WEALTHKERNEL_BENEFICIARY_NAME;
const WK_BENEFICIARY_ACCOUNT_NUMBER = process.env.WEALTHKERNEL_BENEFICIARY_ACCOUNT_NUMBER;
const WK_BENEFICIARY_SORT_CODE = process.env.WEALTHKERNEL_BENEFICIARY_SORT_CODE;
if (!WK_BENEFICIARY_NAME || !WK_BENEFICIARY_ACCOUNT_NUMBER || !WK_BENEFICIARY_SORT_CODE) {
  throw new Error("WK beneficiary bank details in env vars are undefined");
}
const BANK_REFERENCE_LENGTH = 16;
// These are the allowed characters in bank references
const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
export const nanoid = customAlphabet(alphabet, BANK_REFERENCE_LENGTH);

const DIVIDENDS_TRACKING_DAYS = 5;
const ASSET_DIVIDENDS_TRACKING_DAYS = 5;

// Wealthkernel Minimum Order amount in pounds/euros etc. (NOT CENTS)
export const SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT = 0.02;

export const TransactionSellModeArray = ["normal", "whole"] as const;
export type TransactionSellMode = (typeof TransactionSellModeArray)[number];

export type TransactionWithOrders =
  | AssetTransactionDocument
  | RebalanceTransactionDocument
  | ChargeTransactionDocument
  | SavingsTopupTransactionDocument
  | SavingsWithdrawalTransactionDocument;

type AssetRecentActivityOrderItemType = {
  type: "order";
  item: OrderDocument;
};
type AssetRecentActivityDividendItemType = {
  type: "dividend";
  item: DividendTransactionDocument;
};
export type AssetRecentActivityTransactionType =
  | AssetRecentActivityOrderItemType
  | AssetRecentActivityDividendItemType;

type TransactionCharge = {
  transactionId: string;
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
  companyEntity: entitiesConfig.CompanyEntityEnum;
};

type FillClientDisplayFields = {
  displayAmount?: boolean;
  displayQuantity?: boolean;
  displayExchangeRate?: boolean;
  isCancellable?: boolean;
  executionWindow?: boolean;
  estimatedRealTimeCommission?: boolean;
};

type ChargesType = {
  custodyAmounts: Record<string, TransactionCharge[]>;
  commissionAmounts: Record<string, TransactionCharge[]>;
  executionSpreadAmounts: Record<string, TransactionCharge[]>;
  revertRewardAmounts: Record<string, TransactionCharge[]>;
  fxAmounts: Record<string, TransactionCharge[]>;
  dividendFeeAmounts: Record<string, TransactionCharge[]>;
  realtimeExecutionFeeAmounts: Record<string, TransactionCharge[]>;
};

export class TransactionService {
  public static async createAssetTransaction(
    partialAssetTransactionData: Omit<AssetTransactionInterfaceDTO, "createdAt" | "originalInvestmentAmount">
  ): Promise<AssetTransactionDocument> {
    const assetTransactionData: AssetTransactionInterfaceDTO = {
      ...partialAssetTransactionData,
      originalInvestmentAmount: partialAssetTransactionData.consideration.amount,
      createdAt: new Date()
    };

    return new AssetTransaction(assetTransactionData).save();
  }

  /**
   * Creates a rebalance transaction. Does not create any orders for the rebalancing as those will be
   * created in cron task later.
   *
   * @param realPortfolio
   * @param targetAllocation
   * @param options
   */
  public static async createRebalanceTransaction(
    realPortfolio: PortfolioDocument,
    targetAllocation: InitialHoldingsAllocationType[],
    options?: {
      linkedAutomation?: AutomationDocument;
    }
  ): Promise<RebalanceTransactionDocument> {
    const { sellExecutionWindow, buyExecutionWindow } = ExecutionWindowUtil.getRebalanceExecutionWindow();

    const rebalanceTransactionData: RebalanceTransactionInterfaceDTO = {
      owner: realPortfolio.owner as mongoose.Types.ObjectId,
      portfolio: realPortfolio._id,
      targetAllocation: targetAllocation,
      buyExecutionWindow,
      sellExecutionWindow,
      createdAt: new Date()
    };

    if (options?.linkedAutomation) {
      rebalanceTransactionData.linkedAutomation = options.linkedAutomation._id;
    }

    return new RebalanceTransaction(rebalanceTransactionData).save();
  }

  public static async createChargeTransaction(
    partialTransactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount">
  ): Promise<ChargeTransactionDocument> {
    const chargeTransactionData: ChargeTransactionDTOInterface = {
      ...partialTransactionData,
      originalChargeAmount: partialTransactionData.consideration.amount,
      createdAt: new Date()
    };

    return new ChargeTransaction(chargeTransactionData).save();
  }

  /**
   * Creates a revert reward transaction.
   *
   * @param realPortfolio
   * @param rewardId
   * @param requestId
   */
  public static async createRevertRewardTransaction(
    realPortfolio: PortfolioDocument,
    rewardId: string,
    requestId: string
  ): Promise<RevertRewardTransactionDocument> {
    const reward = await RewardService.getReward(rewardId);

    const owner = realPortfolio.populated("owner")
      ? (realPortfolio.owner as UserDocument).id
      : (realPortfolio.owner as mongoose.Types.ObjectId).toString();

    const user = await UserService.getUser(owner);

    if (reward.targetUser.id !== user.id) {
      throw new BadRequestError("The portfolio passed did not have the same owner as the reward");
    } else if (reward.unrestrictedAt < new Date()) {
      throw new BadRequestError("The reward to revert is not restricted");
    }

    const revertRewardTransactionData: RevertRewardTransactionInterfaceDTO = {
      owner: user.id,
      portfolio: realPortfolio._id,
      linkedUserDataRequest: new mongoose.Types.ObjectId(requestId),
      reward: new mongoose.Types.ObjectId(rewardId),
      consideration: {
        currency: user.currency
      },
      createdAt: new Date(),
      activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
    };

    return new RevertRewardTransaction(revertRewardTransactionData).save();
  }

  /**
   * @description Returns the total amount of dividends a user has received for a given asset.
   *
   * @param assetId
   * @param ownerId
   * @returns
   */
  public static async getTotalDividendsForAsset(
    assetId: investmentUniverseConfig.AssetType,
    ownerId: mongoose.Types.ObjectId
  ): Promise<number> {
    const dividends = await DividendTransaction.find({
      asset: assetId,
      owner: ownerId,
      "providers.wealthkernel.status": "Settled"
    });

    return dividends
      .map((dividend) => Decimal.div(dividend.consideration.amount, 100))
      .reduce((sum, value) => sum.plus(value), new Decimal(0))
      .toDecimalPlaces(2)
      .toNumber();
  }

  /**
   * Withdraws an amount from the user's portfolio's available cash.
   *
   * @param portfolioId The portfolio from which the withdrawal will be made
   * @param amount The amount to be withdrawn in POUNDS.
   * @param options { bankAccountId: The bank account to which money will be returned, linkedUserDataRequest: If the
   * withdrawal is triggered by user data request (e.g. deletion), its ID }
   * @returns Promise< void >
   */
  public static async withdraw(
    portfolioId: string,
    amount: number,
    options?: {
      bankAccountId?: string;
      linkedUserDataRequest?: string;
      shouldUpdateCash?: boolean;
      session?: mongoose.ClientSession;
    }
  ): Promise<PortfolioDocument> {
    const shouldUpdateCash = options?.shouldUpdateCash ?? true;

    const portfolio = await Portfolio.findOne({ _id: portfolioId }, null, { session: options?.session }).populate(
      "owner"
    );
    if (!portfolio) {
      throw new BadRequestError("'portfolioId' does not refer to a portfolio");
    }

    const user = portfolio.owner as UserDocument;

    // Assert that the portfolio is real
    if (!portfolio.isReal) {
      throw new InternalServerError("Portfolio is not real");
    }

    if (options?.bankAccountId) {
      const bankAccountExists = await BankAccount.exists({
        _id: new mongoose.Types.ObjectId(options?.bankAccountId),
        owner: user.id
      });
      if (!bankAccountExists) {
        throw new BadRequestError("The provided bank account id is not valid", "Invalid parameter");
      }
    }

    if ((portfolio?.cash?.[user.currency]?.settled ?? 0) < amount && shouldUpdateCash) {
      // Assert that the amount to be withdrawn is smaller or equal to the available cash
      throw new BadRequestError("Portfolio's settled cash is lower than the withdrawal amount");
    }

    const bankReference = nanoid();

    // If amount is over MIN_ALLOWED_WITHDRAWAL than withdraw with type specified amount.
    let withdrawalRequestType: WithdrawalRequestType = "SpecifiedAmount";
    if (amount < MIN_ALLOWED_WITHDRAWAL) {
      // If amount is less than MIN_ALLOWED_WITHDRAWAL the full amount, but only if the available cash
      // matches the wealthkernel available cash - otherwise we have pending charges
      withdrawalRequestType = "Full";

      const latestWealthkernelValuation = await ProviderService.getBrokerageService(
        (portfolio.owner as UserDocument).companyEntity
      ).retrieveLatestValuationByPortfolio(portfolio.providers?.wealthkernel?.id);

      if (!latestWealthkernelValuation) {
        logger.warn(`Valuation wasn't retrieved for ${portfolio.providers?.wealthkernel?.id}`, {
          module: "Transaction Service",
          method: "withdraw",
          data: { wealthkernelPortfolioId: portfolio.providers?.wealthkernel?.id }
        });
        return;
      }

      const wkCash = latestWealthkernelValuation.cash[0]?.value?.amount ?? 0;

      if ((portfolio?.cash?.[user.currency]?.settled ?? 0) !== wkCash) {
        logger.warn(`Attempted to withdraw ${amount} in full but user has pending charges`, {
          module: "TransactionService",
          method: "withdraw",
          data: { portfolioId, amount }
        });
        return;
      }
    }

    const withdrawalData = {
      owner: user.id,
      portfolio: portfolio.id,
      consideration: {
        amount: Decimal.mul(amount, 100).toNumber(),
        currency: user.currency
      },
      // Unique identifier of the bank account the withdrawal should go to.
      // When not set or null, the party's default bank account will be used.
      bankAccount: options.bankAccountId,
      linkedUserDataRequest: options.linkedUserDataRequest,
      bankReference,
      createdAt: new Date(),
      withdrawalRequestType,
      activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE]),
      withdrawalMethod:
        user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          ? WithdrawalMethodEnum.WITH_INTERMEDIARY
          : WithdrawalMethodEnum.DIRECT
    };
    await new WithdrawalCashTransaction(withdrawalData).save({ session: options?.session });

    if (!shouldUpdateCash) {
      return portfolio;
    }

    // Reduce the available cash
    return PortfolioService.updateCashAvailability(portfolio.id, user.currency, -amount, {
      available: true,
      settled: true,
      session: options?.session
    });
  }

  public static async getAssetTransactions(
    filter: AssetTransactionsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: {
      owner?: boolean;
      orders?: boolean;
      pendingDeposit?: boolean;
    },
    sort?: string
  ) {
    return (await TransactionService.getTransactions(
      { ...filter, categories: ["AssetTransaction"] },
      pageConfig,
      populate,
      sort
    )) as PaginatedTransactionsResponse<AssetTransactionDocument> | AssetTransactionDocument[];
  }

  public static async getPendingRebalances(userId: string): Promise<RebalanceTransactionDocument[]> {
    return RebalanceTransaction.find({
      owner: userId,
      rebalanceStatus: { $in: ["NotStarted", "PendingSell", "PendingBuy"] }
    });
  }

  public static async getPendingStockSplitTransactions(): Promise<StockSplitTransactionDocument[]> {
    return StockSplitTransaction.find({
      status: "Pending"
    }).populate("portfolio stockSplit");
  }

  public static async getPendingTransactions(user: UserDocument) {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);

    const [transactions, investmentProducts, buyFxRates, sellFxRates] = await Promise.all([
      Promise.all([
        DepositCashTransaction.find({
          owner: user.id,
          status: "Pending",
          $or: [
            { "providers.truelayer.status": { $in: ["authorized", "executed"] } },
            { "providers.saltedge.status": "accepted" },
            { depositMethod: DepositMethodEnum.BANK_TRANSFER }
          ]
        }).populate("linkedAssetTransaction linkedSavingsTopup bankAccount linkedCreditTicket"),
        WithdrawalCashTransaction.find({
          owner: user.id,
          "providers.wealthkernel.status": { $nin: ["Settled", "Cancelled"] }
        }).populate("bankAccount"),
        AssetTransaction.find({
          owner: user.id,
          status: { $in: ["PendingDeposit", "PendingGift", "Pending"] }
        }).populate([
          {
            path: "pendingDeposit",
            populate: {
              path: "linkedCreditTicket"
            }
          },
          { path: "orders" },
          {
            path: "linkedAutomation",
            populate: {
              path: "mandate"
            }
          }
        ]),
        RebalanceTransaction.find({
          owner: user.id,
          rebalanceStatus: { $in: ["NotStarted", "PendingSell", "PendingBuy"] },
          linkedAutomation: { $exists: false }
        }).populate("orders")
      ]).then((transactions) => transactions.flat()),
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL)
    ]);

    const transactionsWithClientDisplayFields = transactions.map((transaction) => {
      if (["AssetTransaction", "RebalanceTransaction"].includes(transaction.category)) {
        return TransactionService.fillClientDisplayFields(
          user,
          transaction,
          investmentProducts,
          { BUY: buyFxRates, SELL: sellFxRates },
          {
            displayAmount: true,
            displayQuantity: true,
            executionWindow: true,
            displayExchangeRate: true,
            isCancellable: true,
            estimatedRealTimeCommission: true
          }
        );
      } else return transaction;
    });

    const rebalanceTransactions: RebalanceTransactionDocument[] = [];
    const depositCashTransactions: DepositCashTransactionDocument[] = [];
    const withdrawalCashTransactions: WithdrawalCashTransactionDocument[] = [];
    const assetTransactions: AssetTransactionDocument[] = [];
    transactionsWithClientDisplayFields.forEach((transaction) => {
      switch (transaction.category) {
        case "RebalanceTransaction":
          rebalanceTransactions.push(transaction as RebalanceTransactionDocument);
          break;
        case "DepositCashTransaction":
          depositCashTransactions.push(transaction as DepositCashTransactionDocument);
          break;
        case "WithdrawalCashTransaction":
          withdrawalCashTransactions.push(transaction as WithdrawalCashTransactionDocument);
          break;
        case "AssetTransaction":
          assetTransactions.push(transaction as AssetTransactionDocument);
          break;
        default:
          break;
      }
    });

    // filter deposits that are linked to asset transaction, savings topup or credited credit ticket
    const filteredDepositCashTransactions = depositCashTransactions
      .filter(this._filterOutDepositLinkedToAssetTransaction)
      .filter(this._filterOutDepositLinkedToSavingsTopup)
      .filter(this._filterOutDepositLinkedToCreditedCreditTicket);

    // filter asset transactions
    const filteredAssetTransactions = assetTransactions.filter((transaction) => {
      if (transaction.status === "PendingDeposit") {
        const pendingDeposit = transaction?.pendingDeposit as DepositCashTransactionDocument;

        // one off investments (with deposit)
        const isTruelayerDepositSuccessful =
          pendingDeposit.providers?.truelayer?.status === "authorized" ||
          pendingDeposit.providers?.truelayer?.status === "executed";
        if (pendingDeposit.providers?.truelayer) {
          return isTruelayerDepositSuccessful;
        }

        // repeating investments (with direct debit)
        if (pendingDeposit.linkedAutomation) {
          return pendingDeposit.isDirectDebitPaymentCollected || pendingDeposit.createdWhilePendingMandate;
        }
      } else return true;
    });

    return [
      ...rebalanceTransactions,
      ...withdrawalCashTransactions,
      ...filteredAssetTransactions,
      ...filteredDepositCashTransactions
    ].sort((transactionA, transactionB) => {
      if (transactionA.createdAt < transactionB.createdAt) {
        return 1;
      } else if (transactionA.createdAt > transactionB.createdAt) {
        return -1;
      }
    });
  }

  /**
   * Returns a list of transactions whose orders are eligible for polling.
   * @param userId
   * @param options
   */
  public static async getTransactionsWithPotentiallyPendingOrders(
    userId: string,
    options?: { session: mongoose.ClientSession }
  ): Promise<(AssetTransactionDocument | RebalanceTransactionDocument)[]> {
    return (await Transaction.find(
      {
        owner: userId,
        $or: [
          {
            category: "RebalanceTransaction",
            rebalanceStatus: { $in: ["PendingSell", "PendingBuy"] },
            linkedAutomation: { $exists: false }
          },
          {
            category: "AssetTransaction",
            linkedAutomation: { $exists: false },
            status: { $in: ["Pending"] }
          }
        ]
      },
      null,
      { session: options?.session }
    ).populate("orders")) as unknown as (AssetTransactionDocument | RebalanceTransactionDocument)[];
  }

  /**
   * Returns a list of AssetTransactions that may contain "Matched" orders.
   * @param userId
   */
  public static async getAssetTransactionsWithPotentiallyMatchedOrders(
    userId: string
  ): Promise<AssetTransactionDocument[]> {
    return AssetTransaction.find(
      {
        owner: userId,
        linkedAutomation: { $exists: false },
        status: { $in: ["Settled", "Pending"] }
      },
      null
    ).populate("orders");
  }

  /**
   * Checks if the user has any pending asset transactions - this is used to determine the user's portfolio conversion status.
   * This includes:
   * - Active repeating investments
   * - Pending transactions that are not linked to incomplete deposits
   * @param userId The ID of the user to check
   * @returns True if the user has pending asset transactions, false otherwise
   */
  public static async hasPendingAssetTransactions(userId: string): Promise<boolean> {
    const assetTransactions = await AssetTransaction.find({
      owner: userId,
      status: { $in: ["Pending", "PendingDeposit", "PendingGift"] }
    }).populate("pendingDeposit");

    return (
      TransactionService._hasActiveRepeatingInvestments(assetTransactions) ||
      assetTransactions.filter(TransactionService._filterOutAssetTransactionLinkedToIncompleteDeposits).length > 0
    );
  }

  /**
   * Returns a list of savings-related transactions with potentially pending/not submitted amount.
   * @param userId
   * @param savingsProductId
   */
  public static async getSavingsTransactionsWithPotentiallyPendingAmounts(
    userId: string,
    savingsProductId: savingsUniverseConfig.SavingsProductType
  ): Promise<(SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument)[]> {
    const transactions = (await Transaction.find({
      owner: userId,
      $or: [
        {
          category: "SavingsTopupTransaction",
          status: { $in: ["Pending", "PendingDeposit"] },
          savingsProduct: savingsProductId
        },
        {
          category: "SavingsWithdrawalTransaction",
          status: { $in: ["Pending", "PendingTopUp"] },
          savingsProduct: savingsProductId
        }
      ]
    }).populate("orders pendingDeposit")) as unknown as (
      | SavingsTopupTransactionDocument
      | SavingsWithdrawalTransactionDocument
    )[];

    return transactions.filter(TransactionService.filterOutSavingsTopUpTransactionLinkedToIncompleteDeposits);
  }

  public static async getAssetRecentActivityTransactions(
    userId: string,
    assetId: AssetType
  ): Promise<AssetRecentActivityTransactionType[]> {
    const [investmentProduct, owner, transactions] = await Promise.all([
      InvestmentProduct.findOne({ commonId: assetId }).populate("currentTicker"),
      UserService.getUser(userId, {
        addresses: false,
        portfolios: false,
        subscription: true
      }),
      Transaction.find({
        owner: userId,
        $or: [
          { category: "RebalanceTransaction", rebalanceStatus: { $nin: ["Rejected"] } },
          {
            category: "DividendTransaction",
            asset: assetId
          },
          {
            category: "AssetTransaction",
            status: { $in: ["PendingDeposit", "Pending", "Settled", "Cancelled", "PendingGift"] }
          }
        ]
      })
        .sort({ createdAt: -1 }) // Sorting in descending order by createdAt
        .populate([{ path: "orders" }, { path: "pendingDeposit" }])
    ]);

    const plan = (owner.subscription as SubscriptionDocument).plan;
    const latestFXRates = {
      BUY: await TransactionService.getAllForeignCurrencyRatesWithSpread(owner, plan, FXSpreadSide.BUY),
      SELL: await TransactionService.getAllForeignCurrencyRatesWithSpread(owner, plan, FXSpreadSide.SELL)
    };

    // Filtering out of repeating investments with not settled deposits could have been implemented inside
    // _filterOutAssetTransactionLinkedToIncompleteDeposits method but this would affect the method
    // the calculates the user portfolio conversion status and would result in breaking changes in the UI,
    // so the fix was implementated in a separate method.
    const transactionsFiltered: TransactionDocument[] = transactions
      .filter(this._filterOutAssetTransactionLinkedToIncompleteDeposits)
      .filter(this._filterOutRepeatingAssetTransactionsWithNonSettledDeposits);

    const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(assetId));
    const recentActivityOrderItems = transactionsFiltered
      .filter((transaction) => transaction.category !== "DividendTransaction")
      .flatMap((transaction: AssetTransactionDocument | RebalanceTransactionDocument) => {
        const { orders } = transaction as AssetTransactionDocument | RebalanceTransactionDocument;
        return orders
          .filter((order: OrderDocument) => allAssetIsinsSet.has(order.isin))
          .map((order: OrderDocument) => {
            const orderWithDisplayFields = OrderService.fillOrderClientDisplayFields(
              owner,
              order,
              // At this point we have kept only the orders that correspond to the asset's isin
              // so we can use the investment product that we queried in the beginning
              investmentProduct,
              latestFXRates,
              transaction,
              {
                isCancellable: true,
                displayAmount: true,
                displayQuantity: true,
                displayExchangeRate: true,
                estimatedRealTimeCommission: true
              }
            );
            return {
              type: "order",
              item: {
                ...orderWithDisplayFields,
                // on iOS & Android we only need transaction category & linkedAutomation
                transaction
              }
            };
          });
      });

    const recentActivityDividendItems = transactionsFiltered
      .filter((transaction) => transaction.category === "DividendTransaction")
      .map((dividend) => ({
        type: "dividend",
        item: dividend
      }));

    return [].concat(recentActivityDividendItems, recentActivityOrderItems);
  }

  public static async getRebalanceTransactions(
    filter: TransactionsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: {
      owner?: boolean;
      orders?: boolean;
    },
    sort?: string
  ) {
    return (await TransactionService.getTransactions(
      { ...filter, categories: ["RebalanceTransaction"] },
      pageConfig,
      populate,
      sort
    )) as PaginatedTransactionsResponse<RebalanceTransactionDocument> | RebalanceTransactionDocument[];
  }

  public static async getRevertRewardTransactions(
    filter: TransactionsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: {
      owner?: boolean;
    },
    sort?: string
  ) {
    return (await TransactionService.getTransactions(
      { ...filter, categories: ["RevertRewardTransaction"] },
      pageConfig,
      populate,
      sort
    )) as PaginatedTransactionsResponse<RevertRewardTransactionDocument> | RevertRewardTransactionDocument[];
  }

  public static async getDepositCashTransactions(
    filter: DepositTransactionsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: {
      owner?: boolean;
    },
    sort?: string
  ) {
    if (pageConfig) {
      return TransactionService._getTransactionsPaginated(
        { ...filter, categories: ["DepositCashTransaction"] },
        pageConfig,
        populate,
        sort
      );
    }

    const filterToUse = TransactionService._createDbTransactionFilter(filter);
    const populationString = DbUtil.getPopulationString(populate);

    return DepositCashTransaction.find(filterToUse).populate(populationString);
  }

  public static async getWithdrawalCashTransactions(
    filter: TransactionsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: {
      owner?: boolean;
      portfolio?: boolean;
    },
    sort?: string
  ) {
    return (await TransactionService.getTransactions(
      { ...filter, categories: ["WithdrawalCashTransaction"] },
      pageConfig,
      populate,
      sort
    )) as PaginatedTransactionsResponse<WithdrawalCashTransactionDocument> | WithdrawalCashTransactionDocument[];
  }

  public static async getChargeTransactions(
    filter: ChargeTransactionsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: {
      owner?: boolean;
      orders?: boolean;
      pendingDeposit?: boolean;
    },
    sort?: string
  ): Promise<PaginatedTransactionsResponse<ChargeTransactionDocument> | ChargeTransactionDocument[]> {
    return (await TransactionService.getTransactions(
      { ...filter, categories: ["ChargeTransaction"] },
      pageConfig,
      populate,
      sort
    )) as PaginatedTransactionsResponse<ChargeTransactionDocument> | ChargeTransactionDocument[];
  }

  public static async getTransaction(
    id: string,
    populate?: Record<string, boolean>,
    options?: { fillClientDisplayFields: boolean }
  ): Promise<TransactionDocument> {
    const populationString = DbUtil.getPopulationString(populate);

    const transaction = await Transaction.findById(id).populate(populationString);

    if (populate?.orders && options?.fillClientDisplayFields) {
      const [user, investmentProducts] = await Promise.all([
        UserService.getUser(transaction.owner.toString()),
        InvestmentProductService.getInvestmentProductsDict("isin", true),
        DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.ORDERS)
      ]);

      return TransactionService.fillClientDisplayFields(user, transaction, investmentProducts);
    }

    return transaction;
  }

  public static async getTransactions(
    filter: TransactionsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: Record<string, boolean>,
    sort?: string
  ) {
    return pageConfig
      ? TransactionService._getTransactionsPaginated(filter, pageConfig, populate, sort)
      : TransactionService._getTransactions(filter, populate, sort);
  }

  public static async getAssetTransaction(
    id: string,
    populateOrders?: boolean,
    populateForeignCurrencyRates?: boolean
  ): Promise<AssetTransactionDocument> {
    const transaction = await AssetTransaction.findById(id);

    if (populateOrders) {
      const ownerId = transaction.populated("owner")
        ? (transaction.owner as UserDocument).id
        : transaction.owner.toString();

      const [investmentProducts, owner] = await Promise.all([
        InvestmentProductService.getInvestmentProductsDict("isin", true),
        UserService.getUser(ownerId, {
          addresses: false,
          portfolios: false,
          subscription: true
        }),
        transaction.populate("orders")
      ]);

      const plan = (owner.subscription as SubscriptionDocument).plan;

      const latestFXRates = {
        BUY: await TransactionService.getAllForeignCurrencyRatesWithSpread(owner, plan, FXSpreadSide.BUY),
        SELL: await TransactionService.getAllForeignCurrencyRatesWithSpread(owner, plan, FXSpreadSide.SELL)
      };

      const assetTransaction = TransactionService.fillClientDisplayFields(
        owner,
        transaction,
        investmentProducts,
        latestFXRates,
        {
          isCancellable: true,
          displayAmount: true,
          displayQuantity: true,
          displayExchangeRate: true,
          estimatedRealTimeCommission: true
        }
      ) as AssetTransactionDocument;

      if (populateForeignCurrencyRates) {
        const foreignCurrencyRates = await TransactionService._getOrdersForeignCurrencyRatesWithSpread(
          owner,
          transaction.orders,
          investmentProducts,
          plan,
          transaction.orders[0].side === "Buy" ? FXSpreadSide.BUY : FXSpreadSide.SELL
        );

        return {
          ...assetTransaction,
          foreignCurrencyRates
        } as AssetTransactionDocument;
      }

      return assetTransaction;
    } else return transaction;
  }

  public static async getWithdrawalCashTransaction(id: string): Promise<WithdrawalCashTransactionDocument> {
    return WithdrawalCashTransaction.findById(id);
  }

  public static async getWealthyhoodDividendByDepositId(
    depositId: string
  ): Promise<WealthyhoodDividendTransactionDocument> {
    return await WealthyhoodDividendTransaction.findOne({
      "deposit.providers.wealthkernel.id": depositId
    });
  }

  public static async updateWealthyhoodDividendDepositStatus(
    wealthyhoodDividend: WealthyhoodDividendTransactionDocument,
    newDepositStatus: BonusStatusType
  ): Promise<void> {
    if (wealthyhoodDividend.status === "Settled") {
      logger.warn(`Received event with status ${newDepositStatus} for WH dividend that is already settled!`, {
        module: "TransactionService",
        method: "updateWealthyhoodDividendDepositStatus",
        data: wealthyhoodDividend
      });

      return;
    }

    if (newDepositStatus === "Settled") {
      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        await WealthyhoodDividendTransaction.findByIdAndUpdate(
          wealthyhoodDividend.id,
          {
            settledAt: new Date(Date.now()),
            "deposit.providers.wealthkernel.status": "Settled"
          },
          { session }
        );

        // We convert the amount to whole currency from cents to update cash & send event
        const amount = Decimal.div(wealthyhoodDividend.consideration.amount, 100).toNumber();
        const portfolioId = wealthyhoodDividend.populated("portfolio")
          ? wealthyhoodDividend.portfolio._id.toString()
          : wealthyhoodDividend.portfolio.toString();

        await DbUtil.populateIfNotAlreadyPopulated(wealthyhoodDividend, TransactionPopulationFieldsEnum.OWNER);

        const owner = wealthyhoodDividend.owner as UserDocument;

        await PortfolioService.updateCashAvailability(portfolioId, owner.currency, amount, {
          session,
          available: true,
          settled: true
        });

        const PRICE_CONFIG = ConfigUtil.getPricing(owner.companyEntity);
        const plan = PRICE_CONFIG[wealthyhoodDividend.price].plan;

        eventEmitter.emit(events.transaction.wealthyhoodDividendSuccess.eventId, owner, {
          amount,
          currency: wealthyhoodDividend.consideration.currency,
          plan
        } as TrackWealthyhoodDividendPropertiesType);
      });
    } else if (newDepositStatus === "Rejected") {
      logger.error(`Wealthyhood dividend bonus payment was rejected for ${wealthyhoodDividend.id}`, {
        module: "TransactionService",
        method: "updateWealthyhoodDividendDepositStatus"
      });
    }
  }

  public static async cancelTransaction(
    id: string
  ): Promise<AssetTransactionDocument | RebalanceTransactionDocument> {
    const transaction: TransactionDocument = await TransactionService.getTransaction(id, {
      orders: true
    });

    const [user, investmentProducts] = await Promise.all([
      UserService.getUser(transaction.owner.toString(), { subscription: true }),
      InvestmentProductService.getInvestmentProductsDict("isin", true)
    ]);

    if (!transaction.getIsCancellable(user, investmentProducts)) {
      throw new BadRequestError(`Transaction ${id} is not cancellable`);
    }

    if (transaction.category === "AssetTransaction") {
      const [updatedTransaction, latestBuyFXRatesWithSpread, latestSellFXRatesWithSpread] = await Promise.all([
        TransactionService._cancelAssetTransaction(transaction as AssetTransactionDocument),
        TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
        TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL)
      ]);

      return TransactionService.fillClientDisplayFields(
        user,
        updatedTransaction,
        investmentProducts,
        {
          BUY: latestBuyFXRatesWithSpread,
          SELL: latestSellFXRatesWithSpread
        },
        {
          displayAmount: true,
          displayQuantity: true,
          executionWindow: true,
          displayExchangeRate: true,
          isCancellable: true
        }
      ) as AssetTransactionDocument;
    } else if (transaction.category === "RebalanceTransaction") {
      return TransactionService._cancelRebalanceTransaction(transaction as RebalanceTransactionDocument);
    }
  }

  /**
   * This method creates realtime execution charge transactions for any settled asset/rebalance transactions that do not
   * already have linked realtime executio charge transactions.
   */
  public static async chargeRealtimeExecutionForTransactions(): Promise<void> {
    const transactions: AssetTransactionDocument[] = await Transaction.find({
      $and: [
        { "fees.realtimeExecution": { $exists: true } },
        { "fees.realtimeExecution.amount": { $gt: 0 } },
        { settledAt: { $exists: true } },
        { settledAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 7) } }
      ],
      category: "AssetTransaction",
      status: "Settled"
    });

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      try {
        const existingCharge = await ChargeTransaction.findOne({
          chargeType: "realtimeExecution",
          linkedTransaction: transaction.id
        });

        if (existingCharge) {
          continue;
        }

        const user = await UserService.getUser(transaction.owner.toString());

        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount: Decimal.mul(transaction.fees.realtimeExecution.amount, 100).toNumber() // amount is stored in cents
          },
          owner: user.id,
          portfolio: transaction.portfolio as mongoose.Types.ObjectId,
          chargeMethod: "orders",
          chargeType: "realtimeExecution",
          linkedTransaction: transaction._id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        await TransactionService.createChargeTransaction(transactionData);
      } catch (err) {
        captureException(err);
        logger.error(`Realtime execution charge creation failed for ${transaction._id}`, {
          module: "TransactionService",
          method: "chargeRealtimeExecutionForTransactions",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  /**
   * This method creates commission charge transactions for any settled asset/rebalance transactions that do not
   * already have linked commission charge transactions.
   */
  public static async chargeCommissionsForTransactions(): Promise<void> {
    const transactions: (AssetTransactionDocument | RebalanceTransactionDocument)[] = await Transaction.find({
      $and: [
        { "fees.commission": { $exists: true } },
        { "fees.commission.amount": { $gt: 0 } },
        { settledAt: { $exists: true } },
        { settledAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 7) } }
      ],
      category: { $in: ["AssetTransaction", "RebalanceTransaction"] },
      $or: [{ status: "Settled" }, { rebalanceStatus: "Settled" }]
    });

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      try {
        const existingCharge = await ChargeTransaction.findOne({
          chargeType: "commission",
          linkedTransaction: transaction.id
        });

        if (existingCharge) {
          continue;
        }

        const user = await UserService.getUser(transaction.owner.toString());

        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount: Decimal.mul(transaction.fees.commission.amount, 100).toNumber() // amount is stored in cents
          },
          owner: user.id,
          portfolio: transaction.portfolio as mongoose.Types.ObjectId,
          chargeMethod: "orders",
          chargeType: "commission",
          linkedTransaction: transaction._id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        await TransactionService.createChargeTransaction(transactionData);
      } catch (err) {
        captureException(err);
        logger.error(`Commission charge creation failed for ${transaction._id}`, {
          module: "TransactionService",
          method: "chargeCommissionsForTransactions",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  /**
   * This method creates execution spread charge transactions for any settled asset/rebalance transactions that do not
   * already have linked execution spread charge transactions.
   */
  public static async chargeExecutionSpreadForTransactions(): Promise<void> {
    const transactions: (AssetTransactionDocument | RebalanceTransactionDocument)[] = await Transaction.find({
      $and: [
        { "fees.executionSpread": { $exists: true } },
        { "fees.executionSpread.amount": { $gt: 0 } },
        { settledAt: { $exists: true } },
        { settledAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 7) } }
      ],
      category: { $in: ["AssetTransaction", "RebalanceTransaction"] },
      $or: [{ status: "Settled" }, { rebalanceStatus: "Settled" }]
    });

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      try {
        const existingCharge = await ChargeTransaction.findOne({
          chargeType: "executionSpread",
          linkedTransaction: transaction.id
        });

        if (existingCharge) {
          continue;
        }

        const user = await UserService.getUser(transaction.owner.toString());

        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount: Decimal.mul(transaction.fees.executionSpread.amount, 100).toNumber() // amount is stored in cents
          },
          owner: user.id,
          portfolio: transaction.portfolio as mongoose.Types.ObjectId,
          chargeMethod: "orders",
          chargeType: "executionSpread",
          linkedTransaction: transaction._id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        await TransactionService.createChargeTransaction(transactionData);
      } catch (err) {
        captureException(err);
        logger.error(`Execution spread charge creation failed for ${transaction._id}`, {
          module: "TransactionService",
          method: "chargeExecutionSpreadForTransactions",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  /**
   * This method creates fx charge transactions for any settled asset/rebalance transactions that do not
   * already have linked fx charge transactions.
   */
  public static async chargeFxFeesForTransactions(): Promise<void> {
    const transactions: (AssetTransactionDocument | RebalanceTransactionDocument)[] = await Transaction.find({
      "fees.fx.amount": { $gt: 0 },
      category: { $in: ["AssetTransaction", "RebalanceTransaction"] },
      $and: [{ settledAt: { $exists: true } }, { settledAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 7) } }],
      $or: [{ status: "Settled" }, { rebalanceStatus: "Settled" }]
    });

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      try {
        const existingCharge = await ChargeTransaction.findOne({
          chargeType: "fx",
          linkedTransaction: transaction.id
        });

        if (existingCharge) {
          continue;
        }

        const user = await UserService.getUser(transaction.owner.toString());

        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount: Decimal.mul(transaction.fees.fx.amount, 100).toNumber() // amount is stored in cents
          },
          owner: user.id,
          portfolio: transaction.portfolio as mongoose.Types.ObjectId,
          chargeMethod: "orders",
          chargeType: "fx",
          linkedTransaction: transaction._id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        await TransactionService.createChargeTransaction(transactionData);
      } catch (err) {
        captureException(err);
        logger.error(`Fx charge creation failed for ${transaction._id}`, {
          module: "TransactionService",
          method: "chargeFxFeesForTransactions",
          data: {
            transactionId: transaction._id,
            userId: transaction.owner as mongoose.Types.ObjectId,
            error: err
          }
        });
      }
    }
  }

  /**
   * This method creates commission charge transactions for any settled rewards that do not already have linked
   * commission charge transactions.
   */
  public static async chargeCommissionsForRewards(): Promise<void> {
    const rewards: RewardDocument[] = await Reward.aggregate([
      {
        $match: {
          $and: [
            { "fees.commission": { $exists: true } },
            { "fees.commission.amount": { $gt: 0 } },
            { updatedAt: { $exists: true } },
            { updatedAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 7) } }
          ],
          "order.providers.wealthkernel.status": "Matched"
        }
      },
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "linkedReward",
          pipeline: [
            {
              $match: {
                chargeType: "commission"
              }
            }
          ],
          as: "commissionCharge"
        }
      },
      {
        $match: {
          commissionCharge: { $size: 0 }
        }
      }
    ]);

    for (let i = 0; i < rewards.length; i++) {
      const reward = rewards[i];
      try {
        const user = await UserService.getUser(reward.targetUser.toString());
        const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(user);
        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount: Decimal.mul(reward.fees.commission.amount, 100).toNumber() // amount is stored in cents
          },
          owner: user.id,
          portfolio: portfolio.id,
          chargeMethod: "orders",
          chargeType: "commission",
          linkedReward: reward._id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        await TransactionService.createChargeTransaction(transactionData);
      } catch (err) {
        captureException(err);
        logger.error(`Commission charge creation failed for ${reward.id}`, {
          module: "TransactionService",
          method: "chargeCommissionsForRewards",
          data: { rewardId: reward._id, userId: (reward.targetUser as UserDocument)._id, error: err }
        });
      }
    }
  }

  /**
   * This method creates execution spread charge transactions for any settled rewards that do not already have linked
   * execution spread charge transactions.
   */
  public static async chargeExecutionSpreadForRewards(): Promise<void> {
    const rewards: RewardDocument[] = await Reward.aggregate([
      {
        $match: {
          $and: [
            { "fees.executionSpread": { $exists: true } },
            { "fees.executionSpread.amount": { $gt: 0 } },
            { updatedAt: { $exists: true } },
            { updatedAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 7) } }
          ],
          "order.providers.wealthkernel.status": "Matched"
        }
      },
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "linkedReward",
          pipeline: [
            {
              $match: {
                chargeType: "executionSpread"
              }
            }
          ],
          as: "executionSpreadCharge"
        }
      },
      {
        $match: {
          executionSpreadCharge: { $size: 0 }
        }
      }
    ]);

    for (let i = 0; i < rewards.length; i++) {
      const reward = rewards[i];
      try {
        const user = await UserService.getUser(reward.targetUser.toString());
        const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(user);
        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount: Decimal.mul(reward.fees.executionSpread.amount, 100).toNumber() // amount is stored in cents
          },
          owner: user.id,
          portfolio: portfolio.id,
          chargeMethod: "orders",
          chargeType: "executionSpread",
          linkedReward: reward._id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        await TransactionService.createChargeTransaction(transactionData);
      } catch (err) {
        captureException(err);
        logger.error(`Execution spread charge creation failed for ${reward.id}`, {
          module: "TransactionService",
          method: "chargeExecutionSpreadForRewards",
          data: { rewardId: reward._id, userId: (reward.targetUser as UserDocument)._id, error: err }
        });
      }
    }
  }

  /**
   * This method creates fx charge transactions for any settled rewards that do not already have linked
   * fx charge transactions.
   */
  public static async chargeFxFeesForRewards(): Promise<void> {
    const rewards: RewardDocument[] = await Reward.aggregate([
      {
        $match: {
          "fees.fx.amount": { $gt: 0 },
          "order.providers.wealthkernel.status": "Matched",
          $and: [
            { updatedAt: { $exists: true } },
            { updatedAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 7) } }
          ]
        }
      },
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "linkedReward",
          pipeline: [
            {
              $match: {
                chargeType: "fx"
              }
            }
          ],
          as: "fxCharge"
        }
      },
      {
        $match: {
          fxCharge: { $size: 0 }
        }
      }
    ]);

    for (let i = 0; i < rewards.length; i++) {
      const reward = rewards[i];
      let user: UserDocument;
      try {
        user = await UserService.getUser(reward.targetUser.toString());

        const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(user);
        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount: Decimal.mul(reward.fees.fx.amount, 100).toNumber() // amount is stored in cents
          },
          owner: user.id,
          portfolio: portfolio.id,
          chargeMethod: "orders",
          chargeType: "fx",
          linkedReward: reward._id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        await TransactionService.createChargeTransaction(transactionData);
      } catch (err) {
        captureException(err);
        logger.error(`Fx charge creation failed for ${reward.id}`, {
          module: "TransactionService",
          method: "chargeFxFeesForRewards",
          data: { rewardId: reward._id, userId: user.id, error: err }
        });
      }
    }
  }

  /**
   * @returns the cashback amount for the user (in cents) given an order amount (in cents).
   * @param orderAmount
   * @param owner
   */
  public static async getCashbackAmount(orderAmount: number, owner: UserDocument): Promise<number> {
    const canReceiveCashback = await UserService.canReceiveCashback(owner);
    const amountIsCashbackEligible = Decimal.div(orderAmount, 100).greaterThanOrEqualTo(
      MINIMUM_AMOUNT_FOR_CASHBACK
    );

    if (!canReceiveCashback || !amountIsCashbackEligible) {
      return 0;
    }

    if (!owner.populated("subscription")) {
      await owner.populate("subscription");
    }

    const plan = owner.subscription.plan;

    return Decimal.mul(orderAmount, CASHBACK_RATES[plan]).round().toNumber();
  }

  public static async syncCustodyChargeTransactionsFromWK(): Promise<void> {
    const pendingChargeTransactions = await ChargeTransaction.find({
      chargeType: "custody",
      status: "Pending",
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("owner orders portfolio");

    // We create the owner dict to ensure there are no more than one pending custody charges for the same user
    const ownerDict: Record<string, ChargeTransactionDocument[]> = {};
    pendingChargeTransactions.forEach((transaction) => {
      const user = transaction.owner as UserDocument;
      if (!ownerDict[user.id]) {
        ownerDict[user.id] = [transaction];
      } else {
        throw new Error(`There are more than one pending custody charges for user ${user.id}`);
      }
    });

    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("isin", true, {
      listedOnly: false
    });

    for (let i = 0; i < pendingChargeTransactions.length; i++) {
      const transaction = pendingChargeTransactions[i];
      try {
        await TransactionService._syncChargeTransaction(transaction, investmentProductsDict);
      } catch (err) {
        captureException(err);
        logger.error(`Charge transaction syncing failed for ${transaction._id}`, {
          module: "TransactionService",
          method: "syncCustodyChargeTransactionsFromWK",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  public static async syncRevertRewardTransactionsFromWK(): Promise<void> {
    const pendingRevertRewardTransactions = await RevertRewardTransaction.find({
      status: "Pending",
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("owner orders portfolio reward");

    for (let i = 0; i < pendingRevertRewardTransactions.length; i++) {
      const transaction = pendingRevertRewardTransactions[i];
      try {
        await TransactionService._syncRevertRewardTransaction(transaction);
      } catch (err) {
        captureException(err);
        logger.error(`Revert reward transaction syncing failed for ${transaction._id}`, {
          module: "TransactionService",
          method: "syncRevertRewardTransactionsFromWK",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  public static async createAssetTransactionPendingDepositForRecurringTopUp(
    automation: TopUpAutomationDocument,
    collectionDate: Date
  ): Promise<void> {
    // The method is executed only when the automation is active and the activation date is at the past
    if (
      !automation.active ||
      (automation.initialiseAt && new Date(automation.initialiseAt).getTime() > Date.now())
    ) {
      logger.warn(
        `We just attempted to create a repeating investment & top-up for automation ${automation.id} but it hasn't been initialised yet`,
        {
          module: "TransactionService",
          method: "createAssetTransactionPendingDepositForRecurringTopUp",
          data: {
            automation: automation.id,
            active: automation.active,
            initialiseAt: automation.initialiseAt
          }
        }
      );
      return;
    }

    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.MANDATE)
    ]);

    const mandate = automation.mandate as MandateDocument;

    await DbUtil.populateIfNotAlreadyPopulated(mandate, MandatePopulationFieldsEnum.BANK_ACCOUNT);
    const bankAccount = mandate.bankAccount as BankAccountDocument;

    if (!bankAccount.isProviderActive) {
      logger.warn(
        `We cannot create a repeating top-up for automation ${automation.id} because the bank account is not provider active`,
        {
          module: "TransactionService",
          method: "createAssetTransactionPendingDepositForRecurringTopUp",
          data: {
            automation: automation.id,
            bankAccount: bankAccount.id
          }
        }
      );
      return;
    }

    const pendingDeposit = await TransactionService._createAutomatedDepositTransaction(automation, collectionDate);
    if (pendingDeposit.depositMethod !== DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER) {
      await TransactionService.emitDepositCreatedEvent(pendingDeposit, { triggeredByAutomation: true });
    }

    // We retrieve the portfolio document with populated tickers (necessary for creating orders)
    const populatedPortfolio = await PortfolioService.getPortfolio(automation.portfolio.toString(), true);
    if (!populatedPortfolio.isTargetAllocationSetup && !populatedPortfolio?.holdings?.length) {
      logger.warn(
        `We cannot invest repeating topup because portfolio ${populatedPortfolio.id} has no holdings or target allocation`,
        {
          module: "TransactionService",
          method: "createAssetTransactionPendingDepositForRecurringTopUp",
          data: {
            automation: automation.id,
            portfolio: populatedPortfolio.id
          }
        }
      );
      return;
    }

    // Choose which allocation method to use
    let allocationMethodToUse;
    const onlyHasRewardedHoldings = await PortfolioService.onlyHasRewardedHoldings(populatedPortfolio);
    const hasOnlyRewardedHoldingsOrNoHoldings = !populatedPortfolio.holdings.length || onlyHasRewardedHoldings;
    if (!populatedPortfolio.isTargetAllocationSetup) {
      allocationMethodToUse = PortfolioAllocationMethodEnum.HOLDINGS;
    } else if (hasOnlyRewardedHoldingsOrNoHoldings) {
      allocationMethodToUse = PortfolioAllocationMethodEnum.TARGET_ALLOCATION;
    } else {
      allocationMethodToUse = automation.allocationMethod ?? PortfolioAllocationMethodEnum.HOLDINGS;
    }

    // After creating the deposit, we create a portfolio buy transaction pending that deposit.
    await PortfolioService.buyAssetsForPortfolio(
      populatedPortfolio,
      Decimal.div(automation.consideration.amount, 100).toNumber(),
      {
        allocationMethod: allocationMethodToUse,
        pendingDeposit,
        linkedAutomation: automation,
        executeEtfOrdersInRealtime: false
      }
    );
  }

  public static async createSavingsTransactionPendingDepositForRecurringTopUp(
    automation: SavingsTopUpAutomationDocument,
    collectionDate: Date
  ): Promise<void> {
    // The method is executed only when the automation is active and the activation date is at the past
    if (!automation.active) {
      logger.warn(
        `We just attempted to create a repeating savings & top-up for automation ${automation.id} but it is not active!`,
        {
          module: "TransactionService",
          method: "createSavingsTransactionPendingDepositForRecurringTopUp",
          data: {
            automation: automation.id,
            active: automation.active
          }
        }
      );
      return;
    }

    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.MANDATE),
      DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.PORTFOLIO)
    ]);

    const pendingDeposit = await TransactionService._createAutomatedDepositTransaction(automation, collectionDate);
    if (pendingDeposit.depositMethod !== DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER) {
      await TransactionService.emitDepositCreatedEvent(pendingDeposit, { triggeredByAutomation: true });
    }

    await PortfolioService.topupSavings(
      automation.portfolio as PortfolioDocument,
      automation.savingsProduct,
      Decimal.div(automation.consideration.amount, 100).toNumber(),
      {
        linkedAutomation: automation,
        pendingDeposit
      }
    );
  }

  public static async createWealthyhoodDividendDeposits() {
    const wealthyhoodDividendsMissingDeposit = await WealthyhoodDividendTransaction.find({
      $or: [
        { "deposit.providers.wealthkernel.id": { $exists: false } },
        { "deposit.providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("portfolio");

    for (let i = 0; i < wealthyhoodDividendsMissingDeposit.length; i++) {
      const wealthyhoodDividend = wealthyhoodDividendsMissingDeposit[i];
      await TransactionService._createWealthyhoodDividendDeposit(wealthyhoodDividend);
    }
  }

  public static async syncWealthyhoodDividendDeposits() {
    // For WH dividends with a WK deposit id, sync wk status
    // that were createdAt at least 15 minutes ago, to act as a fallback to webhook mechanism
    const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

    const pendingWealthyhoodDividends: WealthyhoodDividendTransactionDocument[] =
      await WealthyhoodDividendTransaction.find({
        "deposit.providers.wealthkernel.id": { $exists: true, $ne: null },
        "deposit.providers.wealthkernel.status": { $ne: "Settled" },
        createdAt: { $lte: minimumCreationTime }
      }).populate("owner");

    for (let i = 0; i < pendingWealthyhoodDividends.length; i++) {
      const wealthyhoodDividend = pendingWealthyhoodDividends[i];
      try {
        const wkTransaction: BonusType = await ProviderService.getBrokerageService(
          (wealthyhoodDividend.owner as UserDocument).companyEntity
        ).retrieveBonus(wealthyhoodDividend.deposit.providers?.wealthkernel?.id);
        await TransactionService.updateWealthyhoodDividendDepositStatus(wealthyhoodDividend, wkTransaction.status);
      } catch (err) {
        captureException(err);
        logger.error(`Wealthyhood dividend deposit syncing failed for ${wealthyhoodDividend._id}`, {
          module: "TransactionService",
          method: "syncWealthyhoodDividendDeposits",
          data: {
            dividendId: wealthyhoodDividend._id,
            error: err
          }
        });
      }
    }
  }

  public static async syncWithdrawalStatusByWkWithdrawalId(
    wkWithdrawalId: string,
    newWithdrawalStatus: WithdrawalStatusType
  ): Promise<void> {
    if (newWithdrawalStatus === "Created") return;

    const withdrawal = await WithdrawalCashTransaction.findOne({
      "providers.wealthkernel.id": wkWithdrawalId,
      "providers.wealthkernel.status": { $in: ["Pending", "Active", "Cancelling"] }
    }).populate("owner");

    if (!withdrawal) return;

    const user = withdrawal.owner as UserDocument;

    try {
      const isSettled = newWithdrawalStatus === "Settled";
      const settledAt =
        isSettled && withdrawal.withdrawalMethod !== WithdrawalMethodEnum.WITH_INTERMEDIARY
          ? new Date(Date.now())
          : undefined;
      const withdrawalData = {
        "providers.wealthkernel.status": newWithdrawalStatus,
        "providers.wealthkernel.settledAt": isSettled ? new Date(Date.now()) : undefined,
        settledAt
      } as any;

      // If the type was 'Full', then we should update the consideration amount to reflect the one withdrawn by WK
      if (withdrawal.withdrawalRequestType === "Full" && newWithdrawalStatus === "Settled") {
        const response = await ProviderService.getBrokerageService(user.companyEntity).retrieveWithdrawal(
          withdrawal.providers?.wealthkernel?.id
        );
        const updatedWithdrawalAmount = Decimal.mul(response.paidOut.amount, 100).toNumber();
        if (updatedWithdrawalAmount !== withdrawal.consideration.amount) {
          logger.warn(`Consideration amount for full withdrawal ${withdrawal.id} has changed!`, {
            module: "TransactionService",
            method: "syncWithdrawalStatusByWkWithdrawalId",
            data: {
              originalAmount: withdrawal.consideration.amount,
              settledAmount: updatedWithdrawalAmount
            }
          });
        }

        withdrawalData.consideration = {
          amount: updatedWithdrawalAmount
        };
      }

      await WithdrawalCashTransaction.findByIdAndUpdate(withdrawal.id, withdrawalData);
    } catch (err) {
      captureException(err);
      logger.error(`Syncing with wealthkernel failed for withdrawal ${withdrawal._id}`, {
        module: "TransactionService",
        method: "syncWithdrawalStatusByWkWithdrawalId",
        data: { transactionId: withdrawal._id, userId: (withdrawal.owner as UserDocument)._id, error: err }
      });
    }
  }

  public static async createCashbackDeposits() {
    // We retrieve all cashbacks that do not have submitted bonuses.
    const cashbacksMissingDeposit: CashbackTransactionDocument[] = await CashbackTransaction.find({
      status: "Pending",
      $or: [
        {
          "deposit.providers.wealthkernel.id": {
            $exists: false
          }
        },
        {
          "deposit.providers.wealthkernel.id": {
            $eq: undefined
          }
        }
      ]
    }).populate([
      {
        path: "linkedAssetTransaction",
        populate: {
          path: "orders"
        }
      }
    ]);

    for (let i = 0; i < cashbacksMissingDeposit.length; i++) {
      const cashback = cashbacksMissingDeposit[i];

      try {
        const linkedAssetTransaction = cashback.linkedAssetTransaction as AssetTransactionDocument;
        const orders = linkedAssetTransaction.orders as OrderDocument[];

        // Before creating cashback deposits, we want to make sure that the linked asset transaction of the cashback
        // has all orders in a terminal state. We do not do this as part of the Mongo operation to avoid it being
        // very intensive.
        if (orders.every((order) => order.hasTerminalStatus || order.isSubmittedToBroker)) {
          await TransactionService._createCashbackDeposit(cashback);
        }
      } catch (err) {
        logger.error(`Could not create cashback bonus for ${cashback.id}`, {
          module: "TransactionService",
          method: "createCashbackDeposits"
        });
        captureException(err);
      }
    }
  }

  public static async syncCashbackDeposits() {
    // For cashbacks with a WK deposit id, sync wk status that were createdAt at least 15 minutes ago, to act as a
    // fallback to webhook mechanism
    const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

    const pendingCashbacks: CashbackTransactionDocument[] = await CashbackTransaction.find({
      "deposit.providers.wealthkernel.id": { $exists: true, $ne: null },
      "deposit.providers.wealthkernel.status": { $ne: "Settled" },
      createdAt: { $lte: minimumCreationTime }
    }).populate("owner");

    for (let i = 0; i < pendingCashbacks.length; i++) {
      const cashback = pendingCashbacks[i];
      try {
        const wkTransaction = await ProviderService.getBrokerageService(
          (cashback.owner as UserDocument).companyEntity
        ).retrieveBonus(cashback.deposit.providers?.wealthkernel?.id);
        await TransactionService.updateCashbackDepositStatus(cashback, wkTransaction.status);
      } catch (err) {
        captureException(err);
        logger.error(`Cashback deposit syncing failed for ${cashback._id}`, {
          module: "TransactionService",
          method: "syncCashbackDeposits",
          data: {
            cashbackId: cashback._id,
            error: err
          }
        });
      }
    }
  }

  public static async getDepositByTruelayerId(truelayerId: string): Promise<DepositCashTransactionDocument> {
    return DepositCashTransaction.findOne({
      "providers.truelayer.id": truelayerId
    });
  }

  public static async getDepositBySaltedgeCustomId(
    saltedgeCustomId: string
  ): Promise<DepositCashTransactionDocument> {
    return DepositCashTransaction.findOne({
      "providers.saltedge.customId": saltedgeCustomId
    });
  }

  public static async getDirectDebitDepositsByGoCardlessIds(
    goCardlessIds: string[]
  ): Promise<DepositCashTransactionDocument[]> {
    return DepositCashTransaction.find({
      "directDebit.providers.gocardless.id": { $in: goCardlessIds }
    });
  }

  public static async getDepositByBankReference(reference: string): Promise<DepositCashTransactionDocument> {
    return DepositCashTransaction.findOne({
      bankReference: reference
    });
  }

  public static async getDepositByIncomingPaymentDevengoId(
    devengoId: string,
    stage: TransferWithIntermediaryStageEnum
  ): Promise<DepositCashTransactionDocument> {
    const path = `transferWithIntermediary.${stage}.incomingPayment.providers.devengo.id`;

    return DepositCashTransaction.findOne({
      [path]: devengoId
    });
  }

  public static async getWithdrawalByIncomingPaymentDevengoId(
    devengoId: string,
    stage: TransferWithIntermediaryStageEnum
  ): Promise<WithdrawalCashTransactionDocument> {
    const path = `transferWithIntermediary.${stage}.incomingPayment.providers.devengo.id`;

    return WithdrawalCashTransaction.findOne({
      [path]: devengoId
    });
  }

  public static async getDepositByOutgoingPaymentDevengoId(
    devengoId: string,
    stage: TransferWithIntermediaryStageEnum
  ): Promise<DepositCashTransactionDocument> {
    const path = `transferWithIntermediary.${stage}.outgoingPayment.providers.devengo.id`;

    return DepositCashTransaction.findOne({
      [path]: devengoId
    });
  }

  public static async getWithdrawalByOutgoingPaymentDevengoId(
    devengoId: string,
    stage: TransferWithIntermediaryStageEnum
  ): Promise<WithdrawalCashTransactionDocument> {
    const path = `transferWithIntermediary.${stage}.outgoingPayment.providers.devengo.id`;

    return WithdrawalCashTransaction.findOne({
      [path]: devengoId
    });
  }

  public static async getChargeByTruelayerId(truelayerId: string): Promise<ChargeTransactionDocument> {
    return ChargeTransaction.findOne({
      "providers.truelayer.id": truelayerId
    });
  }

  public static async getChargeByWealthkernelId(wealthkernelId: string): Promise<ChargeTransactionDocument> {
    return ChargeTransaction.findOne({
      "providers.wealthkernel.id": wealthkernelId
    });
  }

  public static async getRevertRewardByWealthkernelId(
    wealthkernelId: string
  ): Promise<RevertRewardTransactionDocument> {
    return RevertRewardTransaction.findOne({
      "providers.wealthkernel.id": wealthkernelId
    });
  }

  public static async getChargeByStripeId(stripeId: string): Promise<ChargeTransactionDocument> {
    return ChargeTransaction.findOne({
      "providers.stripe.id": stripeId
    });
  }

  public static async getCashbackByDepositId(depositId: string): Promise<CashbackTransactionDocument> {
    return CashbackTransaction.findOne({
      "deposit.providers.wealthkernel.id": depositId
    });
  }

  public static async getDepositByWealthkernelId(wealthkernelId: string): Promise<DepositCashTransactionDocument> {
    return DepositCashTransaction.findOne({
      "providers.wealthkernel.id": wealthkernelId
    });
  }

  public static async rejectFailedStripeCharges(): Promise<void> {
    const chargeTransactionsToReject = await ChargeTransaction.find({
      status: "Pending",
      chargeMethod: "card",
      createdAt: {
        $lte: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 8)
      }
    });

    for (let i = 0; i < chargeTransactionsToReject.length; i++) {
      const charge = chargeTransactionsToReject[i];

      logger.info(`Rejecting ${charge.id} because it's been pending for 8 days or more!`, {
        module: "TransactionService",
        method: "rejectFailedStripeCharges"
      });

      await ChargeTransaction.findByIdAndUpdate(charge.id, { status: "Rejected" });
    }
  }

  public static async updateCashbackDepositStatus(
    cashback: CashbackTransactionDocument,
    newDepositStatus: BonusStatusType
  ): Promise<void> {
    if (cashback.status === "Settled") {
      logger.warn(`Received event with status ${newDepositStatus} for cashback that is already settled!`, {
        module: "TransactionService",
        method: "updateWealthyhoodDividendDepositStatus",
        data: cashback
      });

      return;
    }

    if (newDepositStatus === "Settled") {
      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        const updatedCashback = await CashbackTransaction.findByIdAndUpdate(
          cashback.id,
          {
            settledAt: new Date(Date.now()),
            status: "Settled",
            "deposit.providers.wealthkernel.status": "Settled"
          },
          { new: true, session }
        ).populate("owner");

        // We convert the amount to GBP from cents to update cash & send event
        const amount = Decimal.div(cashback.consideration.amount, 100).toNumber();
        const owner = updatedCashback.owner as UserDocument;
        const portfolioId = cashback.populated("portfolio")
          ? cashback.portfolio._id.toString()
          : cashback.portfolio.toString();

        await PortfolioService.updateCashAvailability(portfolioId, owner.currency, amount, {
          session,
          available: true,
          settled: true
        });
      });
    } else if (newDepositStatus === "Rejected") {
      logger.error(`Cashback bonus payment was rejected for ${cashback.id}`, {
        module: "TransactionService",
        method: "updateCashbackDepositStatus"
      });
    }
  }

  /**
   * Syncs the status from TrueLayer to the local deposit document.
   * @param truelayerId
   * @returns The transaction that was updated. If no transaction was found for that
   * payment ID (in TrueLayer or locally), returns undefined.
   */
  public static async syncDepositTruelayerStatus(
    truelayerId: string
  ): Promise<ApiResponse<DepositCashTransactionDocument>> {
    const { status, failureReason, executedAt } = await ProviderService.getOpenBankingPaymentService(
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
    ).getOpenBankingDeposit(truelayerId);

    const updatedDeposit = await TransactionService.updateDepositTruelayerData(truelayerId, {
      status: status as PaymentStatusTypeV3,
      failureReason,
      executedAt
    });

    return {
      data: [updatedDeposit]
    };
  }

  /**
   * Syncs the status from TrueLayer to the local charge transaction document.
   * @param truelayerId
   * @returns The transaction that was updated. If no transaction was found for that
   * payment ID (in TrueLayer or locally), returns undefined.
   */
  public static async syncLifetimeChargeTruelayerStatus(
    truelayerId: string
  ): Promise<ApiResponse<ChargeTransactionDocument>> {
    // 1. Retrieve payment from truelayer
    const { status, failureReason, executedAt } =
      await ProviderService.getOpenBankingPaymentService().getOpenBankingDeposit(truelayerId);

    const updatedCharge = await TransactionService.updateLifetimeChargeTruelayerData(truelayerId, {
      status: status as PaymentStatusTypeV3,
      failureReason,
      executedAt
    });

    return {
      data: [updatedCharge]
    };
  }

  /**
   * Creates Wealthkernel withdrawal requests for all withdrawals that have not been submitted to WK yet.
   */
  public static async createMissingWealthkernelWithdrawals() {
    const unsubmittedWkWithdrawals = await WithdrawalCashTransaction.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("portfolio bankAccount");

    for (const withdrawal of unsubmittedWkWithdrawals) {
      await TransactionService._requestWealthkernelWithdrawalSafely(withdrawal);
    }
  }

  /**
   * Syncs withdrawals with Wealthkernel.
   */
  public static async syncPendingWealthkernelWithdrawals(): Promise<void> {
    // Sync withdrawals that were createdAt at least 15 minutes ago, to act as a fallback to webhook mechanism
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

    const openWithdrawals = await WithdrawalCashTransaction.find({
      "providers.wealthkernel.status": { $in: ["Pending", "Active", "Cancelling"] },
      "providers.wealthkernel.id": { $exists: true },
      createdAt: { $lt: fifteenMinutesAgo }
    }).populate("portfolio");

    for (const withdrawal of openWithdrawals) {
      await TransactionService._syncPendingWealthkernelWithdrawalSafely(withdrawal);
    }
  }

  /**
   * @description Creates acquisition Devengo payments for deposits that have confirmed incoming payments.
   */
  public static async createAcquisitionPaymentsForEligibleDevengoDeposits(): Promise<void> {
    const depositsWithConfirmedIncomingPayments = await DepositCashTransaction.find({
      "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.status": "confirmed",
      $or: [
        { "transferWithIntermediary.acquisition.outgoingPayment.providers.devengo.id": { $exists: false } },
        { "transferWithIntermediary.acquisition.outgoingPayment.providers.devengo.id": { $eq: undefined } }
      ]
    });

    const promises = depositsWithConfirmedIncomingPayments.map(async (deposit) => {
      try {
        await TransactionService.createBankTransferPaymentAndUpdateDeposit(
          deposit,
          TransferWithIntermediaryStageEnum.ACQUISITION
        );
      } catch (err) {
        captureException(err);
        logger.error(`Devengo acquisition bank transfer creation failed for deposit ${deposit._id}`, {
          module: "TransactionService",
          method: "createAcquisitionPaymentsForEligibleDevengoDeposits",
          data: { transactionId: deposit._id, userId: (deposit.owner as UserDocument)._id, error: err }
        });
      }
    });
    await Promise.all(promises);
  }

  /**
   * @description Creates collection Devengo payments to Wealthkernel for deposits that have confirmed incoming
   * payments.
   */
  public static async createCollectionPaymentsForEligibleDevengoDeposits(): Promise<void> {
    const depositsWithConfirmedIncomingPayments = await DepositCashTransaction.find({
      "transferWithIntermediary.collection.incomingPayment.providers.devengo.status": "confirmed",
      $or: [
        { "transferWithIntermediary.collection.outgoingPayment.providers.devengo.id": { $exists: false } },
        { "transferWithIntermediary.collection.outgoingPayment.providers.devengo.id": { $eq: undefined } }
      ]
    });

    const promises = depositsWithConfirmedIncomingPayments.map(async (deposit) => {
      try {
        await TransactionService.createBankTransferPaymentAndUpdateDeposit(
          deposit,
          TransferWithIntermediaryStageEnum.COLLECTION
        );
      } catch (err) {
        captureException(err);
        logger.error(`Devengo collection bank transfer creation failed for deposit ${deposit._id}`, {
          module: "TransactionService",
          method: "createCollectionPaymentsForEligibleDevengoDeposits",
          data: { transactionId: deposit._id, userId: (deposit.owner as UserDocument)._id, error: err }
        });
      }
    });
    await Promise.all(promises);
  }

  /**
   * @description Creates Devengo payments to user bank accounts for withdrawals that have confirmed incoming
   * payments.
   */
  public static async createCollectionPaymentsForEligibleDevengoWithdrawals(): Promise<void> {
    const withdrawalsWithConfirmedIncomingPayments = await WithdrawalCashTransaction.find({
      "transferWithIntermediary.collection.incomingPayment.providers.devengo.status": "confirmed",
      $or: [
        { "transferWithIntermediary.collection.outgoingPayment.providers.devengo.id": { $exists: false } },
        { "transferWithIntermediary.collection.outgoingPayment.providers.devengo.id": { $eq: undefined } }
      ]
    });

    const promises = withdrawalsWithConfirmedIncomingPayments.map(async (withdrawal) => {
      try {
        await TransactionService._createCollectionBankTransferPaymentAndUpdateWithdrawal(withdrawal);
      } catch (err) {
        captureException(err);
        logger.error(`Wealthkernel expectation creation failed for withdrawal ${withdrawal._id}`, {
          module: "TransactionService",
          method: "createCollectionPaymentsForEligibleDevengoWithdrawals",
          data: { transactionId: withdrawal._id, userId: (withdrawal.owner as UserDocument)._id, error: err }
        });
      }
    });
    await Promise.all(promises);
  }

  /**
   * @description Creates wealthkernel expectations for deposits:
   * 1) With truelayer status 'executed' and without Wealthkernel ID.
   * 2) With Devengo outgoing payments and without Wealthkernel ID.
   * 3) With GoCardless direct debit and Devengo outgoing payments and without Wealthkernel ID.
   */
  public static async createEligibleWealthkernelDeposits(): Promise<void> {
    // We only create deposit expectations for deposits, that their truelayer payment executed
    //  at least 15 minutes ago, to act as a fallback to Truelayer Webhook
    const minimumExecutionTime = new Date(Date.now() - 15 * 60 * 1000);

    // Create wealthkernel expectation if status is executed
    const depositsPendingWkExpectation = await DepositCashTransaction.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      "providers.wealthkernel.id": { $exists: false },
      $or: [
        {
          depositMethod: DepositMethodEnum.OPEN_BANKING,
          "providers.truelayer.status": "executed",
          "providers.truelayer.executedAt": { $lte: minimumExecutionTime }
        },
        {
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          "transferWithIntermediary.collection.outgoingPayment.providers.devengo.id": { $exists: true }
        },
        {
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          "directDebit.providers.gocardless.id": { $exists: true },
          "transferWithIntermediary.collection.outgoingPayment.providers.devengo.id": { $exists: true }
        }
      ]
    });
    const pendingWkExpectationPromises = depositsPendingWkExpectation.map(
      TransactionService._createWkDepositExpectationSafely
    );
    await Promise.all(pendingWkExpectationPromises);
  }

  /**
   * @description Syncs wealthkernel direct debit payments for all deposits with a linked automation that do have a
   * direct debit payment submitted to WK.
   */
  public static async syncWealthkernelDirectDebitPayments(): Promise<void> {
    const depositsWithPendingWkPayment: DepositCashTransactionDocument[] = await DepositCashTransaction.find({
      linkedAutomation: { $exists: true },
      "directDebit.providers.wealthkernel.status": { $in: ["Pending", "Collecting", "Collected"] }
    }).populate({
      path: "linkedAutomation",
      populate: {
        path: "mandate"
      }
    });

    for (let i = 0; i < depositsWithPendingWkPayment.length; i++) {
      const directDebitDeposit = depositsWithPendingWkPayment[i];

      await TransactionService._syncWkDepositDirectDebitPaymentSafely(directDebitDeposit);
    }
  }

  /**
   * @description Processes all pending rebalance transactions.
   */
  public static async processRebalanceTransactions(): Promise<void> {
    const pendingRebalanceTransactions = (await TransactionService.getRebalanceTransactions({
      statuses: ["Pending"]
    })) as RebalanceTransactionDocument[];

    for (let i = 0; i < pendingRebalanceTransactions.length; i++) {
      const rebalanceTransaction = pendingRebalanceTransactions[i];

      await TransactionService._processPendingRebalanceTransaction(rebalanceTransaction);
    }
  }

  /**
   * @description  Sync all deposit with non-terminal truelayer state with truelayer.
   */
  public static async syncPendingTruelayerDeposits(): Promise<void> {
    // For deposits with non-terminal truelayer state fetch new truelayer status
    const pendingTruelayerDeposits = await DepositCashTransaction.find({
      "providers.truelayer.status": {
        $in: ["authorization_required", "authorizing", "authorized"] as PaymentStatusTypeV3[]
      },
      "providers.truelayer.version": "v3",
      createdAt: { $lte: DateUtil.getDateOfMinutesAgo(5) }
    });

    const pendingTruelayerPromises = pendingTruelayerDeposits.map((deposit) =>
      TransactionService._syncDepositTruelayerStatusSafely(deposit)
    );
    await Promise.all(pendingTruelayerPromises);
  }

  /**
   * @description Syncs Wealthkernel deposits that are pending and have been created at least 15 minutes ago.
   *
   * For deposits that are not open banking or bonus, we do not want to sync the status from Wealthkernel,
   * as we rely only on Wealthkernel webhooks to update the status of these deposits.
   */
  public static async syncPendingWealthkernelDeposits(): Promise<void> {
    const pendingWKDeposits = await DepositCashTransaction.find({
      status: "Pending",
      "providers.wealthkernel.status": { $ne: "Settled" },
      "providers.wealthkernel.id": { $exists: true },
      createdAt: { $lte: DateUtil.getDateOfMinutesAgo(15) },
      depositMethod: { $in: [DepositMethodEnum.OPEN_BANKING, DepositMethodEnum.BONUS] }
    });

    for (const deposit of pendingWKDeposits) {
      await TransactionService.syncDepositWkStatusSafely(deposit);
    }
  }

  /**
   * @description  Sync all charges with non-terminal truelayer state with truelayer.
   */
  public static async syncPendingTruelayerCharges(): Promise<void> {
    // For deposits with non-terminal truelayer state fetch new truelayer status
    const pendingTruelayerCharges = await ChargeTransaction.find({
      "providers.truelayer.status": {
        $in: ["authorization_required", "authorizing", "authorized"] as PaymentStatusTypeV3[]
      }
    });

    const pendingTruelayerPromises = pendingTruelayerCharges.map((charge) =>
      TransactionService._syncChargeTruelayerStatusSafely(charge)
    );
    await Promise.all(pendingTruelayerPromises);
  }

  public static async convertUsersThatHadTransactionsPendingDeposit() {
    logger.info("Starting converting users that had transactions pending deposits...", {
      module: "TransactionService",
      method: "convertUsersThatHadTransactionsPendingDeposits"
    });

    const transactionsThatWerePendingDeposits = await AssetTransaction.find({
      pendingDeposit: { $exists: true, $ne: null },
      status: { $eq: "Pending" }
    }).populate("owner");

    const updatePromises = transactionsThatWerePendingDeposits
      .filter(({ id, owner }) => {
        const ownerExists = Boolean(owner);
        if (!ownerExists) {
          logger.error(`Populated Owner is null for transaction ${id}`, {
            module: "TransactionService",
            method: "convertUsersThatHadTransactionsPendingDeposits"
          });
        }
        return ownerExists;
      })
      .filter(({ owner }) => (owner as UserDocument).portfolioConversionStatus === "notStarted")
      .map((transaction) => {
        const user = transaction.owner as UserDocument;

        logger.info(
          `Will update user ${user._id} because their transaction ${transaction._id} was ${transaction.status}`,
          {
            module: "TransactionService",
            method: "convertUsersThatHadTransactionsPendingDeposits"
          }
        );

        return User.findByIdAndUpdate(user._id, { portfolioConversionStatus: "inProgress" });
      });

    await Promise.all(updatePromises);

    logger.info("Completed converting users that had transactions pending deposits...", {
      module: "TransactionService",
      method: "convertUsersThatHadTransactionsPendingDeposits"
    });
  }

  public static async syncTransactionsPendingGifts() {
    logger.info("Starting syncing transactions that are pending gifts...", {
      module: "TransactionService",
      method: "syncTransactionsPendingGifts"
    });

    const transactionsPendingOnSettledGifts: AssetTransactionDocument[] = await AssetTransaction.aggregate()
      .match({
        status: "PendingGift"
      })
      .lookup({
        from: "gifts",
        localField: "pendingGift",
        foreignField: "_id",
        as: "lookedUpPendingGift"
      })
      .match({ "lookedUpPendingGift.deposit.providers.wealthkernel.status": "Settled" });

    for (const transaction of transactionsPendingOnSettledGifts) {
      logger.info(`Will update transaction ${transaction._id} since pending gift was settled`, {
        module: "TransactionService",
        method: "syncTransactionsPendingGifts"
      });

      const updatedTransaction = await AssetTransaction.findByIdAndUpdate(
        transaction._id,
        { status: "Pending" },
        { new: true }
      ).populate("owner pendingGift portfolio cashback orders");
      const portfolio = updatedTransaction.portfolio as PortfolioDocument;
      const owner = updatedTransaction.owner as UserDocument;

      const isFirstInvestment = await UserService.userHasSingleInvestment(owner.id);

      if (isFirstInvestment) {
        eventEmitter.emit(events.transaction.firstInvestmentCreation.eventId, owner);
      }

      await TransactionService._emitInvestmentCreationEvent(updatedTransaction);

      await OrderService.submitRealtimeOrdersSafely(updatedTransaction.orders, portfolio);
    }

    logger.info("Completed syncing transactions that are pending gifts...", {
      module: "TransactionService",
      method: "syncTransactionsPendingGifts"
    });
  }

  public static async convertUsersThatHadTransactionsPendingGift() {
    logger.info("Starting converting users that had transactions pending gifts...", {
      module: "TransactionService",
      method: "convertUsersThatHadTransactionsPendingGift"
    });

    const transactionsThatWerePendingGifts = await AssetTransaction.find({
      pendingGift: { $exists: true, $ne: null },
      status: { $eq: "Pending" }
    }).populate("owner");

    const updatePromises = transactionsThatWerePendingGifts
      .filter(({ id, owner }) => {
        const ownerExists = Boolean(owner);
        if (!ownerExists) {
          logger.error(`Populated Owner is null for transaction ${id}`, {
            module: "TransactionService",
            method: "convertUsersThatHadTransactionsPendingGift"
          });
        }
        return ownerExists;
      })
      .filter(({ owner }) => (owner as UserDocument).portfolioConversionStatus === "notStarted")
      .map((transaction) => {
        const user = transaction.owner as UserDocument;

        logger.info(
          `Will update user ${user._id} because their transaction ${transaction._id} was ${transaction.status}`,
          {
            module: "TransactionService",
            method: "convertUsersThatHadTransactionsPendingGift"
          }
        );

        return User.findByIdAndUpdate(user._id, { portfolioConversionStatus: "inProgress" });
      });

    await Promise.all(updatePromises);

    logger.info("Completed converting users that had transactions pending deposits...", {
      module: "TransactionService",
      method: "convertUsersThatHadTransactionsPendingDeposits"
    });
  }

  public static async createBankTransferDepositTransaction(
    user: UserDocument,
    depositData: {
      amount: number;
      bankReference: string;
      bankAccountId: string;
      devengo: {
        id: string;
        status: IncomingPaymentStatusType;
        accountId: string;
      };
    },
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<DepositCashTransactionDocument> {
    const selectedBankAccount = await BankAccount.findOne(
      { _id: depositData.bankAccountId, owner: user._id },
      null,
      {
        session: options?.session
      }
    );
    if (!selectedBankAccount) {
      logger.error("Bank account cannot be found", {
        module: "TransactionService",
        method: "createBankTransferDepositTransaction",
        userEmail: user.email
      });
      throw new Error("The selected bank account was not found");
    }

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.PORTFOLIOS);
    const portfolio = user.portfolios[0] as PortfolioDocument;

    if (!portfolio) {
      logger.error(`User ${user.email} attempted to create a payment without having a real portfolio`, {
        module: "TransactionService",
        method: "createBankTransferDepositTransaction",
        userEmail: user.email
      });
      throw new Error("User has not REAL portfolio");
    }

    if (!user.isVerified) {
      throw new Error("User is not verified yet.");
    }

    const depositTransactionData = {
      bankReference: depositData.bankReference,
      owner: user.id,
      depositMethod: DepositMethodEnum.BANK_TRANSFER,
      portfolio: portfolio.id,
      consideration: {
        currency: "EUR", // Fow now, we only support EUR payments using bank transfers.
        amount: depositData.amount
      },
      activeProviders: ProviderService.getProviders(user.companyEntity, [
        ProviderScopeEnum.BANK_TRANSFER_PAYMENTS
      ]),
      transferWithIntermediary: {
        acquisition: {
          incomingPayment: {
            providers: {
              devengo: depositData.devengo
            }
          }
        }
      },
      bankAccount: depositData.bankAccountId,
      createdAt: new Date(),
      status: "Pending",
      depositAction: DepositActionEnum.JUST_PAY
    };

    return new DepositCashTransaction(depositTransactionData).save({ session: options?.session });
  }

  public static async createOpenBankingDepositTransaction(
    requestingUser: UserDocument,
    paymentAmount: number,
    bankAccountId: string,
    portfolioId: string = null,
    depositAction: DepositActionEnum,
    bankId: banksConfig.BankType,
    clientInfo: { platform?: PlatformCategoryEnum; version?: string } = {}
  ): Promise<{ paymentUri: string; depositId: string }> {
    let selectedBankAccount: BankAccountDocument;

    // If user has provided a bank account ID, we ensure it exists & belongs to the requesting user.
    if (bankAccountId) {
      selectedBankAccount = await BankAccount.findById(bankAccountId).populate("owner");

      if (!selectedBankAccount) {
        logger.error("Linked bank account cannot be found", {
          module: "TransactionService",
          method: "createOpenBankingDepositTransaction",
          userEmail: requestingUser.email
        });
        throw new BadRequestError("The selected bank account was not found");
      }

      const user = selectedBankAccount.owner as UserDocument;

      // Confirm that investors own the bank account they created the payment for
      if (user?._id?.toString() != requestingUser._id.toString()) {
        logger.error("User doesn't seem to own that bank account", {
          module: "TransactionService",
          method: "createOpenBankingDepositTransaction",
          userEmail: requestingUser.email
        });
        throw new BadRequestError("Bank account does not belong to user");
      }
    } else if (!bankId) {
      throw new BadRequestError("Either a bank account or bank ID must be passed!");
    }

    await DbUtil.populateIfNotAlreadyPopulated(requestingUser, UserPopulationFieldsEnum.PORTFOLIOS);

    if (!requestingUser.portfolios?.length) {
      logger.error(`User ${requestingUser.email} attempted to create a payment without having a REAL portfolio`, {
        module: "TransactionService",
        method: "createOpenBankingDepositTransaction",
        userEmail: requestingUser.email
      });
      throw new BadRequestError("User has not REAL portfolio");
    }

    // If the user has specified a portfolio, we want to deposit there. Otherwise, we deposit to their GIA portfolio.
    const portfolioToDeposit = portfolioId
      ? await PortfolioService.getPortfolio(portfolioId)
      : await PortfolioService.getGeneralInvestmentPortfolio(requestingUser);

    // Investors need to already have a portfolio created at wealthkernel in order to be able to make deposits.
    const wealthkernelPortfolioId = portfolioToDeposit?.providers?.wealthkernel?.id;
    if (!wealthkernelPortfolioId) {
      logger.error("Wealthkernel portfolio id cannot be found", {
        module: "TransactionService",
        method: "createOpenBankingDepositTransaction",
        userEmail: requestingUser.email
      });
      throw new BadRequestError(
        `User is not verified yet. Wealthkernel portfolio id is missing for portfolio '${portfolioToDeposit._id}'.`
      );
    }

    const paymentResponse = await ProviderService.getOpenBankingPaymentService(
      requestingUser.companyEntity
    ).createOpenBankingDeposit({
      amount: Decimal.mul(paymentAmount, 100).toNumber(),
      beneficiary: BeneficiaryEnum.WEALTHKERNEL,
      remitter: {
        bankId: selectedBankAccount?.bankId ?? bankId,
        name: selectedBankAccount?.name,
        number: selectedBankAccount?.number,
        sortCode: selectedBankAccount?.sortCode,
        providerId: selectedBankAccount?.truelayerProviderId as TruelayerPayProvidersType
      },
      user: {
        email: requestingUser.email,
        name: `${requestingUser.firstName} ${requestingUser.lastName}`,
        providers: requestingUser.providers
      },
      depositAction,
      clientInfo: { platform: clientInfo.platform, version: clientInfo.version }
    });

    const { paymentUri, providerData, bankReference } = paymentResponse;

    const depositTransactionData = {
      owner: requestingUser._id,
      portfolio: portfolioToDeposit._id,
      consideration: {
        currency: requestingUser.currency,
        amount: Decimal.mul(paymentAmount, 100).toNumber()
      },
      depositMethod: DepositMethodEnum.OPEN_BANKING,
      activeProviders: ProviderService.getProviders(requestingUser.companyEntity, [
        ProviderScopeEnum.BROKERAGE,
        ProviderScopeEnum.SINGLE_DEPOSIT_PAYMENTS
      ]),
      providers: providerData,
      bankAccount:
        requestingUser.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
          ? bankAccountId
          : undefined,
      createdAt: new Date(),
      status: "Pending",
      depositAction,
      bankReference
    };

    const deposit = await new DepositCashTransaction(depositTransactionData).save();

    return { paymentUri, depositId: deposit.id };
  }

  public static async createLifetimeSubscriptionChargeTransaction(
    requestingUser: UserDocument,
    price: plansConfig.PriceType,
    bankAccountId: string,
    clientInfo: { platform?: PlatformCategoryEnum; version?: string } = {}
  ): Promise<{ paymentUri: string }> {
    const selectedBankAccount = await BankAccount.findById(bankAccountId).populate("owner");
    const user = selectedBankAccount.owner as UserDocument;

    // Confirm that investors own the bank account they created the payment for
    if (user.id !== requestingUser.id) {
      throw new Error("Bank account does not belong to user");
    }

    const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(user);
    if (!portfolio) {
      throw new Error("User requesting lifetime subscription payment has no portfolio");
    }

    const currentSubscription = await SubscriptionService.getSubscription(user.id);
    if (!currentSubscription) {
      throw new Error("User requesting lifetime subscription payment has no subscription");
    }

    if (currentSubscription.price === price) {
      throw new Error(
        `User ${user.id} already has subscription ${currentSubscription.id} price ${price} but we're trying to create a lifetime payment for the same price`
      );
    }

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    const { amount, allowedSubscriptionCategories } = PRICE_CONFIG[price];

    if (!allowedSubscriptionCategories.includes("SinglePaymentSubscription")) {
      throw new Error("User is making lifetime subscription payment for a price that does not allow it");
    }

    const singleDepositPaymentService = ProviderService.getOpenBankingPaymentService();

    const paymentResponse = await singleDepositPaymentService.createOpenBankingDeposit({
      amount: Decimal.mul(amount, 100).toNumber(), // Amount has to be passed to truelayer in cents
      beneficiary: BeneficiaryEnum.WEALTHYHOOD,
      remitter: {
        name: selectedBankAccount.name,
        number: selectedBankAccount.number,
        sortCode: selectedBankAccount.sortCode,
        providerId: selectedBankAccount.truelayerProviderId as TruelayerPayProvidersType
      },
      user: {
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        providers: user.providers
      },
      depositAction: DepositActionEnum.LIFETIME_CHARGE,
      clientInfo: { platform: clientInfo.platform, version: clientInfo.version }
    });

    const { paymentUri, bankReference } = paymentResponse;

    const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
      consideration: {
        currency: user.currency,
        amount: Decimal.mul(amount, 100).toNumber() // Amount is stored in cents
      },
      owner: user.id,
      portfolio: portfolio.id,
      chargeMethod: "lifetime-payment",
      subscription: currentSubscription.id,
      bankAccount: new mongoose.Types.ObjectId(bankAccountId),
      bankReference,
      chargeType: "subscription",
      price,
      activeProviders: ProviderService.getProviders(user.companyEntity, [
        ProviderScopeEnum.SINGLE_DEPOSIT_CHARGES
      ]),
      providers: paymentResponse.providerData as TruelayerDepositCreationDataType
    };

    await TransactionService.createChargeTransaction(transactionData);

    return { paymentUri };
  }

  public static async createWealthkernelInvestmentDividends(): Promise<void> {
    const isinToAssetCommonIdDict: Record<string, investmentUniverseConfig.AssetType> = Object.fromEntries(
      Object.entries(ASSET_CONFIG).map(([key, config]) => [config.isin, key as investmentUniverseConfig.AssetType])
    );

    await WealthkernelUtil.onEachInstance(async (wealthkernelService) => {
      await wealthkernelService.listTransactions(
        {
          startDate: WealthkernelService.formatDate(
            DateUtil.getDateOfDaysAgo(new Date(), DIVIDENDS_TRACKING_DAYS)
          ),
          type: "Dividend",
          limit: 500
        },
        async (dividend): Promise<void> => {
          if (!isinToAssetCommonIdDict[dividend.isin]) return;

          if (dividend.consideration.amount > 0) {
            await TransactionService._createInvestmentDividend(dividend);
          }
        }
      );
    });
  }

  public static async createWealthkernelInvestmentAssetDividends(): Promise<void> {
    const isinToAssetCommonIdDict: Record<string, investmentUniverseConfig.AssetType> = Object.fromEntries(
      Object.entries(ASSET_CONFIG).map(([key, config]) => [config.isin, key as investmentUniverseConfig.AssetType])
    );

    await WealthkernelUtil.onEachInstance(async (wealthkernelService) => {
      await wealthkernelService.listTransactions(
        {
          startDate: WealthkernelService.formatDate(
            DateUtil.getDateOfDaysAgo(new Date(), ASSET_DIVIDENDS_TRACKING_DAYS)
          ),
          type: "Adjustment",
          limit: 500
        },
        async (assetDividend: TransactionType): Promise<void> => {
          if (!isinToAssetCommonIdDict[assetDividend.isin]) return;

          if (assetDividend.quantity > 0 && assetDividend.narrative.toLowerCase().includes("stock dividend")) {
            await TransactionService._createInvestmentAssetDividend(assetDividend);
          }
        }
      );
    });
  }

  /**
   * Returns a preview of a asset buy/sell transaction with the given order.
   *
   * @param portfolio
   * @param asset
   * @param pendingOrder
   */
  public static async getPortfolioUpdatePreview(
    portfolio: PortfolioDocument,
    asset: AssetType,
    pendingOrder: PendingOrderType
  ): Promise<TransactionPreview> {
    const user = portfolio.owner as UserDocument;

    const [investmentProduct, availableHoldings] = await Promise.all([
      InvestmentProductService.getInvestmentProduct(asset, true),
      PortfolioService.getAvailableHoldings(portfolio),
      DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION)
    ]);

    // Check if the order is valid
    const holdingsDict = Object.fromEntries(availableHoldings.map((holding) => [holding.assetCommonId, holding]));
    const order = PortfolioUtil.checkSubmittedOrder(user, asset, pendingOrder, holdingsDict, investmentProduct);
    if (!order) {
      throw new BadRequestError("The submitted order does not pass our investment criteria");
    }

    if (ASSET_CONFIG[asset].category === "stock") {
      return TransactionService._getStockPortfolioUpdatePreview(
        holdingsDict,
        asset,
        pendingOrder,
        user,
        investmentProduct
      );
    } else {
      return TransactionService._getETFPortfolioUpdatePreview(
        holdingsDict,
        asset,
        pendingOrder,
        user,
        investmentProduct
      );
    }
  }

  public static async getPortfolioBuyPreview(
    portfolio: PortfolioDocument,
    orderAmount: number,
    options: {
      allocationMethod: PortfolioAllocationMethodEnum;
    }
  ): Promise<TransactionPreview> {
    const user = portfolio.owner as UserDocument;

    // Get the buy orders to create
    const [investmentProductsDict] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION)
    ]);

    let holdingsPercentage: { [key in AssetType]?: number } = {};
    if (options.allocationMethod == PortfolioAllocationMethodEnum.TARGET_ALLOCATION) {
      portfolio.initialHoldingsAllocation.forEach((asset) => {
        if (asset.percentage > 0) {
          holdingsPercentage[asset.assetCommonId] = asset.percentage;
        }
      });
    } else {
      holdingsPercentage = PortfolioUtil.mapHoldingsToAllocationFormat(user.currency, portfolio.holdings).assets;
    }

    // Get orders for both express and smart execution
    const [expressOrders, smartOrders] = await Promise.all([
      OrderService.getBuyOrdersToCreate(
        portfolio,
        user.currency,
        holdingsPercentage,
        orderAmount,
        user.companyEntity,
        investmentProductsDict,
        true, // Express execution (realtime)
        options.allocationMethod
      ),
      OrderService.getBuyOrdersToCreate(
        portfolio,
        user.currency,
        holdingsPercentage,
        orderAmount,
        user.companyEntity,
        investmentProductsDict,
        false, // Smart execution (not realtime)
        options.allocationMethod
      )
    ]);

    // Apply fees to both scenarios
    const [expressOrdersWithFees, smartOrdersWithFees] = [
      OrderService.applyFeesToOrders(user.subscription.plan, expressOrders, user.currency, investmentProductsDict),
      OrderService.applyFeesToOrders(user.subscription.plan, smartOrders, user.currency, investmentProductsDict)
    ];

    // Aggregate fees for each execution method
    const [expressFees, smartFees] = [
      aggregateFees(
        expressOrdersWithFees.map((orderData) => orderData.fees),
        user.currency
      ),
      aggregateFees(
        smartOrdersWithFees.map((orderData) => orderData.fees),
        user.currency
      )
    ];

    // Get foreign currency rates if needed
    const foreignCurrencyRates = await TransactionService._getOrdersForeignCurrencyRatesWithSpread(
      user,
      expressOrders, // Using express orders (could use either)
      investmentProductsDict,
      user.subscription.plan,
      FXSpreadSide.BUY
    );

    // As hasETFOrders is only used to determine whether the smart execution toggle will be displayed or not, using
    // either smart or express orders to determine its value would be equivalent.
    const hasETFOrders = smartOrders.some(
      (order) => ASSET_CONFIG[getAssetIdFromIsin(order.isin)].category === "etf"
    );

    // Similarly to hasETFOrders, we can use either smart or express orders to determine if orders will be skipped
    const maximumBuyOrdersThatWillBeCreated = Object.entries(holdingsPercentage).length;
    const willSkipOrders = smartOrders.length < maximumBuyOrdersThatWillBeCreated;

    // Get the execution windows for both scenarios
    const [expressExecutionWindow, smartExecutionWindow] = [
      ExecutionWindowUtil.getAssetTransactionExecutionWindow(expressOrders, user.currency, {
        investmentProducts: investmentProductsDict
      }),
      ExecutionWindowUtil.getAssetTransactionExecutionWindow(smartOrders, user.currency, {
        investmentProducts: investmentProductsDict
      })
    ];

    // Get cashback amount
    const cashbackAmount = await TransactionService.getCashbackAmount(
      Decimal.mul(orderAmount, 100).toNumber(),
      user
    );

    // Format the orders
    const [formattedExpressOrders, formattedSmartOrders] = [
      OrderUtil.formatOrdersPreview(expressOrders, {
        investmentProductsDict,
        userCurrency: user.currency,
        fees: expressFees?.realtimeExecution?.amount ?? 0
      }),
      OrderUtil.formatOrdersPreview(smartOrders, {
        investmentProductsDict,
        userCurrency: user.currency,
        // Realtime fees for smart orders are 0, but this is written in a way
        // to not have to be modified if it changes in the future.
        fees: smartFees?.realtimeExecution?.amount ?? 0
      })
    ];

    return {
      executionWindow: user.isRealtimeETFExecutionEnabled
        ? { smart: smartExecutionWindow, express: expressExecutionWindow }
        : { smart: smartExecutionWindow },
      fees: user.isRealtimeETFExecutionEnabled
        ? {
            express: expressFees,
            smart: smartFees
          }
        : { smart: smartFees },
      orders: user.isRealtimeETFExecutionEnabled
        ? { smart: formattedSmartOrders, express: formattedExpressOrders }
        : { smart: formattedSmartOrders },
      cashback: Decimal.div(cashbackAmount, 100).toDecimalPlaces(2).toNumber(),
      hasETFOrders,
      foreignCurrencyRates,
      willSkipOrders
    };
  }

  /**
   * Deletes all transactions with an owner ID that does not match a user document.
   */
  public static async deleteOrphanedTransactions(): Promise<void> {
    const orphanedTransactions = await Transaction.aggregate([
      {
        $match: {
          $or: [
            { status: { $in: ["Pending", "PendingGift", "PendingDeposit"] } },
            { rebalanceStatus: { $in: ["NotStarted", "PendingSell", "PendingBuy"] } }
          ]
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "owner",
          foreignField: "_id",
          as: "owner"
        }
      },
      {
        $unwind: {
          path: "$owner",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $match: {
          owner: {
            $eq: null
          }
        }
      }
    ]);

    await Promise.all(orphanedTransactions.map((transaction) => Transaction.findByIdAndDelete(transaction._id)));
  }

  public static async createWealthyhoodDividends(
    dividendMonth: string = DateUtil.getYearAndMonth(new Date(Date.now()))
  ): Promise<void> {
    await SubscriptionService.getSubscriptionsStreamed({
      active: true,
      prices: PriceArrayConst.filter((price) => ConfigUtil.getPricing()[price].plan !== "free")
    }).eachAsync(async (subscription) => {
      const user = await UserService.getUser(subscription.owner.toString());
      if (!user) {
        logger.warn(`User '${subscription.owner.toString()}' not found`, {
          module: "TransactionService",
          method: "createWealthyhoodDividends"
        });
        return;
      } else if (!user.hasConvertedPortfolio) {
        return;
      } else if (user.companyEntity !== entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
        return;
      }

      const portfolio: PortfolioDocument = await PortfolioService.getGeneralInvestmentPortfolio(user, true);

      // We retrieve existing Wealthyhood dividend for this month
      const existingWealthyhoodDividends = await WealthyhoodDividendTransaction.find({
        owner: user.id,
        dividendMonth
      });

      // We only create a new Wealthyhood dividend if there has been no other one created for this month.
      if (existingWealthyhoodDividends.length === 0) {
        try {
          const wealthyhoodDividend = await TransactionService._createWealthyhoodDividend(
            portfolio,
            subscription.price,
            dividendMonth
          );
          logger.info(`Created wealthyhood dividend for portfolio ${portfolio.id}`, {
            module: "TransactionService",
            method: "createWealthyhoodDividends",
            data: {
              wealthyhoodDividend
            }
          });
        } catch (err) {
          captureException(err);
          logger.error(`Could not give out dividends to portfolio ${portfolio.id}`, {
            module: "TransactionService",
            method: "createWealthyhoodDividends"
          });
        }
      }
    });
  }

  public static async syncDepositStatusByWkDepositId(
    wkDepositId: string,
    newDepositStatus: DepositStatusType
  ): Promise<void> {
    if (newDepositStatus === "Created") return;

    const transaction = await DepositCashTransaction.findOne({
      "providers.wealthkernel.id": wkDepositId,
      "providers.wealthkernel.status": { $nin: ["Cancelled", "Rejected", "Settled"] }
    });

    // If the deposit transaction is already settled, rejected or cancelled, exit.
    if (!transaction) {
      return;
    }

    await TransactionService._updateWkDepositStatus(transaction, newDepositStatus);
  }

  /**
   * @summary Update rebalance transaction for a portfolio
   *
   * @param rebalanceTransactionId
   * @param targetAllocation
   */
  public static async updateRebalanceTransaction(
    rebalanceTransactionId: string,
    targetAllocation: InitialHoldingsAllocationType[]
  ): Promise<RebalanceTransactionDocument> {
    return await RebalanceTransaction.findByIdAndUpdate(
      rebalanceTransactionId,
      {
        targetAllocation
      },
      { new: true }
    );
  }

  public static async getNotStartedRebalanceTransactions(
    portfolioId: string
  ): Promise<RebalanceTransactionDocument[]> {
    return await RebalanceTransaction.find({
      portfolio: portfolioId,
      rebalanceStatus: "NotStarted"
    });
  }

  /**
   * @description Syncs an existing deposit transaction with truelayer and updates the deposit status
   * If the deposit fails and is linked to an assetTransaction:
   * It updates the asset transaction from pendingDeposit to DepositFailed and cancels the corresponding cashbask if it exists
   * @param truelayerId
   * @param truelayerPayload
   * @public
   */
  public static async updateDepositTruelayerData(
    truelayerId: string,
    truelayerPayload: TruelayerPayloadType
  ): Promise<DepositCashTransactionDocument> {
    const transaction = await DepositCashTransaction.findOne({ "providers.truelayer.id": truelayerId });

    if (!transaction)
      throw new BadRequestError(`TruelayerId ${truelayerId} did not match any deposit transaction`);

    const { status, failureReason, executedAt } = truelayerPayload;

    // Validate that status can be updated, based on payment lifecycle
    if (
      transaction.providers?.truelayer?.status &&
      !TruelayerPaymentsClient.validatePaymentStatusUpdate(
        transaction.providers?.truelayer?.status as PaymentStatusTypeV3,
        status
      )
    )
      return transaction;

    const truelayerData: {
      id: string;
      version: TruelayerPaymentVersionType;
      status: PaymentStatusTypeV3;
      failureReason?: FailureStatusType | string;
      executedAt?: Date;
    } = {
      id: transaction.providers?.truelayer?.id,
      version: transaction.providers?.truelayer?.version,
      status: status
    };

    if (status === "failed") {
      truelayerData["failureReason"] = failureReason;

      // update relevant asset/savings transaction
      const isDepositLinkedToTransaction =
        await TransactionService._isDepositLinkedToPendingDepositTransaction(transaction);
      if (isDepositLinkedToTransaction) {
        await TransactionService._handlePendingDepositFailure(transaction);
      }
    } else if (status === "executed") {
      truelayerData["executedAt"] = executedAt;

      if (transaction.depositAction === DepositActionEnum.DEPOSIT_AND_SAVE) {
        await TransactionService._handleOneStepSavePaymentAuthorized(transaction);
      }
    }

    let depositStatus: TransactionStatusType;
    if (["authorization_required", "authorizing", "authorized", "settled", "executed"].includes(status)) {
      depositStatus = "Pending";
    } else if (status === "failed") {
      if (failureReason === "canceled") {
        depositStatus = "Cancelled";
      } else {
        depositStatus = "Rejected";
      }
    }
    // Use previous payment status for filter, to ensure that is updated correctly and avoid concurrency issues
    const updatedTransaction = await DepositCashTransaction.findOneAndUpdate(
      {
        "providers.truelayer.id": transaction.providers?.truelayer?.id,
        "providers.truelayer.status": transaction.providers?.truelayer?.status
      },
      { "providers.truelayer": truelayerData, status: depositStatus },
      {
        runValidators: true,
        upsert: false,
        new: true
      }
    );

    if (updatedTransaction?.providers?.truelayer?.status === "executed") {
      await TransactionService._createWkDepositExpectationSafely(updatedTransaction);
    }

    return updatedTransaction ?? transaction;
  }

  /**
   * @description Syncs an existing deposit transaction with truelayer and updates the deposit status
   * If the deposit fails and is linked to an assetTransaction:
   * It updates the asset transaction from pendingDeposit to DepositFailed and cancels the corresponding cashbask if it exists
   * @public
   * @param saltedgeId
   * @param saltedgeCustomId
   * @param status
   */
  public static async updateDepositSaltedgeData(
    saltedgeId: string,
    saltedgeCustomId: string,
    status: PaymentStatusType
  ): Promise<DepositCashTransactionDocument> {
    let depositStatus: TransactionStatusType = "Pending";
    if (["rejected", "failed", "unknown", "deleted"].includes(status)) {
      depositStatus = "Rejected";
    }

    return DepositCashTransaction.findOneAndUpdate(
      {
        $or: [{ "providers.saltedge.id": saltedgeId }, { "providers.saltedge.customId": saltedgeCustomId }]
      },
      {
        "providers.saltedge": {
          id: saltedgeId,
          customId: saltedgeCustomId,
          status
        },
        status: depositStatus
      },
      { new: true }
    );
  }

  /**
   * @description Syncs an existing deposit transaction with Devengo data for the deposit acquisition incoming payment.
   * @public
   * @param depositId
   * @param stage
   * @param data
   * @param options
   */
  public static async updateDepositIncomingPaymentDevengoData(
    depositId: string,
    stage: TransferWithIntermediaryStageEnum,
    data: {
      id: string;
      status: IncomingPaymentStatusType;
      accountId: string;
    },
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<DepositCashTransactionDocument> {
    const transaction = await DepositCashTransaction.findById(depositId, null, { session: options?.session });

    if (!transaction) {
      throw new BadRequestError(`There is no deposit with ID ${depositId}`);
    } else if (
      ["confirmed", "rejected"].includes(
        transaction.transferWithIntermediary?.[stage]?.incomingPayment?.providers?.devengo?.status
      )
    ) {
      // We already have a terminal status for this deposit, therefore we don't want to update it.
      return transaction;
    }

    // We don't update the deposit to settled status in the Devengo flow, as the final step is the Wealthkernel
    // deposit arriving. The settlement of that deposit will mark the transaction as 'Settled'.
    let depositStatus: TransactionStatusType;
    if (data.status === "rejected") {
      depositStatus = "Rejected";
    } else {
      depositStatus = "Pending";
    }

    const isDevengoConfirmed = data.status === "confirmed";

    const devengoUpdateData: any = {
      id: data.id,
      status: data.status,
      accountId: data.accountId
    };

    if (isDevengoConfirmed) {
      devengoUpdateData.settledAt = new Date(Date.now());
    }

    return DepositCashTransaction.findOneAndUpdate(
      {
        $or: [
          {
            _id: depositId,
            [`transferWithIntermediary.${stage}.incomingPayment.providers.devengo.id`]: { $exists: false }
          },
          {
            $and: [
              {
                [`transferWithIntermediary.${stage}.incomingPayment.providers.devengo.id`]: { $exists: true }
              },
              {
                [`transferWithIntermediary.${stage}.incomingPayment.providers.devengo.id`]:
                  transaction.transferWithIntermediary?.[stage]?.incomingPayment?.providers?.devengo?.id
              },
              {
                [`transferWithIntermediary.${stage}.incomingPayment.providers.devengo.status`]:
                  transaction.transferWithIntermediary?.[stage]?.incomingPayment?.providers?.devengo?.status
              }
            ]
          }
        ]
      },
      {
        [`transferWithIntermediary.${stage}.incomingPayment.providers.devengo`]: devengoUpdateData,
        status: depositStatus
      },
      {
        runValidators: true,
        upsert: false,
        new: true,
        session: options?.session
      }
    );
  }

  /**
   * @description Syncs an existing withdrawal transaction with Devengo data for the withdrawal's incoming payment.
   * @public
   * @param withdrawalId
   * @param data
   */
  public static async updateWithdrawalIncomingPaymentDevengoData(
    withdrawalId: string,
    data: {
      id: string;
      status: IncomingPaymentStatusType;
      accountId: string;
    }
  ): Promise<WithdrawalCashTransactionDocument> {
    const transaction = await WithdrawalCashTransaction.findById(withdrawalId);

    if (!transaction) {
      throw new BadRequestError(`There is no withdrawal with ID ${withdrawalId}`);
    } else if (
      ["confirmed", "rejected"].includes(
        transaction.transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.status
      )
    ) {
      // We already have a terminal status for this withdrawal, therefore we don't want to update it.
      return transaction;
    }

    return WithdrawalCashTransaction.findOneAndUpdate(
      {
        $or: [
          {
            _id: withdrawalId,
            "transferWithIntermediary.collection.incomingPayment.providers.devengo.id": { $exists: false }
          },
          {
            $and: [
              {
                "transferWithIntermediary.collection.incomingPayment.providers.devengo.id": { $exists: true }
              },
              {
                "transferWithIntermediary.collection.incomingPayment.providers.devengo.id":
                  transaction.transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.id
              },
              {
                "transferWithIntermediary.collection.incomingPayment.providers.devengo.status":
                  transaction.transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.status
              }
            ]
          }
        ]
      },
      {
        "transferWithIntermediary.collection.incomingPayment.providers.devengo": {
          id: data.id,
          status: data.status,
          accountId: data.accountId
        }
      },
      {
        runValidators: true,
        upsert: false,
        new: true
      }
    );
  }

  /**
   * @description Syncs an existing deposit transaction with Devengo data for the deposit's outgoing payment.
   * @public
   * @param id
   * @param stage
   * @param status
   */
  public static async updateDepositOutgoingPaymentDevengoData(
    id: string,
    stage: TransferWithIntermediaryStageEnum,
    status: OutgoingPaymentStatusType
  ): Promise<DepositCashTransactionDocument> {
    const transaction = await DepositCashTransaction.findOne({
      [`transferWithIntermediary.${stage}.outgoingPayment.providers.devengo.id`]: id
    });

    if (!transaction) {
      throw new BadRequestError(`Devengo ID ${id} did not match any deposit transaction`);
    } else if (
      ["confirmed", "rejected"].includes(
        transaction.transferWithIntermediary?.[stage]?.outgoingPayment?.providers?.devengo?.status
      )
    ) {
      // We already have a terminal status for this deposit, therefore we don't want to update it.
      return;
    }

    const isDevengoConfirmed = status === "confirmed";

    const devengoUpdateData: any = {
      id: id,
      status: status
    };

    if (isDevengoConfirmed) {
      devengoUpdateData.settledAt = new Date(Date.now());
    }

    return DepositCashTransaction.findOneAndUpdate(
      {
        [`transferWithIntermediary.${stage}.outgoingPayment.providers.devengo.id`]:
          transaction.transferWithIntermediary?.[stage]?.outgoingPayment.providers?.devengo?.id,
        [`transferWithIntermediary.${stage}.outgoingPayment.providers.devengo.status`]:
          transaction.transferWithIntermediary?.[stage]?.outgoingPayment.providers?.devengo?.status
      },
      {
        [`transferWithIntermediary.${stage}.outgoingPayment.providers.devengo`]: devengoUpdateData
      },
      {
        runValidators: true,
        upsert: false,
        new: true
      }
    );
  }

  /**
   * @description Syncs an existing withdrawal transaction with Devengo data for the withdrawal's outgoing payment.
   * @public
   * @param withdrawalId
   * @param data
   */
  public static async updateWithdrawalOutgoingPaymentDevengoData(
    withdrawalId: string,
    data: {
      id: string;
      status: OutgoingPaymentStatusType;
    }
  ): Promise<WithdrawalCashTransactionDocument> {
    const transaction = await WithdrawalCashTransaction.findById(withdrawalId);

    if (!transaction) {
      throw new BadRequestError(`There is no withdrawal with ID ${withdrawalId}`);
    } else if (
      ["confirmed", "rejected"].includes(
        transaction.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status
      )
    ) {
      // We already have a terminal status for this withdrawal, therefore we don't want to update it.
      return;
    }

    const isDevengoConfirmed = data.status === "confirmed";

    const devengoUpdateData: any = {
      id: data.id,
      status: data.status
    };
    const updateData: any = {
      "transferWithIntermediary.collection.outgoingPayment.providers.devengo": devengoUpdateData
    };

    if (isDevengoConfirmed) {
      const settledAt = new Date(Date.now());
      devengoUpdateData.settledAt = settledAt;
      updateData.settledAt = settledAt;
    }

    return WithdrawalCashTransaction.findOneAndUpdate(
      {
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.id":
          transaction.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.id,
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status":
          transaction.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status
      },
      updateData,
      {
        runValidators: true,
        upsert: false,
        new: true
      }
    );
  }

  public static async updateLifetimeChargeTruelayerData(
    truelayerId: string,
    truelayerPayload: TruelayerPayloadType
  ): Promise<ChargeTransactionDocument> {
    const transaction = await ChargeTransaction.findOne({ "providers.truelayer.id": truelayerId });

    if (!transaction) throw new BadRequestError(`TruelayerId ${truelayerId} did not match any charge transaction`);

    const { status, failureReason, executedAt } = truelayerPayload;

    // Validate that status can be updated, based on payment lifecycle
    if (
      transaction.providers?.truelayer?.status &&
      !TruelayerPaymentsClient.validatePaymentStatusUpdate(
        transaction.providers?.truelayer?.status as PaymentStatusTypeV3,
        status
      )
    )
      return;

    const truelayerData: {
      id: string;
      status: PaymentStatusTypeV3;
      failureReason?: FailureStatusType | string;
      executedAt?: Date;
    } = {
      id: transaction.providers?.truelayer?.id,
      status: status
    };
    if (status === "failed") {
      truelayerData["failureReason"] = failureReason;
    } else if (status === "executed") {
      truelayerData["executedAt"] = executedAt;
    }

    let updatedChargeStatus: TransactionStatusType;
    if (status === "executed") {
      updatedChargeStatus = "Settled";
    } else if (status === "failed") {
      updatedChargeStatus = "Rejected";
    } else {
      updatedChargeStatus = "Pending";
    }

    // Use previous payment status for filter, to ensure that is updated correctly and avoid concurrency issues
    const updatedTransaction = await ChargeTransaction.findOneAndUpdate(
      {
        "providers.truelayer.id": transaction.providers?.truelayer?.id,
        "providers.truelayer.status": transaction.providers?.truelayer?.status
      },
      { status: updatedChargeStatus, "providers.truelayer": truelayerData },
      {
        runValidators: true,
        upsert: false,
        new: true
      }
    );

    if (["authorized", "executed"].includes(updatedTransaction?.providers?.truelayer?.status)) {
      await SubscriptionService.redeemLifetimePayment(
        (transaction.subscription as mongoose.Types.ObjectId).toString(),
        transaction.price
      );
    }

    return updatedTransaction;
  }

  public static async settleCardPaymentCharge(
    chargeTransaction: ChargeTransactionDocument,
    paymentMethod: PaymentMethodDocument,
    nextChargeAt: Date
  ): Promise<ChargeTransactionDocument> {
    const updatedTransaction = await ChargeTransaction.findByIdAndUpdate(
      chargeTransaction.id,
      {
        status: "Settled",
        paymentMethod: paymentMethod.id,
        "providers.stripe.status": "succeeded"
      },
      { new: true }
    ).populate("owner");
    await SubscriptionService.updateNextChargeAt(chargeTransaction.subscription.toString(), nextChargeAt);

    TransactionService._emitSubscriptionChargeSuccessEvent(updatedTransaction.owner as UserDocument, {
      chargeMethod: chargeTransaction.chargeMethod,
      price: chargeTransaction.price,
      actualCharge: Decimal.div(updatedTransaction.consideration?.amount, 100).toNumber(),
      settledAt: new Date(Date.now())
    });

    return updatedTransaction;
  }

  public static async updateCardPaymentStatus(
    chargeId: string,
    status: Stripe.PaymentIntent.Status
  ): Promise<ChargeTransactionDocument> {
    return ChargeTransaction.findByIdAndUpdate(
      chargeId,
      {
        "providers.stripe.status": status
      },
      { new: true }
    );
  }

  public static async updateWealthyhoodDividendTransaction(
    transactionId: string,
    wealthyhoodDividendData: { hasViewedAppModal: boolean }
  ): Promise<void> {
    if (!transactionId || !validator.isMongoId(transactionId)) {
      throw new InternalServerError(`Transaction ID ${transactionId} is not valid`);
    }

    const sanitisedData = Object.fromEntries(
      Object.entries(wealthyhoodDividendData).filter(([, value]) => value !== undefined && value !== null)
    );

    if (!Object.keys(sanitisedData).length) {
      throw new InternalServerError("Data cannot be empty");
    }

    await WealthyhoodDividendTransaction.findByIdAndUpdate(transactionId, sanitisedData);
  }

  public static async updateChargeStatusByWkChargeId(
    wkChargeId: string,
    newChargeStatus: ChargeStatusType | InternalTransferStatusType
  ): Promise<void> {
    const transaction = await ChargeTransaction.findOne({
      "providers.wealthkernel.id": wkChargeId,
      status: { $in: ["PendingWealthkernelCharge", "Pending"] }
    });

    if (!transaction) {
      throw new NotFoundError(
        `Did not found a pending requested charge transaction by wealthkernel id ${wkChargeId}`
      );
    }

    let updateData: mongoose.UpdateQuery<ChargeTransactionDocument> = {
      "providers.wealthkernel": {
        id: wkChargeId,
        status: newChargeStatus,
        submittedAt: transaction?.providers?.wealthkernel?.submittedAt
      }
    };

    if (["Completed", "Booked"].includes(newChargeStatus)) {
      updateData = {
        ...updateData,
        status: "Settled",
        settledAt: new Date(Date.now())
      };
    } else if (newChargeStatus === "Cancelled") {
      updateData.status = "Cancelled";
    }

    const updatedTransaction = await ChargeTransaction.findByIdAndUpdate(transaction.id, updateData, {
      new: true
    }).populate("owner linkedTransaction");

    /**
     * Emit event based on transaction chargeType
     */
    if (updatedTransaction.status === "Settled" && updatedTransaction.chargeType === "custody") {
      const properties: TrackCustodyChargeSuccess = {
        amount: Decimal.div(updatedTransaction.consideration?.amount, 100).toNumber(),
        currency: updatedTransaction.consideration?.currency
      };

      eventEmitter.emit(
        events.transaction.custodyChargeSuccess.eventId,
        updatedTransaction.owner as UserDocument,
        properties
      );
    } else if (updatedTransaction.status === "Settled" && updatedTransaction.chargeType === "subscription") {
      TransactionService._emitSubscriptionChargeSuccessEvent(updatedTransaction.owner as UserDocument, {
        chargeMethod: transaction.chargeMethod,
        price: transaction.price,
        actualCharge: Decimal.div(updatedTransaction.consideration?.amount, 100).toNumber(),
        settledAt: new Date(Date.now())
      });
    } else if (updatedTransaction.status === "Settled" && updatedTransaction.chargeType === "dividendCommission") {
      const savingsDividend = updatedTransaction.linkedTransaction as SavingsDividendTransactionDocument;

      TransactionService._emitSavingsDividendChargeSuccessEvent(updatedTransaction.owner as UserDocument, {
        chargedAmount: Decimal.div(updatedTransaction.consideration?.amount, 100).toNumber(),
        originalDividendAmount: Decimal.div(savingsDividend.originalDividendAmount, 100).toNumber()
      });
    }
  }

  public static async updateRevertRewardStatusByWkChargeId(
    wkChargeId: string,
    newChargeStatus: ChargeStatusType | InternalTransferStatusType
  ): Promise<void> {
    const transaction = await RevertRewardTransaction.findOne({
      "providers.wealthkernel.id": wkChargeId,
      status: { $in: ["PendingWealthkernelCharge", "Pending"] }
    });

    if (!transaction) {
      throw new NotFoundError(
        `Did not found a pending requested revert reward transaction by wealthkernel id ${wkChargeId}`
      );
    }

    let updateData: mongoose.UpdateQuery<RevertRewardTransactionDocument> = {
      "providers.wealthkernel": {
        id: wkChargeId,
        status: newChargeStatus,
        submittedAt: transaction?.providers?.wealthkernel?.submittedAt
      }
    };

    if (["Completed", "Booked"].includes(newChargeStatus)) {
      updateData = {
        ...updateData,
        status: "Settled",
        settledAt: new Date(Date.now())
      };
    } else if (newChargeStatus === "Cancelled") {
      updateData.status = "Cancelled";
    }

    await RevertRewardTransaction.findByIdAndUpdate(transaction.id, updateData, {
      new: true
    });
  }

  /**
   *  @description
   *  Submit requests to Wealthkernel for charges and revert rewards.
   *  Throw an error in case there are some charge transactions with pending orders.
   *
   */
  public static async submitChargesToWK(
    startDate: Date,
    endDate: Date = new Date(),
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): Promise<void> {
    const [
      custodyAmountsByWkPortfolioId,
      commissionAmountsByWkPortfolioId,
      executionSpreadAmountsByWkPortfolioId,
      revertRewardAmountsByWkPortfolioId,
      fxAmountByPortfolioId,
      dividendFeeAmountsByPortfolioId,
      realtimeExecutionFeeAmountsByPortfolioId
    ] = await Promise.all([
      TransactionService._getCustodyAmountsByWkPortfolioId(startDate, endDate),
      TransactionService._getCommissionAmountsByWkPortfolioId(startDate, endDate),
      TransactionService._getExecutionSpreadAmountsByWkPortfolioId(startDate, endDate),
      TransactionService._getRevertRewardAmountsByWkPortfolioId(startDate, endDate),
      TransactionService._getFxAmountsByWkPortfolioId(startDate, endDate),
      TransactionService._getDividendFeeAmountsByWkPortfolioId(startDate, endDate),
      TransactionService._getRealtimeExecutionFeeAmountsByWkPortfolioId(startDate, endDate)
    ]);

    const filteredCharges = this._filterChargesByCompanyEntity(
      {
        custodyAmounts: custodyAmountsByWkPortfolioId,
        commissionAmounts: commissionAmountsByWkPortfolioId,
        executionSpreadAmounts: executionSpreadAmountsByWkPortfolioId,
        revertRewardAmounts: revertRewardAmountsByWkPortfolioId,
        fxAmounts: fxAmountByPortfolioId,
        dividendFeeAmounts: dividendFeeAmountsByPortfolioId,
        realtimeExecutionFeeAmounts: realtimeExecutionFeeAmountsByPortfolioId
      },
      companyEntity
    );

    await this._submitWkCharges(filteredCharges);
  }

  public static async addBonusDepositToUser(user: UserDocument, bonusDepositAmount: number): Promise<void> {
    const [generalInvestmentPortfolio, bankAccounts] = await Promise.all([
      PortfolioService.getGeneralInvestmentPortfolio(user),
      BankAccountService.getBankAccounts({ owner: user.id })
    ]);

    // For whitelisted accounts we can send a bonus even if they don't have a bank account
    if (!UserUtil.isUserWhitelisted(user) && bankAccounts.length === 0) {
      throw new BadRequestError("User has no bank account linked");
    }

    const bonusPayment = await ProviderService.getBrokerageService(user.companyEntity).createBonus({
      destinationPortfolio: generalInvestmentPortfolio.providers?.wealthkernel?.id,
      consideration: {
        currency: user.currency,
        amount: bonusDepositAmount
      }
    });

    logger.info(`Created bonus deposit with WK id ${bonusPayment.id}`, {
      module: "transactionService",
      method: "addBonusDepositToUser",
      data: {
        destinationPortfolio: generalInvestmentPortfolio.providers?.wealthkernel?.id
      }
    });

    const bonusDepositDocument = await new DepositCashTransaction({
      depositMethod: DepositMethodEnum.BONUS,
      bankAccount: bankAccounts[0],
      bankReference: `admin-${nanoid()}`,
      owner: user._id,
      portfolio: generalInvestmentPortfolio._id,
      consideration: {
        currency: user.currency,
        amount: Decimal.mul(bonusDepositAmount, 100).toNumber()
      },
      providers: {
        truelayer: {
          id: `admin-${randomUUID()}`,
          status: "executed"
        },
        wealthkernel: {
          id: bonusPayment.id,
          status: "Settled"
        }
      },
      status: "Settled",
      createdAt: new Date(),
      settledAt: new Date()
    }).save();

    logger.info(`Created fake top-up in our db for bonus deposit with WK id ${bonusPayment.id}`, {
      module: "transactionService",
      method: "addBonusDepositToUser",
      data: {
        depositCashTransactionId: bonusDepositDocument._id
      }
    });

    await PortfolioService.updateCashAvailability(
      generalInvestmentPortfolio.id,
      user.currency,
      bonusDepositAmount,
      { available: true, settled: true }
    );

    logger.info(`Updated user's cash for bonus deposit with WK id ${bonusPayment.id}`, {
      module: "transactionService",
      method: "addBonusDepositToUser",
      data: {
        generalInvestmentPortfolioId: generalInvestmentPortfolio.id
      }
    });
  }

  /**
   * Enhances a transaction object with properties useful for the clients.
   * The fields are calculated display amounts, quantities, and optionally adds isCancellable field to order
   * its orders with additional properties useful for client-side display.
   * @param user
   * @param transaction The transaction to fill.
   * @param investmentProducts
   * @param latestFXRatesWithSpread
   * @param fields (optional) Controls which additional fields to populate (default: `displayAmount` and
   *        `displayQuantity` are `true`, `isCancellable` is `false`).
   * @returns An enhanced TransactionDocument object with additional display properties and optionally
   *          updated orders.
   * @public
   */
  public static fillClientDisplayFields(
    user: UserDocument,
    transaction: TransactionDocument,
    investmentProducts: { [key: string]: InvestmentProductDocument },
    latestFXRatesWithSpread?: PartialRecord<FXSpreadSide, ForeignCurrencyRatesType>,
    fields: FillClientDisplayFields = {
      displayAmount: true,
      displayQuantity: true,
      executionWindow: true,
      displayExchangeRate: false,
      isCancellable: false,
      estimatedRealTimeCommission: false
    }
  ): TransactionDocument {
    if (transaction.category === "AssetTransaction") {
      const orders = (transaction as AssetTransactionDocument).orders;
      const enhancedTransaction = {
        ...transaction.toObject(),
        orders: TransactionService._fillOrdersClientDisplayFields(
          user,
          orders,
          investmentProducts,
          latestFXRatesWithSpread,
          transaction,
          fields
        ),
        displayAmount: transaction.getDisplayAmount(user.currency, investmentProducts),
        displayQuantity: (transaction as AssetTransactionDocument).getDisplayQuantity(
          user.currency,
          investmentProducts
        ),
        isCancellable: transaction.getIsCancellable(user, investmentProducts)
      } as AssetTransactionDocument;
      if (fields.isCancellable) {
        enhancedTransaction.isCancellable = transaction.getIsCancellable(user, investmentProducts);
      }
      return enhancedTransaction;
    } else if (transaction.category === "RebalanceTransaction") {
      const orders = (transaction as RebalanceTransactionDocument).orders;
      const enhancedTransaction = {
        ...transaction.toObject(),
        orders: TransactionService._fillOrdersClientDisplayFields(
          user,
          orders,
          investmentProducts,
          latestFXRatesWithSpread,
          transaction,
          fields
        ),
        isCancellable: transaction.getIsCancellable(user, investmentProducts)
      } as RebalanceTransactionDocument;
      if (fields.isCancellable) {
        enhancedTransaction.isCancellable = transaction.getIsCancellable(user, investmentProducts);
      }
      return enhancedTransaction;
    } else if (["SavingsTopupTransaction", "SavingsWithdrawalTransaction"].includes(transaction.category)) {
      const orders = (transaction as SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument)
        .orders;
      const enhancedTransaction = {
        ...transaction.toObject(),
        orders: TransactionService._fillOrdersClientDisplayFields(
          user,
          orders,
          investmentProducts,
          latestFXRatesWithSpread,
          transaction,
          {
            displayAmount: true,
            displayQuantity: false,
            executionWindow: false,
            displayExchangeRate: false,
            isCancellable: false,
            estimatedRealTimeCommission: false
          }
        )
      } as SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument;
      return enhancedTransaction;
    } else {
      // For other transaction types, simply return the original transaction without modification.
      return transaction;
    }
  }

  public static async getPendingTransactionsWithOrders(owner: string): Promise<TransactionDocument[]> {
    const [assetAndChargesTransactions, rebalanceTransactions] = await Promise.all([
      Transaction.find({
        owner,
        category: { $in: ["AssetTransaction", "ChargeTransaction"] },
        status: "Pending"
      }).populate({
        path: "orders",
        strictPopulate: false
      }),
      RebalanceTransaction.find({
        owner,
        rebalanceStatus: { $in: ["NotStarted", "PendingSell", "PendingBuy", "Rejected"] }
      }).populate({
        path: "orders",
        strictPopulate: false
      })
    ]);

    return [...assetAndChargesTransactions, ...rebalanceTransactions];
  }

  /**
   * @description
   * Retrieves and processes pending cashflow transactions (cashbacks and deposits) for a specified portfolio.
   * The method performs the following operations:
   * 1. Fetches all pending cashbacks and deposit transactions for the given portfolio ID.
   *    - For cashbacks, it includes any transaction in 'Pending' status.
   *    - For deposits, it selects transactions with Truelayer status 'authorized' or 'executed' and not settled on Wealthkernel.
   * 2. Applies custom filters:
   *    - Deposits linked to repeating investments are excluded.
   *    - Deposits linked to 1-step investments are included only if the investment is cancelled.
   *    - Filters out cashbacks linked to a portfolio buy with incomplete Truelayer flow.
   * 3. Sorts the remaining transactions in descending order by their creation date.
   *
   * @param {string} portfolioId - The ID of the portfolio for which to retrieve pending transactions.
   */
  public static async getPendingCashflowTransactions(portfolioId: string) {
    const [cashbacks, deposits, savingsWithdrawals] = await Promise.all([
      CashbackTransaction.find({
        portfolio: portfolioId,
        status: "Pending"
      }).populate({
        path: "linkedAssetTransaction",
        populate: { path: "pendingDeposit" }
      }),
      DepositCashTransaction.find({
        portfolio: portfolioId,
        status: "Pending",
        $or: [
          { "providers.truelayer.status": { $in: ["authorized", "executed"] } },
          { "providers.saltedge.status": "accepted" },
          { depositMethod: DepositMethodEnum.BANK_TRANSFER }
        ],
        linkedAutomation: { $exists: false }
      }).populate([
        {
          path: "linkedAssetTransaction"
        },
        { path: "bankAccount" },
        {
          path: "linkedSavingsTopup"
        },
        {
          path: "linkedCreditTicket"
        }
      ]),
      SavingsWithdrawalTransaction.find({
        portfolio: portfolioId,
        status: { $in: ["PendingTopUp", "Pending"] }
      }).populate("orders")
    ]);

    //  filter deposits that are linked to asset transaction, savings topup or credited credit ticket
    const filteredDeposits = deposits
      .filter(this._filterOutDepositLinkedToAssetTransaction)
      .filter(this._filterOutDepositLinkedToSavingsTopup)
      .filter(this._filterOutDepositLinkedToCreditedCreditTicket);

    // Filter out cashbacks  that are linked to a portfolio buy which has pending deposit that has not completed truelayer flow
    const filteredCashbacks = cashbacks.filter((cashback: CashbackTransactionDocument) => {
      const pendingDeposit = (cashback?.linkedAssetTransaction as AssetTransactionDocument)
        ?.pendingDeposit as DepositCashTransactionDocument;
      if (pendingDeposit?.providers?.truelayer && pendingDeposit?.isTruelayerFlowCompleted)
        return pendingDeposit?.isTruelayerFlowCompleted;
      else if (pendingDeposit?.status === "Rejected" || pendingDeposit?.status === "Cancelled") return false;
      return true;
    });

    const sortedTransactions = [...filteredCashbacks, ...filteredDeposits, ...savingsWithdrawals].sort(
      (transactionA, transactionB) => transactionB.createdAt.getTime() - transactionA.createdAt.getTime()
    );
    return sortedTransactions;
  }

  public static async createSavingsTopupTransaction(
    partialSavingsTopupTransaction: Omit<SavingsTopupTransactionDTOInterface, "createdAt">
  ): Promise<SavingsTopupTransactionDocument> {
    const savingsTopupTransactionData: SavingsTopupTransactionDTOInterface = {
      ...partialSavingsTopupTransaction,
      createdAt: new Date()
    };

    return new SavingsTopupTransaction(savingsTopupTransactionData).save();
  }

  public static async createSavingsWithdrawalTransaction(
    partialSavingsWithdrawalTransaction: Omit<SavingsWithdrawalTransactionDTOInterface, "createdAt">
  ): Promise<SavingsWithdrawalTransactionDocument> {
    const savingsWithdrawalTransactionData: SavingsWithdrawalTransactionDTOInterface = {
      ...partialSavingsWithdrawalTransaction,
      createdAt: new Date()
    };

    return new SavingsWithdrawalTransaction(savingsWithdrawalTransactionData).save();
  }

  public static async syncSavingsTopupTransaction(topup: SavingsTopupTransactionDocument): Promise<void> {
    logger.info(`Syncing ${topup.id} Savings Topup`, {
      module: "TransactionService",
      method: "syncSavingsTopupTransaction",
      data: {
        topupId: topup.id
      }
    });

    // If transaction is not Pending, we shouldn't process it as it could result to race conditions
    if (topup.status !== "Pending") return;

    const ordersSum = topup.orders.reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0));
    if (!ordersSum.eq(topup.consideration.amount)) {
      logger.info(
        `Sum of orders ${ordersSum.toNumber()} does not match topup consideration amount ${
          topup.consideration.amount
        }`,
        {
          module: "TransactionService",
          method: "syncSavingsTopupTransaction",
          data: {
            topupId: topup.id
          }
        }
      );
      return;
    }

    const allOrdersMatchedOrFilled = topup.orders.every((order) =>
      ["Settled", "InternallyFilled"].includes(order.status)
    );

    if (allOrdersMatchedOrFilled) {
      logger.info(`Syncing ${topup.id} Savings Topup - all orders are matched`, {
        module: "TransactionService",
        method: "syncSavingsTopupTransaction",
        data: {
          topupId: topup.id
        }
      });

      /**
       * Because some order might be InternallyFilled, which means that amount is already represented in the portfolio cash/savings
       * We only care about the orders that we have submitted to the broker
       */
      const amountFromSubmittedOrders = topup.orders
        .filter((order) => order.isSubmittedToBroker)
        .reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0))
        .toNumber();

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        await SavingsTopupTransaction.findOneAndUpdate(
          { _id: topup.id },
          {
            status: "Settled",
            settledAt: new Date(Date.now())
          },
          { session }
        );

        await PortfolioService.updatePortfolioSavings(
          (topup.portfolio as mongoose.Types.ObjectId).toString(),
          topup.savingsProduct,
          amountFromSubmittedOrders,
          { session }
        );

        TransactionService._emitSavingsTransactionSuccessEvent(topup.owner as UserDocument, {
          savingsProductId: topup.savingsProduct,
          amount: Decimal.div(topup.consideration.amount, 100).toNumber(),
          currency: topup.consideration.currency,
          side: "buy"
        });
      });

      logger.info(`Successfully synced ${topup.id} Savings Topup`, {
        module: "TransactionService",
        method: "syncSavingsTopupTransaction"
      });
    } else if (topup.orders.some((order) => ["Rejected", "Cancelled"].includes(order.status))) {
      logger.error("Unexpected order status", {
        module: "TransactionService",
        method: "syncSavingsTopupTransaction",
        data: {
          topupId: topup.id
        }
      });
      throw new Error(`Received unexpected order status while sync ${topup.id} savings topup`);
    }
  }

  public static async syncSavingsWithdrawalTransaction(
    withdrawal: SavingsWithdrawalTransactionDocument
  ): Promise<SavingsWithdrawalTransactionDocument> {
    logger.info(`Syncing ${withdrawal.id} Savings Withdrawal`, {
      module: "TransactionService",
      method: "syncSavingsWithdrawalTransaction",
      data: {
        withdrawalId: withdrawal.id
      }
    });

    // If transaction is not Pending, we shouldn't process it as it could result to race conditions
    if (withdrawal.status !== "Pending") return withdrawal;

    const ordersSum = withdrawal.orders.reduce(
      (sum, order) => sum.plus(order.consideration.amount),
      new Decimal(0)
    );
    if (!ordersSum.eq(withdrawal.consideration.amount)) {
      logger.info(
        `Sum of orders ${ordersSum.toNumber()} does not match withdrawal consideration amount ${
          withdrawal.consideration.amount
        }`,
        {
          module: "TransactionService",
          method: "syncSavingsWithdrawalTransaction",
          data: {
            withdrawalId: withdrawal.id
          }
        }
      );
      return withdrawal;
    }

    const allOrdersMatchedOrFilled = withdrawal.orders.every((order) =>
      ["Settled", "InternallyFilled"].includes(order.status)
    );

    if (allOrdersMatchedOrFilled) {
      logger.info("Order is matched - will update transaction status, portfolio cash & savings", {
        module: "TransactionService",
        method: "syncSavingsWithdrawalTransaction",
        data: {
          withdrawalId: withdrawal.id
        }
      });

      /**
       * Because some order might be InternallyFilled, which means that amount is already represented in the portfolio savings
       * We only care about the orders that we have submitted to the broker
       * -- For the portfolio cash we will use the transaction amount since internal orders do not update it
       */
      const amountFromSubmittedOrders = withdrawal.orders
        .filter((order) => order.isSubmittedToBroker)
        .reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0))
        .toNumber();

      const portfolioId = (withdrawal.portfolio as mongoose.Types.ObjectId).toString();

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        await DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.OWNER);

        const user = withdrawal.owner as UserDocument;

        await SavingsWithdrawalTransaction.findOneAndUpdate(
          { _id: withdrawal.id },
          {
            status: "Settled",
            settledAt: new Date(Date.now())
          },
          { session }
        );
        if (amountFromSubmittedOrders > 0) {
          await PortfolioService.updatePortfolioSavings(
            portfolioId,
            withdrawal.savingsProduct,
            -amountFromSubmittedOrders,
            { session }
          );
        }
        await PortfolioService.updateCashAvailability(
          portfolioId,
          user.currency,
          Decimal.div(withdrawal.consideration.amount, 100).toNumber(),
          { session, available: true, settled: true }
        );

        TransactionService._emitSavingsTransactionSuccessEvent(user, {
          savingsProductId: withdrawal.savingsProduct,
          amount: Decimal.div(withdrawal.consideration.amount, 100).toNumber(),
          currency: withdrawal.consideration.currency,
          side: "sell"
        });
      });

      return SavingsWithdrawalTransaction.findById(withdrawal.id).populate("orders");
    } else if (withdrawal.orders.some((order) => ["Rejected", "Cancelled"].includes(order.status))) {
      logger.error("Unexpected order status", {
        module: "TransactionService",
        method: "syncSavingsWithdrawalTransaction",
        data: {
          withdrawalId: withdrawal.id
        }
      });
      throw new Error(`Received unexpected order status while sync ${withdrawal.id} savings withdrawal`);
    }
  }

  public static async getSavingsProductActivityTransactions(
    userId: string,
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    limit?: number
  ): Promise<TransactionDocument[]> {
    // 1. Query Transactions
    const transactions = await Transaction.find({
      owner: userId,
      $or: [
        {
          category: "SavingsTopupTransaction",
          status: { $in: ["PendingDeposit", "Pending", "Settled"] }
        },
        {
          category: "SavingsWithdrawalTransaction",
          status: { $in: ["PendingTopUp", "Pending", "Settled"] }
        },
        {
          category: "SavingsDividendTransaction",
          status: { $in: ["PendingReinvestment", "Pending", "Settled"] },
          savingsProduct: savingsProductId
        }
      ]
    }).populate([
      { path: "orders" },
      { path: "linkedSavingsDividend" },
      {
        path: "linkedAutomation",
        populate: {
          path: "mandate"
        }
      },
      { path: "pendingDeposit", populate: { path: "bankAccount" } }
    ]);

    // 2. Filter Transactions
    const transactionsFiltered: TransactionDocument[] = transactions
      .filter(this._filterOutSavingsTopUpTransactionLinkedToSavingsDividend)
      .filter(this.filterOutSavingsTopUpTransactionLinkedToIncompleteDeposits)
      .filter(this._filterTransactionsThatMatchSavingsProduct(savingsProductId));

    // 3. Sort and optionally limit transactions
    const sortedTransactionsWithLimit = transactionsFiltered.sort(
      (a, b) => b.sortingField.getTime() - a.sortingField.getTime()
    );
    if (limit) {
      sortedTransactionsWithLimit.splice(limit);
    }

    return transactionsFiltered;
  }

  /**
   * @description
   * This method processes Savings Withdrawal with 'PendingTopUp' status.
   * Context:
   * For a Savings Withdrawal to have 'PendingTopUp' status,
   * it would mean that is depending for SavingsTopup to be settled,
   * so they're will be enough saving holdings to sell.
   * ---
   * In this method we aggregate they available savings amount to sell,
   * if it's enough for the given Savings Withdrawal we make the status 'Pending'.
   */
  public static async processPendingTopUpSavingsWithdrawal(
    savingsWithdrawal: SavingsWithdrawalTransactionDocument
  ): Promise<void> {
    const pendingSavingsWithdrawals = await SavingsWithdrawalTransaction.find({
      status: "Pending",
      savingsProduct: savingsWithdrawal.savingsProduct,
      _id: { $ne: savingsWithdrawal.id }
    }).populate("orders");

    // 1. Calculate available to sell amount
    const restrictedAmountForSellOrders = pendingSavingsWithdrawals
      .flatMap((transaction) => transaction.orders)
      .reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0));
    const savingsProductId = savingsWithdrawal.savingsProduct;
    const portfolio = savingsWithdrawal.portfolio as PortfolioDocument;
    const currentSavingsAmount = new Decimal(portfolio.savings?.get(savingsProductId)?.amount ?? 0);
    const availableToSell = Decimal.sub(currentSavingsAmount, restrictedAmountForSellOrders);

    // 2. Calculate the remaining amount to sell for the given withdrawal
    const amountRectrictedFromOrders =
      savingsWithdrawal.orders?.reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0)) ??
      new Decimal(0);
    const remaningAmountToSell = new Decimal(savingsWithdrawal.consideration.amount)
      .minus(amountRectrictedFromOrders)
      .toNumber();

    if (availableToSell.gte(remaningAmountToSell)) {
      await savingsWithdrawal.updateOne({ status: "Pending" });
      logger.info(`Updated status to 'Pending' for ${savingsWithdrawal.id} savings withdrawal.`, {
        module: "TransactionService",
        method: "processPendingTopUpSavingsWithdrawal",
        data: {
          availableToSell: availableToSell.toNumber(),
          considerationAmount: savingsWithdrawal.consideration.amount,
          savingsWithdrawal: savingsWithdrawal.id
        }
      });
    } else {
      logger.info(
        `Not enough saving holdings to update status to 'Pending' for ${savingsWithdrawal.id} savings withdrawal.`,
        {
          module: "TransactionService",
          method: "processPendingTopUpSavingsWithdrawal",
          data: {
            availableToSell: availableToSell.toNumber(),
            considerationAmount: savingsWithdrawal.consideration.amount,
            savingsWithdrawal: savingsWithdrawal.id
          }
        }
      );
    }
  }

  public static getVolumeOfTransactions(
    assetTransactions: AssetTransactionDocument[],
    options: { includeSells: boolean }
  ): Decimal {
    return assetTransactions
      .filter((transaction) => transaction.status === "Settled")
      .map((transaction) => {
        const isPortfolioBuy = transaction.portfolioTransactionCategory === "buy";
        const isPortfolioSell = transaction.portfolioTransactionCategory === "sell";

        const isAssetBuy =
          transaction.portfolioTransactionCategory === "update" &&
          transaction.orders.length === 1 &&
          transaction.orders[0].side === "Buy";
        const isAssetSell =
          transaction.portfolioTransactionCategory === "update" &&
          transaction.orders.length === 1 &&
          transaction.orders[0].side === "Sell";
        const isDeprecatedPortfolioUpdate =
          transaction.portfolioTransactionCategory === "update" && transaction.orders.length > 1;

        const includeTransaction =
          isPortfolioBuy ||
          isAssetBuy ||
          isDeprecatedPortfolioUpdate ||
          (options.includeSells && (isPortfolioSell || isAssetSell));

        if (!includeTransaction) {
          return 0;
        }

        if (isPortfolioBuy) {
          return transaction.originalInvestmentAmount ?? transaction.consideration.amount;
        } else if (options.includeSells && isPortfolioSell) {
          return transaction.consideration.amount;
        } else if (isAssetBuy) {
          const order = transaction.orders[0];
          return (
            order.consideration.originalAmount ?? order.consideration.amountSubmitted ?? order.consideration.amount
          );
        } else if (options.includeSells && isAssetSell) {
          const order = transaction.orders[0];
          return order.consideration.amount;
        } else if (isDeprecatedPortfolioUpdate) {
          return transaction.orders
            .filter((order) => order.side === "Buy" || (options.includeSells && order.side === "Sell"))
            .map((order) => {
              if (order.side === "Buy") {
                return (
                  order.consideration.originalAmount ??
                  order.consideration.amountSubmitted ??
                  order.consideration.amount
                );
              } else if (order.side === "Sell") {
                return order.consideration.amount;
              } else {
                return 0;
              }
            })
            .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
            .toNumber();
        }

        throw new InternalServerError("Transaction category not recognised!");
      })
      .reduce((sum, quantity) => sum.plus(quantity), new Decimal(0))
      .div(100);
  }

  public static async createSavingsDividend(
    wkDividend: TransactionType,
    savingsProduct: SavingsProductDocument
  ): Promise<void> {
    if (wkDividend.status === "Cancelled") {
      logger.error(`Cannot process cancelled savings dividend ${wkDividend.id}`, {
        module: "TransactionService",
        method: "createSavingsDividend"
      });
      return;
    }

    logger.info(`Creating savings dividend for ${wkDividend.id}`, {
      module: "TransactionService",
      method: "createSavingsDividend"
    });
    // 1. Check if document exists already for this dividend
    const dividendExists = await SavingsDividendTransaction.exists({ "providers.wealthkernel.id": wkDividend.id });

    // 2. If dividend transaction id exists abort
    if (dividendExists) {
      logger.warn(`Savings dividend ${wkDividend.id} already has a db document - aborting`, {
        module: "TransactionService",
        method: "createSavingsDividend"
      });
      return;
    }

    // 3. If dividend transaction id does not exist create dividend document
    const portfolio = await Portfolio.findOne({
      "providers.wealthkernel.id": wkDividend.portfolioId
    }).populate("owner");
    const user = portfolio.owner as UserDocument;

    const { dividendPayoutInCents, dividendAmountPostFeesInCents, dividendFeeInCents } =
      await TransactionService._applyFeesToSavingsDividend(portfolio, wkDividend, savingsProduct);

    const dividendData: SavingsDividendTransactionDTOInterface = {
      fees: {
        fx: {
          amount: 0,
          currency: user.currency
        },
        executionSpread: {
          amount: 0,
          currency: user.currency
        },
        commission: {
          amount: Decimal.div(dividendFeeInCents, 100).toNumber(), // Store in whole and not cents,
          currency: user.currency
        }
      },
      consideration: {
        currency: wkDividend.consideration.currency,
        amount: dividendAmountPostFeesInCents
      },
      originalDividendAmount: dividendPayoutInCents,
      owner: user.id,
      portfolio: portfolio.id,
      savingsProduct: savingsProduct.commonId,
      activeProviders: [ProviderEnum.WEALTHKERNEL],
      providers: {
        wealthkernel: {
          id: wkDividend.id,
          status: wkDividend.status as WkTransactionStatusType
        }
      },
      status: "Pending",
      createdAt: new Date(Date.now()),
      dividendMonth: DateUtil.getYearAndMonth(DateUtil.getFirstDayOfLastMonth())
    };
    const dividend = await new SavingsDividendTransaction(dividendData).save();

    logger.info(`DB document for savings dividend ${wkDividend.id} was created successfully!`, {
      module: "TransactionService",
      method: "createSavingsDividend",
      data: { dividend, wkDividend }
    });

    const properties: TrackNotificationSavingsDividendCreation = {
      amount: CurrencyUtil.formatCurrency(
        Decimal.div(dividend.consideration.amount, 100).toNumber(),
        dividend.consideration.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      )
    };

    eventEmitter.emit(events.transaction.savingsDividendCreation.eventId, user, properties);

    // 4. Create charge transaction for dividend fee
    if (dividendFeeInCents > 0) {
      const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
        consideration: {
          currency: user.currency,
          amount: dividendFeeInCents
        },
        owner: user.id,
        portfolio: portfolio.id,
        chargeMethod: "cash",
        chargeType: "dividendCommission",
        linkedTransaction: dividend._id,
        activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
      };
      const charge = await TransactionService.createChargeTransaction(transactionData);

      logger.info(`DB document for ${dividend.id} dividend charge was created successfully!`, {
        module: "TransactionService",
        method: "createSavingsDividend",
        data: { dividend: dividend.id, charge: charge.id }
      });
    } else {
      logger.info(`Skipping dividend fee charge for ${dividend.id}!`, {
        module: "TransactionService",
        method: "createSavingsDividend",
        data: { dividend: dividend.id }
      });
    }
  }

  /**
   * Re-investing savings dividends essentially means creating savings top-ups for savings dividend documents.
   * These savings dividend documents are created as part of earlier cron tasks. For the dividends to have been
   * created, cash has already been given to the user in WK.
   *
   * When re-investing savings dividends, we check to see if there are any older 'stagnant' dividends that have
   * not been topped up because they are under the WK minimum.
   *
   * If there are, we aggregate all their amounts into the current savings dividend, and set them and their linked
   * top-ups as cancelled.
   *
   * @param dividend
   */
  public static async reinvestSavingsDividend(dividend: SavingsDividendTransactionDocument): Promise<void> {
    const updatedDividend = await TransactionService._aggregateLowAmountSavingsDividends(dividend.id);

    await DbUtil.populateIfNotAlreadyPopulated(updatedDividend, TransactionPopulationFieldsEnum.PORTFOLIO);
    const portfolio = updatedDividend.portfolio as PortfolioDocument;

    const savingsTopup = await PortfolioService.topupSavings(
      portfolio,
      updatedDividend.savingsProduct,
      Decimal.div(updatedDividend.consideration.amount, 100).toNumber(),
      { dividend: updatedDividend }
    );

    await dividend.updateOne({
      status: "PendingReinvestment",
      linkedSavingsTopup: savingsTopup.id
    });
  }

  public static async syncSavingsDividendLinkedToSavingsTopup(
    savingsDividend: SavingsDividendTransactionDocument
  ): Promise<void> {
    const linkedSavingsTopup = savingsDividend.linkedSavingsTopup as SavingsTopupTransactionDocument;

    if (linkedSavingsTopup.status === "Pending") {
      logger.info(`Linked savings topup ${linkedSavingsTopup.id} is still pending - aborting`, {
        module: "TransactionService",
        method: "syncSavingsDividendLinkedToSavingsTopup"
      });
      return;
    }

    if (linkedSavingsTopup.status === "Settled") {
      logger.info(`Linked savings topup ${linkedSavingsTopup.id} is settled`, {
        module: "TransactionService",
        method: "syncSavingsDividendLinkedToSavingsTopup"
      });
      await SavingsDividendTransaction.findByIdAndUpdate(savingsDividend.id, {
        status: "Settled",
        settledAt: new Date(Date.now())
      });
    } else {
      logger.error(
        `Linked savings topup ${linkedSavingsTopup.id} has unexpedted status: ${linkedSavingsTopup.status} `,
        {
          module: "TransactionService",
          method: "syncSavingsDividendLinkedToSavingsTopup",
          data: {
            savingsDividend: savingsDividend.id,
            linkedSavingsTopup: linkedSavingsTopup.id
          }
        }
      );
      throw new Error("Unexpected savings topup status linked to savings dividend");
    }
  }

  /**
   * Method that updates the transaction-level status, in addition to:
   * 1. Updating the fees + consideration amounts of the transaction
   * 2. Sending an investment success event
   *
   * This method can be called multiple times for the same transaction and will not cause race conditions
   * as the updates done in the transaction are idempotent, and the event is only sent once with the check
   * we do in the implementation. However, we should make sure this method **remains** idempotent.
   *
   * @param transaction
   * @param investmentProducts
   * @param options
   */
  public static async syncAssetTransaction(
    transaction: AssetTransactionDocument,
    investmentProducts: InvestmentProductsDictType,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<AssetTransactionDocument> {
    logger.info(`Syncing asset transaction ${transaction._id}`, {
      module: "TransactionService",
      method: "syncAssetTransaction"
    });

    if (transaction.hasTerminalStatus) {
      throw new Error(`Will not sync asset transaction ${transaction.id} as it has status ${transaction.status}`);
    }

    const [orders] = await Promise.all([
      Order.find(
        {
          transaction: transaction.id
        },
        null,
        { session: options?.session }
      ),
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER)
    ]);

    if (orders.length === 0) {
      throw new Error(`Trying to sync asset transaction ${transaction.id} with no orders!`);
    }

    if (orders.some((order) => !order.hasTerminalStatus)) {
      logger.info(
        `Will not sync asset transaction ${transaction._id} as not all its orders have a terminal status!`,
        {
          module: "TransactionService",
          method: "syncAssetTransaction"
        }
      );
      return transaction;
    }

    let updatedStatus: TransactionStatusType;
    if (orders.some((order) => order.isMatched)) {
      updatedStatus = "Settled";
    } else if (orders.every((order) => order.status === "Cancelled")) {
      updatedStatus = "Cancelled";
    }

    const originalTransaction = await AssetTransaction.findByIdAndUpdate(
      transaction.id,
      {
        status: updatedStatus,
        settledAt: updatedStatus === "Settled" ? new Date(Date.now()) : undefined,
        consideration:
          updatedStatus === "Settled"
            ? await TransactionService._calculateSettledAssetTransactionConsideration(transaction, orders)
            : undefined,
        fees: await TransactionService._calculateSettledAssetTransactionFees(
          orders,
          transaction.consideration.currency
        )
      },
      {
        new: false,
        session: options?.session
      }
    );

    logger.info(`Asset transaction ${transaction._id} is now ${updatedStatus}`, {
      module: "TransactionService",
      method: "syncAssetTransaction"
    });

    // We only want to emit the investment success event if the status was updated for the first time to 'Settled'
    // i.e. the original transaction had status 'Pending'
    if (originalTransaction.status === "Pending" && updatedStatus === "Settled") {
      await TransactionService._emitInvestmentSuccessEvent(
        transaction as AssetTransactionDocument,
        orders,
        investmentProducts
      );
    }

    return AssetTransaction.findById(transaction.id, null, { session: options?.session });
  }

  public static async getUserActivityTransactions(
    user: UserDocument,
    limit?: number
  ): Promise<TransactionDocument[]> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);

    // Retrieve transactions with a triple limit to account for post-query filtering and ensure enough results.
    const [transactions, investmentProducts, buyFxRates, sellFxRates] = await Promise.all([
      TransactionService._getUserActivityTransactions(user.id, 3 * limit),
      InvestmentProductService.getInvestmentProductsDict("isin", true, {
        listedOnly: false
      }),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL)
    ]);

    //  filter deposits that are linked to savings top-ups
    const transactionsWithDepositsFiltered = transactions.filter(this._filterOutDepositLinkedToSavingsTopup);

    // filter out asset transactions where:
    // 1. deposit does not have a truelayer status authorised or executed
    // 2. if direct debit, then deposit does not have at least collected DD status
    // Show the above transactions if the portfolio buy is repeating investment
    const transactionsWithAssetTransactionsFiltered = transactionsWithDepositsFiltered.filter((transaction) => {
      if (transaction.category !== "AssetTransaction") return true; // Keep the transaction if it's not an asset transaction

      if (transaction.status === "PendingDeposit") {
        const pendingDeposit = (transaction as AssetTransactionDocument)
          .pendingDeposit as DepositCashTransactionDocument;

        // one off investments (with deposit)
        const isTruelayerDepositSuccessful =
          pendingDeposit.providers?.truelayer?.status === "authorized" ||
          pendingDeposit.providers?.truelayer?.status === "executed";
        if (pendingDeposit.providers?.truelayer) {
          return isTruelayerDepositSuccessful;
        }

        // repeating investments (with direct debit)
        if (pendingDeposit.linkedAutomation) {
          return pendingDeposit.isDirectDebitPaymentCollected || pendingDeposit.createdWhilePendingMandate;
        }

        logger.error(`Deposit ${pendingDeposit._id} has nor truelayer status not linked automation`, {
          module: "TransactionService",
          method: "getUserActivityTransactions",
          data: {
            pendingDeposit: pendingDeposit._id,
            assetTransaction: transaction._id
          }
        });
        return false;
      }
      return true;
    });

    const transactionsSavingsTopupFiltered = transactionsWithAssetTransactionsFiltered
      .filter(this.filterOutSavingsTopUpTransactionLinkedToIncompleteDeposits)
      .filter(this._filterOutSavingsTopUpTransactionLinkedToSavingsDividend);

    return transactionsSavingsTopupFiltered.map((transaction) =>
      TransactionService.fillClientDisplayFields(
        user,
        transaction,
        investmentProducts,
        {
          BUY: buyFxRates,
          SELL: sellFxRates
        },
        {
          displayAmount: true,
          displayQuantity: true,
          executionWindow: true,
          displayExchangeRate: true,
          isCancellable: true,
          estimatedRealTimeCommission: true
        }
      )
    );
  }

  public static async getUserCashActivity(user: UserDocument, limit?: number): Promise<TransactionDocument[]> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);

    // Retrieve transactions with a triple limit to account for post-query filtering and ensure enough results.
    const [transactions, investmentProducts, buyFxRates, sellFxRates] = await Promise.all([
      TransactionService._fetchCashActivityTransactionDocuments(user.id, 3 * limit),
      InvestmentProductService.getInvestmentProductsDict("isin", true, {
        listedOnly: false
      }),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL)
    ]);

    // filter out
    // 1. asset transactions with a linked deposit that is not completed
    // 2. deposits that are linked to savings top-ups
    // 3. savings top-ups with a linked deposit that is not completed
    // 4. savings top-ups with a linked savings dividend
    // 5. cashback transactions with a linked asset transaction that is then linked to incomplete deposit
    const transactionsFiltered = transactions
      .filter(TransactionService._filterOutAssetTransactionLinkedToIncompleteDeposits)
      .filter(TransactionService._filterOutDepositLinkedToSavingsTopup)
      .filter(TransactionService.filterOutSavingsTopUpTransactionLinkedToIncompleteDeposits)
      .filter(TransactionService._filterOutSavingsTopUpTransactionLinkedToSavingsDividend)
      .filter(TransactionService._filterOutCashbackTransactionLinkedToIncompleteDeposits);

    return transactionsFiltered
      .sort((a, b) => {
        if (a.displayDate < b.displayDate) {
          return 1;
        } else if (a.displayDate > b.displayDate) {
          return -1;
        }
        return 0;
      })
      .map((transaction) =>
        TransactionService.fillClientDisplayFields(
          user,
          transaction,
          investmentProducts,
          {
            BUY: buyFxRates,
            SELL: sellFxRates
          },
          {
            displayAmount: true,
            displayQuantity: true,
            executionWindow: true,
            displayExchangeRate: true,
            isCancellable: true,
            estimatedRealTimeCommission: true
          }
        )
      );
  }

  /**
   * @description Fetches the transaction items for the investment activity of the user, with display fields populated.
   * @param user
   */
  public static async getUserInvestmentActivityTransactions(user: UserDocument): Promise<TransactionDocument[]> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);

    // Retrieve transactions with a triple limit to account for post-query filtering and ensure enough results.
    const [transactions, investmentProducts, buyFxRates, sellFxRates] = await Promise.all([
      TransactionService._fetchInvestmentActivityTransactionDocuments(user.id),
      InvestmentProductService.getInvestmentProductsDict("isin", true, {
        listedOnly: false
      }),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL)
    ]);

    // filter out asset transactions with a linked deposit that is not completed
    const transactionsFiltered = transactions.filter(
      TransactionService._filterOutAssetTransactionLinkedToIncompleteDeposits
    );

    return transactionsFiltered.map((transaction) =>
      TransactionService.fillClientDisplayFields(
        user,
        transaction,
        investmentProducts,
        {
          BUY: buyFxRates,
          SELL: sellFxRates
        },
        {
          displayAmount: true,
          displayQuantity: true,
          executionWindow: true,
          displayExchangeRate: true,
          isCancellable: true,
          estimatedRealTimeCommission: true
        }
      )
    );
  }

  public static async getUserBilling(userId: string, limit?: number) {
    const transactions = await ChargeTransaction.find({
      owner: userId,
      chargeType: { $in: ["custody", "subscription"] },
      status: { $in: ["Pending", "PendingWealthkernelCharge", "Settled"] }
    })
      .sort({ createdAt: -1 }) // Sorting in descending order by createdAt
      .limit(limit || 0) // Limiting the number of results for performance purposes
      .populate([{ path: "bankAccount" }, { path: "paymentMethod" }, { path: "subscription" }])
      .exec();

    // NOTE: Temporarily we are returning PendingWealthkernelCharge charges as Pending, because clients do not
    // support this status. This should be removed when clients are released with this status handled.
    return transactions.map((transaction) => {
      if (transaction.status === "PendingWealthkernelCharge") {
        return {
          ...transaction.toObject(),
          status: "Pending"
        };
      } else return transaction;
    });
  }

  public static async getDividendTransactionsForReturnsUpBy(
    ownerId: mongoose.Types.ObjectId,
    startDate?: Date
  ): Promise<DividendTransactionDocument[]> {
    const queryConditions: mongoose.FilterQuery<TransactionDocument> = { owner: ownerId };

    if (startDate) {
      queryConditions.createdAt = { $gte: startDate };
    }

    const dividendTransactions = await DividendTransaction.find({
      ...queryConditions,
      "providers.wealthkernel.status": "Settled"
    });

    return dividendTransactions;
  }

  /**
   * @description Method that returns the asset transaction that belongs to the given pending deposit id.
   * @param depositId
   */
  public static async getAssetTransactionLinkedToDeposit(depositId: string): Promise<AssetTransactionDocument> {
    const [investmentProducts, assetTransaction] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      AssetTransaction.findOne({ pendingDeposit: depositId }).populate([{ path: "orders" }, { path: "cashback" }])
    ]);

    const user = await UserService.getUser(assetTransaction.owner.toString());

    return TransactionService.fillClientDisplayFields(
      user,
      assetTransaction,
      investmentProducts
    ) as AssetTransactionDocument;
  }

  /**
   * Updates a cashback document with the required changes after an order in its linked transaction was cancelled.
   *
   * If that transaction is now cancelled i.e. all its orders are cancelled, then we also want to cancel the cashback.
   * If not, then we just want to update the cashback amount to reflect the remaining order amount.
   *
   * @param cancelledOrderAmount
   * @param transaction
   * @param options
   */
  public static async reduceCashbackAmountAfterCancelledOrder(
    cancelledOrderAmount: number,
    transaction: AssetTransactionDocument,
    options?: { session: mongoose.ClientSession }
  ): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.ORDERS, {
      session: options?.session
    });
    if (
      ![
        ExtendedAssetTransactionCategoryEnum.PORTFOLIO_BUY,
        ExtendedAssetTransactionCategoryEnum.ASSET_BUY
      ].includes(getExtendedPortfolioTransactionCategory(transaction))
    ) {
      return;
    }

    await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.CASHBACK);
    const cashback = transaction.cashback as CashbackTransactionDocument;
    if (!cashback) {
      return;
    }

    const totalBuyOrderAmount = getBuyOrderAmount(transaction);
    const amountIsStillCashbackEligible = Decimal.sub(totalBuyOrderAmount, cancelledOrderAmount)
      .div(100)
      .greaterThanOrEqualTo(MINIMUM_AMOUNT_FOR_CASHBACK);

    if (transaction.status === "Cancelled" || !amountIsStillCashbackEligible) {
      await CashbackTransaction.findByIdAndUpdate(
        transaction.cashback.id,
        {
          status: "Cancelled" as TransactionStatusType
        },
        { session: options?.session }
      );
    } else {
      const plan = ConfigUtil.getPricing()[cashback.price].plan;

      const updatedCashbackAmount = Decimal.sub(totalBuyOrderAmount, cancelledOrderAmount)
        .mul(CASHBACK_RATES[plan as plansConfig.PlanType])
        .round()
        .toNumber();

      await CashbackTransaction.findByIdAndUpdate(
        transaction.cashback.id,
        {
          "consideration.amount": updatedCashbackAmount
        },
        { session: options?.session }
      );
    }
  }

  /**
   * Returns all foreign currency rates (with spread) for the given plan.
   * @param user
   * @param plan
   * @param spreadSide
   */
  public static async getAllForeignCurrencyRatesWithSpread(
    user: UserDocument,
    plan: plansConfig.PlanType,
    spreadSide: FXSpreadSide
  ): Promise<ForeignCurrencyRatesType> {
    return Object.fromEntries(
      await Promise.all(
        CurrencyUtil.getForeignCurrencies(user).map(async (currency) => {
          return [
            currency,
            await calculateFXRateWithSpread(spreadSide, plan, {
              userCurrency: user.currency,
              targetCurrency: currency as currenciesConfig.MainCurrencyType,
              rounding: true
            })
          ];
        })
      )
    );
  }

  /**
   * @description This method is used as a filter
   * It filters out savings topups transactions where
   * deposit does not have a truelayer status authorised or executed
   * AND are not repeating Investments
   * @param transaction (transactionDocument)
   */
  public static filterOutSavingsTopUpTransactionLinkedToIncompleteDeposits(
    transaction: TransactionDocument
  ): boolean {
    if (transaction.category !== "SavingsTopupTransaction") return true; // Keep the transaction if it's not a savings topup transaction

    if (transaction.status === "PendingDeposit") {
      const pendingDeposit = (transaction as SavingsTopupTransactionDocument)
        .pendingDeposit as DepositCashTransactionDocument;

      // one off investments (with deposit)
      if (pendingDeposit.providers?.truelayer) {
        return pendingDeposit.isTruelayerFlowCompleted;
      }

      // repeating investments (with direct debit)
      if (pendingDeposit.linkedAutomation) {
        return pendingDeposit.isDirectDebitPaymentCollected || pendingDeposit.createdWhilePendingMandate;
      }

      logger.error(`Deposit ${pendingDeposit._id} isn't completed`, {
        module: "TransactionService",
        method: "_filterOutSavingsTopUpTransactionLinkedToIncompleteDeposits",
        data: {
          pendingDeposit: pendingDeposit._id,
          savingsTopup: transaction._id
        }
      });
      return false;
    }
    return true;
  }

  public static async processGoCardlessPaymentEvent(event: EventType): Promise<void> {
    const payment = event.links["payment"];
    const depositTransaction = await DepositCashTransaction.findOne({
      "directDebit.providers.gocardless.id": payment
    }).populate("owner");

    if (!depositTransaction) {
      throw new Error(
        `Could not update transaction ${payment} because we don't have a reference to it in our database`
      );
    }

    const user = depositTransaction.owner as UserDocument;

    switch (event.action) {
      case "charged_back":
        eventEmitter.emit(events.transaction.directDebitDepositChargeback.eventId, user, {
          amount: depositTransaction.consideration.amount,
          currency: depositTransaction.consideration.currency,
          id: depositTransaction.id
        });

        await DepositCashTransaction.findByIdAndUpdate(depositTransaction.id, {
          status: "Rejected",
          "directDebit.providers.gocardless.status": event.action
        });
        break;
      case "cancelled":
      case "failed":
        await DepositCashTransaction.findByIdAndUpdate(depositTransaction.id, {
          status: "Rejected",
          "directDebit.providers.gocardless.status": event.action
        });
        break;
      case "submitted":
        await DepositCashTransaction.findByIdAndUpdate(depositTransaction.id, {
          "directDebit.providers.gocardless.status": "submitted"
        });
        break;
      case "confirmed":
        await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
          await CreditTicketService.createCreditTicketForDeposit(depositTransaction, {
            session,
            requestInstantly: true
          });

          await DepositCashTransaction.findByIdAndUpdate(
            depositTransaction.id,
            {
              "directDebit.providers.gocardless.status": "confirmed"
            },
            { session }
          );
        });
        break;
      case "paid_out":
        await DepositCashTransaction.findByIdAndUpdate(depositTransaction.id, {
          "directDebit.providers.gocardless.status": "paid_out"
        });
        break;
      default:
        logger.info(`Not processing payment events of action ${event.action}`, {
          module: "TransactionService",
          method: "processGoCardlessPaymentEvent",
          data: {
            payment: payment,
            action: event.action
          }
        });
        break;
    }
  }

  public static addBankAccountToDeposit(
    id: string,
    bankAccountId: string,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<DepositCashTransactionDocument> {
    return DepositCashTransaction.findByIdAndUpdate(
      id,
      { bankAccount: bankAccountId },
      { new: true, session: options?.session }
    );
  }

  public static async processSavingsTopupPendingDeposit(topup: SavingsTopupTransactionDocument): Promise<void> {
    logger.info(`Processing ${topup.id} Savings Topup`, {
      module: "TransactionService",
      method: "processSavingsTopupPendingDeposit",
      data: {
        topupId: topup.id
      }
    });

    const linkedDeposit = topup.pendingDeposit as DepositCashTransactionDocument;

    await DbUtil.populateIfNotAlreadyPopulated(linkedDeposit, TransactionPopulationFieldsEnum.CREDIT_TICKET);
    const creditTicket = linkedDeposit?.linkedCreditTicket as CreditTicketDocument;

    if (linkedDeposit.status === "Settled" || (creditTicket?.hasBeenCredited && creditTicket?.hasNoPendingCheck)) {
      await TransactionService._markPendingDepositSavingsTopupAsPending(topup);
    } else if (["Cancelled", "Rejected"].includes(linkedDeposit.status)) {
      await TransactionService._markPendingDepositSavingsTopupAsDepositFailed(topup);
    }
  }

  public static async createGoCardlessDirectDebitPayment(deposit: DepositCashTransactionDocument): Promise<void> {
    if (!deposit.linkedAutomation || deposit?.directDebit?.providers?.gocardless?.id) {
      throw new BadRequestError(`Cannot create direct debit payment for deposit ${deposit._id}`);
    }

    const automation = deposit.linkedAutomation as TopUpAutomationDocument | SavingsTopUpAutomationDocument;
    const mandate = automation.mandate as MandateDocument;

    const paymentResponse = await ProviderService.getRepeatingDepositPaymentService(
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    ).createDirectDebitDeposit({
      consideration: {
        amount: Decimal.div(deposit.consideration.amount, 100).toNumber(),
        currency: deposit.consideration.currency
      },
      mandate: {
        providers: {
          gocardless: mandate.providers.gocardless
        }
      },
      collectionDate: deposit.directDebit.collectionRequestDate,
      metadata: {
        wealthyhoodId: deposit.id
      }
    });

    await DepositCashTransaction.updateOne(
      {
        _id: deposit._id
      },
      {
        "directDebit.providers": paymentResponse.providerData
      }
    );
  }

  public static async createBankTransferPaymentAndUpdateDeposit(
    deposit: DepositCashTransactionDocument,
    stage: TransferWithIntermediaryStageEnum
  ): Promise<DepositCashTransactionDocument> {
    await DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.OWNER);
    const user = deposit.owner as UserDocument;

    const { id } = await ProviderService.getBankTransferPaymentService(
      user.companyEntity
    ).createBankTransferPayment({
      amount: deposit.consideration.amount,
      reference: deposit.bankReference,
      linkedTransactionId: deposit.id,
      providerData: {
        accountId: deposit.transferWithIntermediary[stage].incomingPayment.providers.devengo.accountId
      },
      iban:
        stage === TransferWithIntermediaryStageEnum.ACQUISITION
          ? process.env.DEVENGO_BANK_TRANSFER_COLLECTION_IBAN
          : process.env.WEALTHKERNEL_WEALTHYHOOD_EUROPE_IBAN
    });

    return DepositCashTransaction.findByIdAndUpdate(
      deposit.id,
      {
        [`transferWithIntermediary.${stage}.outgoingPayment.providers.devengo`]: {
          id,
          status: "created"
        }
      },
      { new: true }
    );
  }

  public static async processAssetTransactionPendingDeposit(
    assetTransaction: AssetTransactionDocument
  ): Promise<void> {
    const isRepeatingInvestment = !!assetTransaction.linkedAutomation;
    logger.info(`Processing ${assetTransaction.id} asset transaction`, {
      module: "TransactionService",
      method: "processAssetTransactionPendingDeposit",
      data: {
        transactionId: assetTransaction.id,
        isRepeatingInvestment
      }
    });

    const linkedDeposit = assetTransaction.pendingDeposit as DepositCashTransactionDocument;

    await DbUtil.populateIfNotAlreadyPopulated(linkedDeposit, TransactionPopulationFieldsEnum.CREDIT_TICKET);
    const creditTicket = linkedDeposit?.linkedCreditTicket as CreditTicketDocument;

    if (linkedDeposit.status === "Settled" || (creditTicket?.hasBeenCredited && creditTicket?.hasNoPendingCheck)) {
      await TransactionService._markPendingDepositAssetTransactionAsPending(assetTransaction, {
        emitEvents: !isRepeatingInvestment // We only want to emit investment creation events if the transaction is not automated
      });
    } else if (["Cancelled", "Rejected"].includes(linkedDeposit.status)) {
      await TransactionService._markPendingDepositAssetTransactionAsDepositFailed(assetTransaction);
    }
  }

  public static async createWealthkernelDirectDebitPayment(
    deposit: DepositCashTransactionDocument
  ): Promise<void> {
    if (
      !deposit.linkedAutomation ||
      deposit?.directDebit?.providers?.wealthkernel?.id ||
      deposit?.providers?.wealthkernel?.id
    ) {
      throw new BadRequestError(`Cannot create expectation for deposit ${deposit._id}`);
    }

    const portfolio = deposit.portfolio as PortfolioDocument;
    const automation = deposit.linkedAutomation as TopUpAutomationDocument | SavingsTopUpAutomationDocument;
    const mandate = automation.mandate as MandateDocument;

    const paymentResponse = await ProviderService.getRepeatingDepositPaymentService(
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
    ).createDirectDebitDeposit({
      consideration: {
        amount: Decimal.div(deposit.consideration.amount, 100).toNumber(),
        currency: deposit.consideration.currency
      },
      mandate: {
        providers: {
          wealthkernel: mandate.providers.wealthkernel
        }
      },
      portfolio: {
        providers: {
          wealthkernel: { id: portfolio.providers?.wealthkernel?.id }
        }
      },
      collectionDate: deposit.directDebit.collectionRequestDate,
      metadata: {
        wealthyhoodId: deposit.id
      }
    });

    await DepositCashTransaction.findByIdAndUpdate(deposit.id, {
      "directDebit.providers": paymentResponse.providerData
    });
  }

  /**
   * @description Helper method to get all data needed for the monthly investment prompt.
   *
   * @param userId User ID to check
   * @returns Object containing data needed for repeating investment prompt decision
   */
  public static async getMonthlyInvestmentPromptData(userId: string): Promise<{
    hasActiveRepeatingInvestment: boolean;
    previousAssetTransactions: AssetTransactionDocument[];
  }> {
    // Fetch automations and transactions concurrently
    const [{ data: activeAutomations }, previousAssetTransactions] = await Promise.all([
      AutomationService.getAutomations({
        owner: userId,
        categories: ["TopUpAutomation"],
        activeOnly: true
      }),
      AssetTransaction.find({
        owner: userId,
        status: { $in: ["Pending", "Settled"] }
      })
    ]);

    return {
      hasActiveRepeatingInvestment: activeAutomations.length > 0,
      previousAssetTransactions
    };
  }

  /**
   * @description Checks if the user should be prompted to set up a monthly investment.
   *
   * Returns true if:
   * 1. The user doesn't have an active repeating investment automation
   * 2. No gift is used to make this investment
   * 3. The user doesn't have any previous pending or settled asset transactions
   *
   * @param user
   * @param transactionId The ID of the created transaction to check whether to prompt conversion to repeating
   * @param promptData Data needed for the monthly investment prompt
   * @returns Promise<boolean> True if the user should be prompted, false otherwise
   */
  public static shouldPromptForMonthlyInvestment(
    user: UserDocument,
    transactionId: string,
    promptData: {
      hasActiveRepeatingInvestment: boolean;
      isGiftUsed: boolean;
      previousAssetTransactions: AssetTransactionDocument[];
    }
  ): boolean {
    if (user.isConvertingPortfolio || user.hasConvertedPortfolio) {
      return false;
    }

    const previousTransactionsExcludingCurrent = promptData.previousAssetTransactions.filter(
      (transaction) => transaction.id !== transactionId
    );

    return (
      !promptData.isGiftUsed &&
      !promptData.hasActiveRepeatingInvestment &&
      previousTransactionsExcludingCurrent.length === 0
    );
  }

  public static async getPortfolioIdsOfDailySnapshotTransactions(): Promise<ObjectId[]> {
    return Transaction.distinct<ObjectId>("portfolio", {
      $or: [
        {
          category: "SavingsTopupTransaction",
          status: { $in: ["Pending", "Settled"] }
        },
        {
          category: "DepositCashTransaction",
          status: "Settled"
        }
      ]
    });
  }

  public static async isTransactionBankAccountNameChecked(deposit: DepositCashTransactionDocument) {
    await DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.BANK_ACCOUNT);
    const bankAccount = deposit.bankAccount as BankAccountDocument;

    return bankAccount.isProviderActive;
  }

  /**
   * @description This method is used for bulk operations triggered usually from cron jobs.
   * It adds error handling in order to just report the error but not stop the execution of bulk operation.
   *
   * @param deposit (DepositCashTransaction)
   * @public
   */
  public static async syncDepositWkStatusSafely(deposit: DepositCashTransactionDocument): Promise<void> {
    try {
      await DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.OWNER);
      const user = deposit.owner as UserDocument;

      // Retrieve deposit & status from wealthkernel
      const wkDeposit = await ProviderService.getBrokerageService(user.companyEntity).retrieveDeposit(
        deposit.providers.wealthkernel.id
      );
      await TransactionService._updateWkDepositStatus(deposit, wkDeposit.status);
    } catch (err) {
      captureException(err);
      logger.error(`Syncing with wealthkernel failed for deposit ${deposit._id}`, {
        module: "TransactionService",
        method: "syncDepositWkStatusSafely",
        data: { transactionId: deposit._id, userId: (deposit.owner as UserDocument)._id, error: err }
      });
    }
  }

  public static async emitDepositCreatedEvent(
    deposit: DepositCashTransactionDocument,
    options: { triggeredByAutomation: boolean }
  ): Promise<void> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.BANK_ACCOUNT)
    ]);

    const depositsCount = await DepositCashTransaction.countDocuments({
      owner: deposit.owner
    });

    const isFirstDeposit = depositsCount == 1;
    if (isFirstDeposit) {
      eventEmitter.emit(events.transaction.firstDepositCreation.eventId, deposit.owner);
    }

    const properties: TrackPropertiesType = {
      isFirst: isFirstDeposit,
      amount: Decimal.div(deposit.consideration.amount, 100).toNumber(),
      currency: deposit.consideration.currency,
      truelayerId: (deposit.bankAccount as BankAccountDocument)?.truelayerProviderId
    };

    const { triggeredByAutomation } = options;
    eventEmitter.emit(events.transaction.depositCreation.eventId, deposit.owner, properties, {
      triggeredByAutomation
    });
  }

  /**
   * Reverse a bank transfer deposit by:
   * 1) Creating a withdrawal to the provided bank account for the same amount, without changing portfolio cash.
   * 2) Marking the deposit as reversed and settled.
   * 3) Settling any linked credit ticket to prevent future processing.
   */
  public static async reverseDeposit(
    deposit: DepositCashTransactionDocument,
    bankAccountId: string
  ): Promise<void> {
    await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
      await DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.CREDIT_TICKET);
      const creditTicket = deposit.linkedCreditTicket as CreditTicketDocument;

      if (!deposit.hasDevengoCollectionStageCompleted) {
        throw new Error("Cannot reverse deposit as funds not confirmed at collection");
      } else if (creditTicket?.status === "Pending") {
        throw new Error("Cannot reverse deposit as credit ticket is pending");
      }

      // Mark deposit as reversed + settle both deposit and any linked credit ticket to prevent future processing
      await DepositCashTransaction.findByIdAndUpdate(
        deposit.id,
        {
          reversed: true,
          status: "Settled",
          "providers.wealthkernel.status": "Settled",
          "providers.wealthkernel.settledAt": new Date(Date.now()),
          settledAt: new Date(Date.now())
        },
        { session }
      );

      if (creditTicket && deposit.inInstantMoneyFlow) {
        await CreditTicketRepository.settleCreditTicket(creditTicket.id, { session });
      }

      await TransactionService.withdraw(
        deposit.portfolio.toString(),
        Decimal.div(deposit.consideration.amount, 100).toNumber(),
        {
          bankAccountId,
          shouldUpdateCash: false,
          session
        }
      );
    });
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  private static _filterChargesByCompanyEntity(
    charges: ChargesType,
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): ChargesType {
    return Object.entries(charges).reduce((filteredCharges, [chargeType, chargeRecords]) => {
      const filteredRecord: Record<string, TransactionCharge[]> = {};

      Object.entries(chargeRecords).forEach(([recordKey, chargeArray]) => {
        filteredRecord[recordKey] = chargeArray.filter((charge) => charge.companyEntity === companyEntity);
      });

      filteredCharges[chargeType as keyof ChargesType] = filteredRecord;

      return filteredCharges;
    }, {} as ChargesType);
  }

  private static async _getStockPortfolioUpdatePreview(
    holdingsDict: HoldingsDictType,
    asset: AssetType,
    order: PendingOrderType,
    user: UserDocument,
    investmentProduct: InvestmentProductDocument
  ): Promise<TransactionPreview> {
    const expressOrderData = OrderService.getSingleOrderToCreate(order, {
      investmentProduct,
      userCurrency: user.currency,
      userCompanyEntity: user.companyEntity
    });

    const foreignCurrencyRates = await TransactionService._getOrdersForeignCurrencyRatesWithSpread(
      user,
      [expressOrderData],
      { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct },
      user.subscription.plan,
      order.side === "buy" ? FXSpreadSide.BUY : FXSpreadSide.SELL
    );

    const cashbackAmount = await TransactionService.getCashbackAmount(
      expressOrderData?.consideration?.amount ?? 0,
      user
    );

    return {
      executionWindow: {
        express: ExecutionWindowUtil.getAssetTransactionExecutionWindow([expressOrderData], user.currency, {
          investmentProducts: { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct }
        })
      },
      fees: {
        express: OrderService.calculateFeesForSingleOrder(user.subscription.plan, expressOrderData, {
          investmentProduct,
          userCurrency: user.currency
        })
      },
      orders: {
        express: OrderUtil.formatOrdersPreview([expressOrderData], {
          investmentProductsDict: { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct },
          userCurrency: user.currency,
          // Realtime fees for stock orders are 0, but this is written in a way
          // to not have to be modified if it changes in the future.
          fees: expressOrderData?.fees?.realtimeExecution?.amount ?? 0
        })
      },
      foreignCurrencyRates,
      cashback: order.side === "buy" ? Decimal.div(cashbackAmount, 100).toDecimalPlaces(2).toNumber() : 0
    };
  }

  private static async _getETFPortfolioUpdatePreview(
    holdingsDict: HoldingsDictType,
    asset: AssetType,
    order: PendingOrderType,
    user: UserDocument,
    investmentProduct: InvestmentProductDocument
  ): Promise<TransactionPreview> {
    // Get the order data for both smart and standard execution
    const [expressOrderData, smartOrderData] = [true, false].map((executeEtfOrdersInRealtime) =>
      OrderService.getSingleOrderToCreate(order, {
        investmentProduct,
        userCurrency: user.currency,
        userCompanyEntity: user.companyEntity,
        executeEtfOrdersInRealtime
      })
    );

    // Apply fees to both scenarios
    const [expressFees, smartFees] = [expressOrderData, smartOrderData].map((orderData) =>
      OrderService.calculateFeesForSingleOrder(user.subscription.plan, orderData, {
        investmentProduct,
        userCurrency: user.currency
      })
    );

    // Retrieve foreign currency rates (same for either smart/standard execution)
    const foreignCurrencyRates = await TransactionService._getOrdersForeignCurrencyRatesWithSpread(
      user,
      [expressOrderData], // Using either order data is fine as FX rates are the same
      { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct },
      user.subscription.plan,
      order.side === "buy" ? FXSpreadSide.BUY : FXSpreadSide.SELL
    );

    // Get execution windows for both scenarios
    const executionWindow = {
      express: ExecutionWindowUtil.getAssetTransactionExecutionWindow([expressOrderData], user.currency, {
        investmentProducts: { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct }
      }),
      smart: ExecutionWindowUtil.getAssetTransactionExecutionWindow([smartOrderData], user.currency, {
        investmentProducts: { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct }
      })
    };

    // Retrieve cashback amount (same for either smart/standard execution)
    const cashbackAmount = await TransactionService.getCashbackAmount(
      expressOrderData?.consideration?.amount ?? 0,
      user
    );

    const investmentProductsDict = { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct };

    // Format the orders
    const [formattedExpressOrders, formattedSmartOrders] = [
      OrderUtil.formatOrdersPreview([expressOrderData], {
        investmentProductsDict,
        userCurrency: user.currency,
        fees: expressFees?.realtimeExecution?.amount ?? 0
      }),
      OrderUtil.formatOrdersPreview([smartOrderData], {
        investmentProductsDict,
        userCurrency: user.currency,
        // Realtime fees for smart orders are 0, but this is written in a way
        // to not have to be modified if it changes in the future.
        fees: smartFees?.realtimeExecution?.amount ?? 0
      })
    ];

    const shouldIncludeSmart = order.side === "buy";

    return {
      executionWindow: user.isRealtimeETFExecutionEnabled
        ? { express: executionWindow.express, ...(shouldIncludeSmart && { smart: executionWindow.smart }) }
        : { smart: executionWindow.smart },
      fees: user.isRealtimeETFExecutionEnabled
        ? {
            express: expressFees,
            ...(shouldIncludeSmart && { smart: smartFees })
          }
        : { smart: smartFees },
      orders: user.isRealtimeETFExecutionEnabled
        ? {
            express: formattedExpressOrders,
            ...(shouldIncludeSmart && { smart: formattedSmartOrders })
          }
        : { smart: formattedSmartOrders },
      foreignCurrencyRates,
      cashback: order.side === "buy" ? Decimal.div(cashbackAmount, 100).toDecimalPlaces(2).toNumber() : 0,
      willResultInLowQuantityHolding:
        user.isRealtimeETFExecutionEnabled &&
        order.side === "sell" &&
        holdingsDict[asset].quantity !== order.quantity &&
        PortfolioUtil.estimateHoldingAmount(Decimal.sub(holdingsDict[asset].quantity, order.quantity).toNumber(), {
          investmentProduct,
          userCurrency: user.currency
        }) < MIN_ALLOWED_ASSET_INVESTMENT
    };
  }

  /**
   * @description This method is used as a filter
   * It filters out savings topups transactions
   * that are dividend reinvestment and linked to a savings dividend
   * @param transaction (transactionDocument)
   * @private
   */
  private static _filterOutSavingsTopUpTransactionLinkedToSavingsDividend(
    transaction: TransactionDocument
  ): boolean {
    if (transaction.category !== "SavingsTopupTransaction") return true; // Keep the transaction if it's not a savings topup transaction

    if ((transaction as SavingsTopupTransactionDocument).linkedSavingsDividend) {
      return false;
    }

    return true;
  }

  private static async _emitInvestmentSuccessEvent(
    transaction: AssetTransactionDocument,
    orders: OrderDocument[],
    investmentProductsDict: InvestmentProductsDictType
  ): Promise<void> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.CASHBACK),
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER)
    ]);

    const owner = transaction.owner as UserDocument;
    const matchedOrders = orders.filter((order) => order?.providers?.wealthkernel?.status === "Matched");
    const isFirst = await UserService.userHasNoSettledInvestments(transaction.owner as UserDocument);

    // Fire asset transaction success event
    logger.info("eventEmitter.emit - assetTransactionSuccess", {
      module: "TransactionService",
      method: "_emitInvestmentSuccessEvent"
    });

    let amountToUse;
    if (transaction.portfolioTransactionCategory == "update") {
      const { side, consideration } = orders[0];
      amountToUse =
        side == "Buy"
          ? new Decimal(consideration.originalAmount ?? consideration?.amount).div(100)
          : new Decimal(consideration.amount).div(100);
    } else {
      amountToUse = new Decimal(transaction.originalInvestmentAmount ?? transaction.consideration?.amount).div(
        100
      );
    }

    const { portfolioTransactionCategory, fees, cashback } = transaction;
    const side = (
      portfolioTransactionCategory == "update" ? orders[0].side.toLowerCase() : portfolioTransactionCategory
    ) as "buy" | "sell";
    const cashbackAmount = Decimal.div(
      (cashback as CashbackTransactionDocument)?.consideration?.amount ?? 0,
      100
    ).toNumber();
    if (!transaction.populated("orders")) {
      await transaction.populate("orders");
    }

    let category: TrackTransactionInfoCategoryType;
    let assetName: string;
    if (portfolioTransactionCategory === "update") {
      const investmentProduct = investmentProductsDict[transaction.orders[0].isin];
      assetName = ASSET_CONFIG[investmentProduct.commonId].simpleName;
      category = ASSET_CONFIG[investmentProduct.commonId].category;
    } else {
      category = "portfolio";
    }

    const properties: TrackTransactionInfoType = {
      isFirst,
      side,
      category,
      assetName,
      amount: amountToUse.toNumber(),
      currency: transaction.consideration?.currency ?? owner.currency,
      cashbackAmount: cashback ? cashbackAmount : 0,
      fxFees: fees?.fx?.amount ?? 0,
      commissionFees: fees?.commission?.amount ?? 0,
      executionSpreadFees: fees?.executionSpread?.amount ?? 0,
      redeemedGift: !!transaction.pendingGift,
      frequency: transaction.linkedAutomation ? "repeating" : "one-off"
    };

    const assetId =
      matchedOrders.length === 1 ? InvestmentUniverseUtil.getAssetIdFromIsin(matchedOrders[0].isin) : null;

    let notificationProperties: TrackNotificationOrderSettled;
    let type: string;
    if (!transaction.linkedUserDataRequest) {
      type = "assetTransaction";

      // We should only add OneSignal properties when transaction is not part of deletion request.
      notificationProperties = {
        side: side,
        asset: category === "portfolio" ? "your portfolio" : ASSET_CONFIG[assetId].simpleName,
        /** If user sells etf amount must be shares, otherwise it will be £ cash amount */
        amount:
          category === "etf" && side === "sell"
            ? `${matchedOrders[0].quantity} shares`
            : CurrencyUtil.formatCurrency(
                amountToUse.toNumber(),
                owner.currency,
                ConfigUtil.getDefaultUserLocale(owner.residencyCountry)
              )
      };
    }

    eventEmitter.emit(
      events.transaction.investmentSuccess.eventId,
      owner,
      properties,
      {
        type
      },
      notificationProperties
    );
  }

  /**
   * @description This method checks if there is a cashbacked linked to the assetTransaction.
   * If there is, it cancels it.
   * @param assetTransaction (AssetTransactionDocument)
   * @private
   */
  private static async _cancelCashbackLinkedToAssetTransaction(assetTransaction: AssetTransactionDocument) {
    const transactionIsAssetBuy =
      assetTransaction.portfolioTransactionCategory === "update" && assetTransaction.orders[0].side === "Buy";
    const transactionIsPortfolioBuy = assetTransaction.portfolioTransactionCategory === "buy";
    // update the status of the cashback transaction
    if (transactionIsPortfolioBuy || transactionIsAssetBuy) {
      if (!assetTransaction.populated("cashback")) {
        await assetTransaction.populate("cashback");
      }

      if (assetTransaction.cashback) {
        await CashbackTransaction.findByIdAndUpdate(assetTransaction.cashback.id, {
          status: "Rejected" as TransactionStatusType
        });
        logger.info(
          `Rejected cashback ${assetTransaction.cashback.id} corresponding to linkedAssetTransaction ${assetTransaction._id}`,
          {
            module: "TransactionService",
            method: "_cancelCashbackLinkedToAssetTransaction"
          }
        );
      }
    }
  }

  /**
   * @description This method is used as a filter
   * It filters out deposits that are linked with an Asset Transaction that is not cancelled or rejected
   * @param transaction (transactionDocument)
   * @private
   */
  private static _filterOutDepositLinkedToAssetTransaction(transaction: TransactionDocument): boolean {
    if (transaction.category !== "DepositCashTransaction") {
      return true; // Keep the transaction if it's not a deposit
    }
    const linkedAssetTransaction = (transaction as DepositCashTransactionDocument).linkedAssetTransaction;

    if (!linkedAssetTransaction) {
      return true; // Keep the transaction if there's no linked asset transaction
    }

    // Check if the linked transaction is neither Cancelled nor Rejected - in that case the investment won't proceed
    // so we need to display the corresponding deposit to the user.
    const isLinkedTransactionCancelledOrRejected =
      linkedAssetTransaction.status === "Cancelled" || linkedAssetTransaction.status === "Rejected";

    // If the linked transaction is valid, filter out the transaction. Otherwise, keep it.
    return isLinkedTransactionCancelledOrRejected;
  }

  /**
   * @description This method is used as a filter
   * It filters out deposits that are linked with a Credit Ticket that is credited
   * @param transaction (transactionDocument)
   * @private
   */
  private static _filterOutDepositLinkedToCreditedCreditTicket(transaction: TransactionDocument): boolean {
    if (transaction.category !== "DepositCashTransaction") {
      return true; // Keep the transaction if it's not a deposit
    }
    const linkedCreditTicket = (transaction as DepositCashTransactionDocument)
      .linkedCreditTicket as CreditTicketDocument;
    if (!linkedCreditTicket) {
      return true; // Keep the transaction if there's no linked credit ticket
    }

    return linkedCreditTicket.status !== "Credited"; // Filter out the transaction if there's a credited credit ticket
  }

  /**
   * @description This method is used as a filter for asset transactions.
   *
   * It filters out asset transactions where:
   * 1. linked deposit does not have a truelayer status authorised or executed.
   * 2. if repeating, then DD deposit does not have at least collected DD status.
   *
   * @param transaction (transactionDocument)
   * @private
   */
  private static _filterOutAssetTransactionLinkedToIncompleteDeposits(transaction: TransactionDocument): boolean {
    if (transaction.category !== "AssetTransaction") return true; // Keep the transaction if it's not an asset transaction

    if (transaction.status === "PendingDeposit") {
      const pendingDeposit = (transaction as AssetTransactionDocument)
        .pendingDeposit as DepositCashTransactionDocument;

      // one off investments (with deposit)
      if (pendingDeposit.providers?.truelayer) {
        return pendingDeposit.isTruelayerFlowCompleted;
      }

      // repeating investments (with direct debit)
      // we want to display the deposit if it has been collected (which means that the bank account has now been charged)
      // or if it was created while the mandate was pending
      if (pendingDeposit.linkedAutomation) {
        return pendingDeposit.isDirectDebitPaymentCollected || pendingDeposit.createdWhilePendingMandate;
      }

      logger.error(`Deposit ${pendingDeposit._id} has no truelayer status nor linked automation`, {
        module: "TransactionService",
        method: "_filterOutAssetTransactionLinkedToIncompleteDeposits",
        data: {
          pendingDeposit: pendingDeposit._id,
          assetTransaction: transaction._id
        }
      });
      return false;
    }
    return true;
  }

  /**
   * @description Filters out repeating asset transactions with non-settled direct debit payments.
   * @param transaction
   * @returns
   */
  private static _filterOutRepeatingAssetTransactionsWithNonSettledDeposits(
    transaction: TransactionDocument
  ): boolean {
    return !(
      transaction.category === "AssetTransaction" &&
      (transaction as AssetTransactionDocument).linkedAutomation &&
      transaction.status === "PendingDeposit"
    );
  }

  /**
   * @description This method is used as a filter for cashback transactions.
   *
   * It filters out cashback transactions where:
   * 1. linked asset transaction has a linked deposit that does not have a truelayer status authorised or executed.
   * 2. linked asset transaction if repeating, then DD deposit does not have at least collected DD status.
   *
   * @param transaction (transactionDocument)
   * @private
   */
  private static _filterOutCashbackTransactionLinkedToIncompleteDeposits(
    transaction: TransactionDocument
  ): boolean {
    if (transaction.category !== "CashbackTransaction") return true; // Keep the transaction if it's not a cashback transaction

    const assetTransaction = (transaction as CashbackTransactionDocument)
      .linkedAssetTransaction as AssetTransactionDocument;

    if (!assetTransaction) {
      return false;
    }

    if (assetTransaction.status === "PendingDeposit") {
      const pendingDeposit = (assetTransaction as AssetTransactionDocument)
        .pendingDeposit as DepositCashTransactionDocument;

      // one off investments (with deposit)
      if (pendingDeposit.providers?.truelayer) {
        return pendingDeposit.isTruelayerFlowCompleted;
      }

      // repeating investments (with direct debit)
      // we want to display the deposit if it has been collected (which means that the bank account has now been charged)
      // or if it was created while the mandate was pending
      if (pendingDeposit.linkedAutomation) {
        return pendingDeposit.isDirectDebitPaymentCollected || pendingDeposit.createdWhilePendingMandate;
      }

      logger.error(`Deposit ${pendingDeposit._id} has no truelayer status nor linked automation`, {
        module: "TransactionService",
        method: "_filterOutCashbackTransactionLinkedToIncompleteDeposits",
        data: {
          pendingDeposit: pendingDeposit._id,
          assetTransaction: assetTransaction._id
        }
      });
      return false;
    }
    return true;
  }

  /**
   * Checks if there are any active repeating investments in the given transactions array
   * @param transactions Array of asset transactions to check
   * @returns boolean indicating if there are any active repeating investments
   * @private
   */
  private static _hasActiveRepeatingInvestments(transactions: AssetTransactionDocument[]): boolean {
    return transactions.some((transaction) => {
      // For PendingDeposit status, check deposit conditions
      if (transaction.status === "PendingDeposit") {
        const pendingDeposit = transaction.pendingDeposit as DepositCashTransactionDocument;
        if (!pendingDeposit?.linkedAutomation) {
          return false;
        }
        // Return false if deposit has failed direct debit, true otherwise
        return !pendingDeposit.hasFailedDirectDebit;
      }

      // For Pending or Settled status, return true
      return transaction.status === "Pending" || transaction.status === "Settled";
    });
  }

  private static async _createCashbackDeposit(cashback: CashbackTransactionDocument): Promise<void> {
    logger.info(`Creating cashback bonus for doc ${cashback.id}`, {
      module: "TransactionService",
      method: "_createCashbackDeposit"
    });

    await DbUtil.populateIfNotAlreadyPopulated(cashback, TransactionPopulationFieldsEnum.PORTFOLIO);
    const portfolio = cashback.portfolio as PortfolioDocument;

    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;

    const bonusPayment = await ProviderService.getBrokerageService(user.companyEntity).createBonus({
      destinationPortfolio: portfolio.providers?.wealthkernel?.id,
      consideration: {
        currency: user.currency,
        amount: Decimal.div(cashback.consideration.amount, 100).toNumber()
      },
      clientReference: cashback.id
    });

    logger.info(`Created cashback bonus for doc ${cashback.id} with WK id ${bonusPayment.id}`, {
      module: "TransactionService",
      method: "_createCashbackDeposit"
    });

    await CashbackTransaction.findByIdAndUpdate(cashback.id, {
      "deposit.providers.wealthkernel": {
        status: "Created",
        id: bonusPayment.id,
        submittedAt: new Date(Date.now())
      }
    });
  }

  private static async _calculateSettledAssetTransactionFees(
    orders: OrderDocument[],
    currency: currenciesConfig.MainCurrencyType
  ): Promise<FeesType> {
    return aggregateFees(
      orders.filter((order) => order.isMatched).map((order) => order.toObject().fees),
      currency
    );
  }

  private static async _calculateSettledAssetTransactionConsideration(
    transaction: AssetTransactionDocument,
    orders: OrderDocument[]
  ): Promise<TransactionConsiderationType> {
    const isAssetBuy =
      transaction.portfolioTransactionCategory === "update" &&
      orders.length === 1 &&
      orders.every((order) => order.side === "Buy");
    const isPortfolioBuy = transaction.portfolioTransactionCategory === "buy";

    return {
      ...transaction.consideration,
      amount:
        isAssetBuy || isPortfolioBuy
          ? OrderService.calculateSpentCash(orders).mul(100).toNumber()
          : OrderService.calculateReceivedCash(orders).mul(100).toNumber()
    };
  }

  /**
   * Enhances orders objects with userful properties for client-side display
   * @param user
   * @param orders Array of orders to enhance.
   * @param investmentProductsDict
   * @param investmentProducts Mapping of investment product IDs to documents for display value calculations.
   * @param latestFXRatesWithSpread
   * @param transaction Current transaction document for order evaluations.
   * @returns Enhanced array of OrderDocument objects.
   * @private
   */
  private static _fillOrdersClientDisplayFields(
    user: UserDocument,
    orders: OrderDocument[],
    investmentProductsDict: { [key: string]: InvestmentProductDocument },
    latestFXRatesWithSpread: PartialRecord<FXSpreadSide, ForeignCurrencyRatesType>,
    transaction: TransactionDocument,
    {
      displayAmount = true,
      displayQuantity = true,
      executionWindow = true,
      displayExchangeRate = false,
      isCancellable = false,
      estimatedRealTimeCommission = false
    }: FillClientDisplayFields = {}
  ): OrderDocument[] {
    return orders.map((order) => {
      return OrderService.fillOrderClientDisplayFields(
        user,
        order,
        // At this point we have kept only the orders that correspond to the asset's isin
        // so we can use the investment product that we queried in the beginning
        investmentProductsDict[order.isin],
        latestFXRatesWithSpread,
        transaction,
        {
          displayAmount,
          displayQuantity,
          executionWindow,
          displayExchangeRate,
          isCancellable,
          estimatedRealTimeCommission
        }
      );
    });
  }

  /**
   * Cancels any open orders within the transaction.
   *
   * @param transaction
   */
  private static async _cancelAssetTransaction(
    transaction: AssetTransactionDocument
  ): Promise<AssetTransactionDocument> {
    const [investmentProductsDict] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER),
      transaction.populate({
        path: "orders",
        populate: {
          path: "transaction"
        }
      })
    ]);

    const user = transaction.owner as UserDocument;

    return DbUtil.runInSession<AssetTransactionDocument>(async (session: mongoose.ClientSession) => {
      const cancelledOrders = await Promise.all(
        transaction.orders
          .filter((order) => order.getIsCancellable(user, investmentProductsDict[order.isin], transaction))
          .map((order) =>
            OrderService.setOrderStatusToCancelled(order, user, investmentProductsDict[order.isin], { session })
          )
      );

      const isBuyTransaction = [
        ExtendedAssetTransactionCategoryEnum.PORTFOLIO_BUY,
        ExtendedAssetTransactionCategoryEnum.ASSET_BUY
      ].includes(getExtendedPortfolioTransactionCategory(transaction));

      const totalCancelledBuyOrderAmount = cancelledOrders
        .filter((order) => order.side === "Buy")
        .map((order) => order.consideration.originalAmount)
        .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
        .toNumber();

      if (transaction.status === "Pending" && isBuyTransaction) {
        await PortfolioService.updateCashAvailability(
          transaction.portfolio.toString(),
          user.currency,
          new Decimal(totalCancelledBuyOrderAmount).div(100).toNumber(),
          { session, available: true, settled: true }
        );

        const otherPendingOrdersOfUser = await OrderService.getPendingOrders(user.id, { session });

        if (user.isConvertingPortfolio && otherPendingOrdersOfUser.length === 0) {
          await UserService.convertUser(user, "notStarted", { session });
        }
      }

      const updatedTransaction = await TransactionService.syncAssetTransaction(
        transaction,
        investmentProductsDict,
        {
          session
        }
      );

      await Promise.all([
        TransactionService.reduceCashbackAmountAfterCancelledOrder(
          totalCancelledBuyOrderAmount,
          updatedTransaction,
          {
            session
          }
        ),
        DbUtil.populateIfNotAlreadyPopulated(updatedTransaction, TransactionPopulationFieldsEnum.ORDERS, {
          session
        })
      ]);

      return updatedTransaction;
    });
  }

  private static async _cancelRebalanceTransaction(
    transaction: RebalanceTransactionDocument
  ): Promise<RebalanceTransactionDocument> {
    const [updatedTransaction] = await Promise.all([
      RebalanceTransaction.findByIdAndUpdate(transaction.id, {
        rebalanceStatus: "Cancelled" as RebalanceTransactionStatusType
      }),
      Order.updateMany({ transaction: transaction.id, status: "Pending" }, { status: "Cancelled" })
    ]);

    return updatedTransaction;
  }

  /**
   * Returns all the FX rates (with spread) for the given orders and plan.
   *
   * @param user
   * @param orders
   * @param investmentProductsByIsin
   * @param plan
   * @param spreadSide
   * @private
   */
  private static async _getOrdersForeignCurrencyRatesWithSpread(
    user: UserDocument,
    orders: Partial<OrderInterface>[],
    investmentProductsByIsin: { [isin: string]: InvestmentProductDocument },
    plan: plansConfig.PlanType,
    spreadSide: FXSpreadSide
  ): Promise<ForeignCurrencyRatesType | undefined> {
    const foreignCurrenciesInOrders = [
      ...new Set(orders.map((order) => investmentProductsByIsin[order.isin].tradedCurrency))
    ].filter((currency) => CurrencyUtil.getForeignCurrencies(user).includes(currency));

    if (foreignCurrenciesInOrders.length > 0) {
      return Object.fromEntries(
        await Promise.all(
          foreignCurrenciesInOrders.map(async (currency) => {
            return [
              currency,
              await calculateFXRateWithSpread(spreadSide, plan, {
                userCurrency: user.currency,
                targetCurrency: currency,
                rounding: true
              })
            ];
          })
        )
      );
    }
  }

  private static async _getCustodyAmountsByWkPortfolioId(
    startDate: Date,
    endDate: Date = new Date()
  ): Promise<Record<string, TransactionCharge[]>> {
    const custodyChargeTransactionsByPortfolioId = (await TransactionService._getTransactionsByPortfolioId({
      categories: ["ChargeTransaction"],
      chargeTypes: ["custody"],
      statuses: ["PendingWealthkernelCharge"],
      creationDate: { startDate, endDate },
      hasWealthkernelId: false
    } as ChargeTransactionsFilter)) as Record<string, ChargeTransactionDocument[]>;

    const amountPerWkPortfolioId = await Promise.all(
      Object.entries(custodyChargeTransactionsByPortfolioId).map(async ([portfolioId, transactions]) => {
        if (transactions.length > 1) {
          throw new Error(
            `💣 There is more than one custody charge transaction for owner of portfolio ${portfolioId} - this should not happen!`
          );
        }
        const portfolio = await PortfolioService.getPortfolio(portfolioId);

        await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
        const user = portfolio.owner as UserDocument;

        const transaction = transactions[0];

        // We do not add the charge in the report if the charge included selling of holdings and some orders are
        // still unsettled in WK.
        const transactionHasUnsettledOrders =
          ["holdings", "combined"].includes(transaction.chargeMethod) &&
          transaction.orders.some((order) => order.status !== "Settled");

        // In case of existing unsettled orders, we return a 0 amount charge, so that no document is created in WK.
        if (transactionHasUnsettledOrders) {
          return [
            portfolio.providers?.wealthkernel?.id,
            { amount: 0, currency: user.currency, companyEntity: user.companyEntity }
          ];
        }

        const amount = Decimal.div(transaction.consideration.amount, 100).toNumber();
        return [
          portfolio.providers?.wealthkernel?.id,
          { amount, transactionId: transaction.id, currency: user.currency, companyEntity: user.companyEntity }
        ];
      })
    );

    return Object.fromEntries(
      amountPerWkPortfolioId
        .filter(([, charge]) => (charge as TransactionCharge).amount > 0)
        .sort(
          ([, charge], [, anotherCharge]) =>
            (charge as TransactionCharge).amount - (anotherCharge as TransactionCharge).amount
        )
        .map(([portfolioId, charge]) => [portfolioId, [charge]])
    );
  }

  private static async _getCommissionAmountsByWkPortfolioId(
    startDate: Date,
    endDate: Date = new Date()
  ): Promise<Record<string, TransactionCharge[]>> {
    const commissionChargeTransactionsByPortfolioId = (await TransactionService._getTransactionsByPortfolioId({
      categories: ["ChargeTransaction"],
      chargeTypes: ["commission"],
      statuses: ["Pending"],
      creationDate: { startDate, endDate },
      hasWealthkernelId: false
    } as ChargeTransactionsFilter)) as Record<string, ChargeTransactionDocument[]>;

    const commissionCharges = await Promise.all(
      Object.entries(commissionChargeTransactionsByPortfolioId).map(async ([portfolioId, transactions]) => {
        const portfolio = await PortfolioService.getPortfolio(portfolioId);

        await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
        const user = portfolio.owner as UserDocument;

        return [
          portfolio.providers?.wealthkernel?.id,
          transactions
            .filter((transaction) => transaction.consideration.amount > 0)
            .filter((transaction) => transaction.orders.every((order) => order.status === "Settled"))
            .map((transaction) => ({
              amount: Decimal.div(transaction.consideration.amount, 100).toNumber(),
              transactionId: transaction.id,
              currency: user.currency,
              companyEntity: user.companyEntity
            }))
            .sort(
              (charge: TransactionCharge, anotherCharge: TransactionCharge) => charge.amount - anotherCharge.amount
            )
        ];
      })
    );

    return Object.fromEntries(commissionCharges);
  }

  private static async _getExecutionSpreadAmountsByWkPortfolioId(
    startDate: Date,
    endDate: Date = new Date()
  ): Promise<Record<string, TransactionCharge[]>> {
    const executionSpreadChargeTransactionsByPortfolioId = (await TransactionService._getTransactionsByPortfolioId(
      {
        categories: ["ChargeTransaction"],
        chargeTypes: ["executionSpread"],
        statuses: ["Pending"],
        creationDate: { startDate, endDate },
        hasWealthkernelId: false
      } as ChargeTransactionsFilter
    )) as Record<string, ChargeTransactionDocument[]>;

    const executionSpreadCharges = await Promise.all(
      Object.entries(executionSpreadChargeTransactionsByPortfolioId).map(async ([portfolioId, transactions]) => {
        const portfolio = await PortfolioService.getPortfolio(portfolioId);

        await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
        const user = portfolio.owner as UserDocument;

        return [
          portfolio.providers?.wealthkernel?.id,
          transactions
            .filter((transaction) => transaction.consideration.amount > 0)
            .filter((transaction) => transaction.orders.every((order) => order.status === "Settled"))
            .map((transaction) => ({
              amount: Decimal.div(transaction.consideration.amount, 100).toNumber(),
              transactionId: transaction.id,
              currency: user.currency,
              companyEntity: user.companyEntity
            }))
            .sort(
              (charge: TransactionCharge, anotherCharge: TransactionCharge) => charge.amount - anotherCharge.amount
            )
        ];
      })
    );

    return Object.fromEntries(executionSpreadCharges);
  }

  private static async _getRevertRewardAmountsByWkPortfolioId(
    startDate: Date,
    endDate: Date = new Date()
  ): Promise<Record<string, TransactionCharge[]>> {
    const revertRewardTransactionsByPortfolioId = (await TransactionService._getTransactionsByPortfolioId({
      categories: ["RevertRewardTransaction"],
      statuses: ["PendingWealthkernelCharge"],
      creationDate: { startDate, endDate },
      hasWealthkernelId: false
    })) as Record<string, RevertRewardTransactionDocument[]>;

    const amountPerWkPortfolioId = await Promise.all(
      Object.entries(revertRewardTransactionsByPortfolioId).map(async ([portfolioId, transactions]) => {
        const portfolio = await PortfolioService.getPortfolio(portfolioId);

        await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
        const user = portfolio.owner as UserDocument;

        const transaction = transactions[0];

        // If the revert reward transaction has unsettled orders, then we don't want to send it to WK yet.
        if (transaction.orders[0].status !== "Settled") {
          return [
            portfolio.providers?.wealthkernel?.id,
            { amount: 0, currency: user.currency, companyEntity: user.companyEntity }
          ];
        }

        const amount = Decimal.div(transaction.consideration.amount, 100).toNumber();
        return [
          portfolio.providers?.wealthkernel?.id,
          { amount, transactionId: transaction.id, currency: user.currency, companyEntity: user.companyEntity }
        ];
      })
    );

    return Object.fromEntries(
      amountPerWkPortfolioId
        .filter(([, charge]) => (charge as TransactionCharge).amount > 0)
        .sort(
          ([, charge], [, anotherCharge]) =>
            (charge as TransactionCharge).amount - (anotherCharge as TransactionCharge).amount
        )
        .map(([portfolioId, charge]) => [portfolioId, [charge]])
    );
  }

  private static async _getDividendFeeAmountsByWkPortfolioId(
    startDate: Date,
    endDate: Date = new Date()
  ): Promise<Record<string, TransactionCharge[]>> {
    const dividendChargeTransactionsByPortfolioId = (await TransactionService._getTransactionsByPortfolioId({
      categories: ["ChargeTransaction"],
      chargeTypes: ["dividendCommission"],
      statuses: ["Pending"],
      creationDate: { startDate, endDate },
      hasWealthkernelId: false
    } as ChargeTransactionsFilter)) as Record<string, ChargeTransactionDocument[]>;

    const dividendCharges = await Promise.all(
      Object.entries(dividendChargeTransactionsByPortfolioId).map(async ([portfolioId, transactions]) => {
        const portfolio = await PortfolioService.getPortfolio(portfolioId);

        await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
        const user = portfolio.owner as UserDocument;

        return [
          portfolio.providers?.wealthkernel?.id,
          transactions
            .filter((transaction) => transaction.consideration.amount > 0)
            .map((transaction) => ({
              amount: Decimal.div(transaction.consideration.amount, 100).toNumber(),
              transactionId: transaction.id,
              currency: user.currency,
              companyEntity: user.companyEntity
            }))
            .sort(
              (charge: TransactionCharge, anotherCharge: TransactionCharge) => charge.amount - anotherCharge.amount
            )
        ];
      })
    );

    return Object.fromEntries(dividendCharges);
  }

  private static async _getRealtimeExecutionFeeAmountsByWkPortfolioId(
    startDate: Date,
    endDate: Date = new Date()
  ): Promise<Record<string, TransactionCharge[]>> {
    const chargeTransactionsByPortfolioId = (await TransactionService._getTransactionsByPortfolioId({
      categories: ["ChargeTransaction"],
      chargeTypes: ["realtimeExecution"],
      statuses: ["Pending"],
      creationDate: { startDate, endDate },
      hasWealthkernelId: false
    } as ChargeTransactionsFilter)) as Record<string, ChargeTransactionDocument[]>;

    const charges = await Promise.all(
      Object.entries(chargeTransactionsByPortfolioId).map(async ([portfolioId, transactions]) => {
        const portfolio = await PortfolioService.getPortfolio(portfolioId);

        await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
        const user = portfolio.owner as UserDocument;

        return [
          portfolio.providers?.wealthkernel?.id,
          transactions
            .filter((transaction) => transaction.consideration.amount > 0)
            .filter((transaction) => transaction.orders.every((order) => order.status === "Settled"))
            .map((transaction) => ({
              amount: Decimal.div(transaction.consideration.amount, 100).toNumber(),
              transactionId: transaction.id,
              currency: user.currency,
              companyEntity: user.companyEntity
            }))
            .sort(
              (charge: TransactionCharge, anotherCharge: TransactionCharge) => charge.amount - anotherCharge.amount
            )
        ];
      })
    );

    return Object.fromEntries(charges);
  }

  private static async _getFxAmountsByWkPortfolioId(
    startDate: Date,
    endDate: Date = new Date()
  ): Promise<Record<string, TransactionCharge[]>> {
    const fxChargeTransactionsByPortfolioId = (await TransactionService._getTransactionsByPortfolioId({
      categories: ["ChargeTransaction"],
      chargeTypes: ["fx"],
      statuses: ["Pending"],
      creationDate: { startDate, endDate },
      hasWealthkernelId: false
    } as ChargeTransactionsFilter)) as Record<string, ChargeTransactionDocument[]>;

    const fxCharges = await Promise.all(
      Object.entries(fxChargeTransactionsByPortfolioId).map(async ([portfolioId, transactions]) => {
        const portfolio = await PortfolioService.getPortfolio(portfolioId);

        await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
        const user = portfolio.owner as UserDocument;

        return [
          portfolio.providers?.wealthkernel?.id,
          transactions
            .filter((transaction) => transaction.consideration.amount > 0)
            .filter((transaction) => transaction.orders.every((order) => order.status === "Settled"))
            .map((transaction) => ({
              amount: Decimal.div(transaction.consideration.amount, 100).toNumber(),
              transactionId: transaction.id,
              currency: user.currency,
              companyEntity: user.companyEntity
            }))
            .sort(
              (charge: TransactionCharge, anotherCharge: TransactionCharge) => charge.amount - anotherCharge.amount
            )
        ];
      })
    );

    return Object.fromEntries(fxCharges);
  }

  private static async _getTransactions(
    filter: TransactionsFilter = {},
    populate?: Record<string, boolean>,
    sort?: string
  ): Promise<TransactionDocument[]> {
    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }
    const filterToUse = TransactionService._createDbTransactionFilter(filter);
    let transactions: TransactionDocument[] = await Transaction.find(filterToUse, null, options);

    /**
     * If transaction array is empty, make a quick exit to avoid more async operations
     */
    if (transactions.length === 0) return transactions;

    await this._populateAllTransactions(populate, transactions);

    transactions = await this._filterPostQueryTransactions(transactions, filter);

    // When orders are populated, we also fill the transaction/order display fields.
    if (populate && populate["orders"]) {
      const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);

      transactions = await Promise.all(
        transactions.map(async (transaction) => {
          await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER);

          const user = transaction.owner as UserDocument;

          return TransactionService.fillClientDisplayFields(user, transaction, investmentProducts);
        })
      );
    }

    return transactions;
  }

  private static async _getTransactionsByPortfolioId(
    filter: TransactionsFilter = {}
  ): Promise<Record<string, TransactionDocument[]>> {
    const filterToUse = TransactionService._createDbTransactionFilter(filter);

    let transactions: TransactionDocument[] = await Transaction.find({ ...filterToUse }).populate("orders");

    // For some transaction types, status is a virtual field, and hence we do this extra filtering.
    if (filter.statuses) {
      transactions = transactions.filter(({ status }) => filter.statuses.includes(status));
    }

    const transactionsByPortfolioId: { [portfolioId in string]: TransactionDocument[] } = {};
    transactions.forEach((transaction) => {
      const existingTransactionsForPortfolio = transactionsByPortfolioId[transaction.portfolio.toString()];
      if (!existingTransactionsForPortfolio) {
        transactionsByPortfolioId[transaction.portfolio.toString()] = [transaction];
      } else {
        existingTransactionsForPortfolio.push(transaction);
      }
    });

    return transactionsByPortfolioId;
  }

  private static _createDbTransactionFilter(filter: TransactionsFilter) {
    const actualFilter: {
      category?: { $in: TransactionCategoryType[] };
      portfolioTransactionCategory?: { $in: PortfolioTransactionCategoryType[] };
      chargeMethod?: { $in: ChargeMethodType[] };
      chargeType?: { $in: ChargeTypeType[] };
      portfolio?: mongoose.Types.ObjectId;
      owner?: mongoose.Types.ObjectId;
      settledAt?: { $gte: Date; $lt: Date };
      createdAt?: { $gte: Date; $lt: Date };
      "providers.wealthkernel.submittedAt"?: { $gte: Date; $lt: Date };
      "providers.wealthkernel.id"?: { $exists: boolean };
      "directDebit.providers.wealthkernel.id"?: { $exists: boolean };
      "directDebit.providers.gocardless.id"?: { $exists: boolean };
      "providers.truelayer.status"?: { $in: (PaymentStatusTypeV1 | PaymentStatusTypeV3 | null)[] };
      "providers.truelayer.id": string;
      "providers.saltedge.customId": string;
      linkedUserDataRequest?: mongoose.Types.ObjectId;
      linkedAutomation?: mongoose.Types.ObjectId | { $exists: boolean };
      linkedCreditTicket?: mongoose.Types.ObjectId | { $exists: boolean };
      pendingDeposit?: mongoose.Types.ObjectId;
      chargeMonth?: string;
      dividendMonth?: string;
      subscription?: mongoose.Types.ObjectId;
      price?: string;
      hasViewedAppModal?: boolean;
      asset?: investmentUniverseConfig.AssetType;
      depositMethod?: DepositMethodEnum;
    } = {
      category: null,
      portfolioTransactionCategory: null,
      chargeMethod: null,
      chargeType: null,
      portfolio: null,
      owner: null,
      settledAt: null,
      createdAt: null,
      "providers.wealthkernel.submittedAt": null,
      "providers.wealthkernel.id": null,
      "directDebit.providers.wealthkernel.id": null,
      "directDebit.providers.gocardless.id": null,
      "providers.truelayer.status": null,
      "providers.truelayer.id": null,
      "providers.saltedge.customId": null,
      linkedUserDataRequest: null,
      linkedAutomation: null,
      linkedCreditTicket: null,
      pendingDeposit: null,
      chargeMonth: null,
      dividendMonth: null,
      subscription: null,
      price: null,
      hasViewedAppModal: null,
      asset: null,
      depositMethod: null
    };

    Object.keys(actualFilter).forEach((key) => {
      if ((filter as any)[key] != null) {
        (actualFilter as any)[key] = (filter as any)[key];
      }
    });

    if (filter.categories) {
      actualFilter["category"] = {
        $in: [...filter.categories] as TransactionCategoryType[]
      };
    }

    if ((filter as AssetTransactionsFilter).portfolioTransactionCategories) {
      actualFilter["portfolioTransactionCategory"] = {
        $in: [
          ...(filter as AssetTransactionsFilter).portfolioTransactionCategories
        ] as PortfolioTransactionCategoryType[]
      };
    }

    if ((filter as ChargeTransactionsFilter).chargeMethods) {
      actualFilter["chargeMethod"] = {
        $in: [...(filter as ChargeTransactionsFilter).chargeMethods] as ChargeMethodType[]
      };
    }

    if (
      filter.hasLinkedAutomation !== undefined &&
      filter.hasLinkedAutomation !== null &&
      !filter.linkedAutomation
    ) {
      actualFilter["linkedAutomation"] = { $exists: filter.hasLinkedAutomation };
    }

    if (filter.hasCreditTicket !== undefined && filter.hasCreditTicket !== null && !filter.hasCreditTicket) {
      actualFilter["linkedCreditTicket"] = { $exists: filter.hasCreditTicket };
    }

    if ((filter as ChargeTransactionsFilter).chargeTypes) {
      actualFilter["chargeType"] = {
        $in: [...(filter as ChargeTransactionsFilter).chargeTypes] as ChargeTypeType[]
      };
    }

    if (filter.settlementDate) {
      actualFilter["settledAt"] = {
        $gte: filter.settlementDate.startDate ?? new Date("1970-01-01T00:00:00Z"),
        $lt: filter.settlementDate.endDate ?? new Date(Date.now())
      };
    }

    if (filter.creationDate) {
      actualFilter["createdAt"] = {
        $gte: filter.creationDate.startDate ?? new Date("1970-01-01T00:00:00Z"),
        $lt: filter.creationDate.endDate ?? new Date(Date.now())
      };
    }

    if (filter.wealthkernelSubmissionDate) {
      actualFilter["providers.wealthkernel.submittedAt"] = {
        $gte: filter.wealthkernelSubmissionDate.startDate ?? new Date("1970-01-01T00:00:00Z"),
        $lt: filter.wealthkernelSubmissionDate.endDate ?? new Date(Date.now())
      };
    }

    if (filter.truelayerStatuses) {
      actualFilter["providers.truelayer.status"] = {
        // we also include null value to also fetch transactions without truelayer status
        $in: [...filter.truelayerStatuses, null] as (PaymentStatusTypeV1 | PaymentStatusTypeV3)[]
      };
    }

    if (filter.hasWealthkernelId === true) {
      actualFilter["providers.wealthkernel.id"] = {
        $exists: true
      };
    } else if (filter.hasWealthkernelId === false) {
      actualFilter["providers.wealthkernel.id"] = {
        $exists: false
      };
    }

    if (filter.hasDirectDebitWealthkernelId === true) {
      actualFilter["directDebit.providers.wealthkernel.id"] = {
        $exists: true
      };
    } else if (filter.hasDirectDebitWealthkernelId === false) {
      actualFilter["directDebit.providers.wealthkernel.id"] = {
        $exists: false
      };
    }
    if (filter.hasDirectDebitGoCardlessId === true) {
      actualFilter["directDebit.providers.gocardless.id"] = {
        $exists: true
      };
    } else if (filter.hasDirectDebitGoCardlessId === false) {
      actualFilter["directDebit.providers.gocardless.id"] = {
        $exists: false
      };
    }

    if (filter.hasViewedAppModal === true) {
      actualFilter["hasViewedAppModal"] = true;
    } else if (filter.hasViewedAppModal === false) {
      actualFilter["hasViewedAppModal"] = false;
    }

    if ((filter as DepositTransactionsFilter).truelayerPaymentId) {
      actualFilter["providers.truelayer.id"] = (filter as DepositTransactionsFilter).truelayerPaymentId;
    }

    if ((filter as DepositTransactionsFilter).saltedgeCustomPaymentId) {
      actualFilter["providers.saltedge.customId"] = (filter as DepositTransactionsFilter).saltedgeCustomPaymentId;
    }

    if (filter.depositMethod) {
      actualFilter["depositMethod"] = filter.depositMethod;
    }

    return Object.fromEntries(Object.entries(actualFilter).filter(([, value]) => value != null));
  }

  private static async _createInvestmentDividend(wkDividend: TransactionType): Promise<void> {
    logger.info(`Creating dividend for ${wkDividend.id}`, {
      module: "TransactionService",
      method: "_createInvestmentDividend"
    });

    try {
      // 1. Check if document exists already for this dividend
      const dividendExists = await DividendTransaction.exists({ "providers.wealthkernel.id": wkDividend.id });

      // 2. If dividend transaction id exists abort
      if (dividendExists) {
        logger.warn(`Dividend ${wkDividend.id} already has a db document - aborting`, {
          module: "TransactionService",
          method: "_createInvestmentDividend"
        });
        return;
      }

      // 3. If dividend transaction id does not exist create dividend document
      const portfolio = await Portfolio.findOne({
        "providers.wealthkernel.id": wkDividend.portfolioId
      }).populate("owner");
      const user = portfolio.owner as UserDocument;
      const assetId = InvestmentUniverseUtil.getAssetIdFromIsin(wkDividend.isin);

      const isSettled = wkDividend.status === "Settled" || wkDividend.status === "Matched";

      const dividendData: DividendTransactionDTOInterface = {
        consideration: {
          currency: wkDividend.consideration.currency,
          amount: Decimal.mul(wkDividend.consideration.amount, 100).toNumber() // stored in cents
        },
        owner: user.id,
        portfolio: portfolio.id,
        asset: assetId,
        isin: ASSET_CONFIG[assetId].isin,
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: wkDividend.id,
            status: wkDividend.status as WkTransactionStatusType
          }
        },
        createdAt: new Date(Date.now()),
        ...(isSettled && {
          settledAt: new Date(Date.now())
        })
      };

      const dividend = await new DividendTransaction(dividendData).save();

      logger.info(`DB document for dividend ${wkDividend.id} was created successfully!`, {
        module: "TransactionService",
        method: "_createInvestmentDividend",
        data: { dividend, wkDividend }
      });

      // 4. if dividend status is Matched or Settled then update portfolio cash
      if (isSettled) {
        await PortfolioService.updateCashAvailability(
          portfolio.id,
          user.currency,
          wkDividend.consideration.amount,
          { available: true, settled: true }
        );
        logger.info(
          `Cash for portfolio ${portfolio.id} was increased by ${wkDividend.consideration.amount} due to dividend ${wkDividend.id}`,
          {
            module: "TransactionService",
            method: "_createInvestmentDividend"
          }
        );
        const properties: TrackNotificationDividendSuccess = {
          amount: CurrencyUtil.formatCurrency(
            wkDividend.consideration.amount,
            user.currency,
            ConfigUtil.getDefaultUserLocale(user.residencyCountry)
          ),
          etf_name: ASSET_CONFIG[assetId].simpleName
        };
        eventEmitter.emit(events.transaction.dividendSuccess.eventId, portfolio.owner, properties);

        // We want to clear the cache to avoid displaying the change in holdings as increase in returns.
        // MWRR & value will be cached when portfolio returns are requested again through dashboard (or at cron).
        await Promise.all([
          RedisClientService.Instance.del(`portfolios:mwrr:${portfolio.id}`),
          RedisClientService.Instance.del(`portfolios:value_at_mwrr:${portfolio.id}`),
          RedisClientService.Instance.del(`portfolios:up_by:${portfolio.id}`),
          RedisClientService.Instance.del(`portfolios:value_at_up_by:${portfolio.id}`)
        ]);
      }
    } catch (err) {
      captureException(err);
      logger.error(`Dividend creation for dividend ${wkDividend.id} failed`, {
        module: "TransactionService",
        method: "_createInvestmentDividend",
        data: { wkDividend, error: err }
      });
    }
  }

  private static async _createInvestmentAssetDividend(wkAssetDividend: TransactionType): Promise<void> {
    logger.info(`Creating asset dividend for ${wkAssetDividend.id}`, {
      module: "TransactionService",
      method: "_createInvestmentAssetDividend"
    });

    try {
      // 1. Check if document exists already for this dividend
      const assetDividendExists = await AssetDividendTransaction.exists({
        "providers.wealthkernel.id": wkAssetDividend.id
      });

      // 2. If dividend transaction id exists abort
      if (assetDividendExists) {
        logger.warn(`Asset dividend ${wkAssetDividend.id} already has a db document - aborting`, {
          module: "TransactionService",
          method: "_createInvestmentAssetDividend"
        });
        return;
      }

      // 3. If asset dividend transaction id does not exist create asset dividend document
      const portfolio = await PortfolioService.getPortfolioByWealthkernelId(wkAssetDividend.portfolioId, {
        owner: true
      });

      const user = portfolio.owner as UserDocument;
      const assetId = InvestmentUniverseUtil.getAssetIdFromIsin(wkAssetDividend.isin);

      const isSettled = wkAssetDividend.status === "Settled" || wkAssetDividend.status === "Matched";

      const assetDividendData: AssetDividendTransactionDTOInterface = {
        quantity: wkAssetDividend.quantity,
        owner: user.id,
        portfolio: portfolio.id,
        asset: assetId,
        isin: ASSET_CONFIG[assetId].isin,
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: wkAssetDividend.id,
            status: wkAssetDividend.status as WkTransactionStatusType
          }
        },
        createdAt: new Date(Date.now()),
        ...(isSettled && {
          settledAt: new Date(Date.now())
        })
      };

      const dividend = await new AssetDividendTransaction(assetDividendData).save();

      logger.info(`DB document for asset dividend ${wkAssetDividend.id} was created successfully!`, {
        module: "TransactionService",
        method: "_createInvestmentAssetDividend",
        data: { dividend, wkAssetDividend }
      });

      // 4. if dividend status is Matched or Settled then update portfolio holdings
      if (isSettled) {
        await PortfolioService.updatePortfolioHoldings(portfolio.id, [
          {
            side: "Buy",
            quantity: wkAssetDividend.quantity,
            isin: wkAssetDividend.isin
          }
        ]);

        const properties: TrackAssetDividendSuccessPropertiesType = {
          quantity: wkAssetDividend.quantity,
          asset: assetId
        };
        eventEmitter.emit(events.transaction.assetDividendSuccess.eventId, portfolio.owner, properties);
      }
    } catch (err) {
      captureException(err);
      logger.error(`Asset dividend creation for dividend ${wkAssetDividend.id} failed`, {
        module: "TransactionService",
        method: "_createInvestmentAssetDividend",
        data: { wkAssetDividend, error: err }
      });
    }
  }

  private static async _getTransactionsPaginated(
    filter: TransactionsFilter = {},
    pageConfig: { page: number; pageSize: number },
    populate?: Record<string, boolean>,
    sort?: string
  ): Promise<PaginatedTransactionsResponse<TransactionDocument>> {
    const filterToUse = TransactionService._createDbTransactionFilter(filter);

    const count = await Transaction.countDocuments(filterToUse);
    const pageConfigToUse = PaginationUtil.getPaginationParametersFor(count, pageConfig);
    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    let transactions: TransactionDocument[] = await Transaction.find(filterToUse, null, options)
      .skip((pageConfigToUse.page - 1) * pageConfigToUse.pageSize)
      .limit(pageConfigToUse.pageSize);

    await this._populateAllTransactions(populate, transactions);

    transactions = await this._filterPostQueryTransactions(transactions, filter);
    pageConfigToUse.pageSize = transactions.length;

    // When orders are populated, we also fill the transaction/order display fields.
    if (populate && populate["orders"]) {
      const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);
      transactions = await Promise.all(
        transactions.map(async (transaction) => {
          await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER);

          const user = transaction.owner as UserDocument;

          return TransactionService.fillClientDisplayFields(user, transaction, investmentProducts);
        })
      );
    }

    return { pagination: pageConfigToUse, transactions };
  }

  /**
   * @description This method is used for bulk operations triggered usually from cron jobs.
   * It adds error handling in order to just report the error but not stop the execution of bulk operation.
   * @param deposit (DepoistCashTransaction)
   * @private
   */
  private static async _syncDepositTruelayerStatusSafely(deposit: DepositCashTransactionDocument): Promise<void> {
    try {
      if (!deposit.populated("owner")) {
        await deposit.populate("owner");
      }
      await TransactionService.syncDepositTruelayerStatus(deposit.providers.truelayer.id);
    } catch (err) {
      captureException(err);
      logger.error(`Transaction truelayer status syncing failed for deposit ${deposit._id}`, {
        module: "TransactionService",
        method: "_syncDepositTruelayerStatusSafely",
        data: { transactionId: deposit._id, userId: (deposit.owner as UserDocument)._id, error: err }
      });
    }
  }

  private static async _syncChargeTruelayerStatusSafely(charge: ChargeTransactionDocument): Promise<void> {
    try {
      if (!charge.populated("owner")) {
        await charge.populate("owner");
      }
      await TransactionService.syncLifetimeChargeTruelayerStatus(charge.providers.truelayer.id);
    } catch (err) {
      captureException(err);
      logger.error(`Transaction truelayer status syncing failed for charge ${charge._id}`, {
        module: "TransactionService",
        method: "_syncChargeTruelayerStatusSafely",
        data: { transactionId: charge._id, userId: (charge.owner as UserDocument)._id, error: err }
      });
    }
  }

  private static async _processPendingRebalanceTransaction(
    rebalanceTransaction: RebalanceTransactionDocument
  ): Promise<void> {
    try {
      logger.info(
        `Processing rebalance transaction ${rebalanceTransaction.id}, has status ${rebalanceTransaction.rebalanceStatus}`,
        { module: "TransactionService", method: "_processPendingRebalanceTransaction" }
      );

      await rebalanceTransaction.populate([
        { path: "owner" },
        {
          path: "portfolio",
          populate: [
            { path: "currentTicker" },
            {
              path: "holdings.asset",
              populate: {
                path: "currentTicker"
              }
            }
          ]
        }
      ]);

      switch (rebalanceTransaction.rebalanceStatus) {
        case "NotStarted":
          return await TransactionService._processNotStartedRebalanceTransaction(rebalanceTransaction);
        case "PendingSell":
          return await TransactionService._processPendingSellRebalanceTransaction(rebalanceTransaction);
        case "PendingBuy":
          return await TransactionService._processPendingBuyRebalanceTransaction(rebalanceTransaction);
        default:
          return;
      }
    } catch (err) {
      captureException(err);
      logger.error(`Rebalance transaction processing failed for transaction ${rebalanceTransaction._id}`, {
        module: "TransactionService",
        method: "_processPendingRebalanceTransaction",
        data: {
          transactionId: rebalanceTransaction._id,
          userId: (rebalanceTransaction.owner as UserDocument)._id,
          error: err
        }
      });
    }
  }

  private static async _createWkDepositExpectation(deposit: DepositCashTransactionDocument): Promise<void> {
    if (deposit?.providers?.wealthkernel?.id) {
      throw new BadRequestError(
        `Cannot create expectation for deposit ${deposit._id} as it already has a WK deposit ID`
      );
    }

    // 1. Retrieve wealthkernel portfolio id for the users' default portfolio
    await deposit.populate([{ path: "portfolio" }, { path: "bankAccount" }, { path: "owner" }]);

    const portfolio = deposit.portfolio as PortfolioDocument;
    const user = deposit.owner as UserDocument;
    const wealthkernelPortfolioId = portfolio.providers?.wealthkernel?.id;

    if (!wealthkernelPortfolioId) {
      throw new BadRequestError(
        `Cannot create expectation for deposit ${deposit._id}. Wealthkernel portfolio does not exist`
      );
    }

    const wkDepositResponse = await ProviderService.getBrokerageService(user.companyEntity).createDeposit(deposit);

    if (!wkDepositResponse) {
      return;
    }

    const wkDepositId = wkDepositResponse.id;

    // 3. Update transaction with wealthkernel info
    const updatedDeposit = await DepositCashTransaction.findByIdAndUpdate(
      deposit.id,
      {
        "providers.wealthkernel": { id: wkDepositId, status: "Created" }
      },
      { new: true }
    );

    if (deposit.depositMethod === DepositMethodEnum.OPEN_BANKING) {
      await TransactionService.emitDepositCreatedEvent(updatedDeposit, { triggeredByAutomation: false });
    }
  }

  private static async _syncWkDepositDirectDebitPayment(deposit: DepositCashTransactionDocument): Promise<void> {
    const wkDepositId = deposit?.directDebit?.providers?.wealthkernel?.id;
    if (!wkDepositId) {
      throw new BadRequestError(
        `Deposit transaction ${deposit.id} is missing wealthkernel direct debit payment id`
      );
    }

    // Retrieve deposit & status from wealthkernel
    const { status, amount } = await ProviderService.getRepeatingDepositPaymentService(
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
    ).getDirectDebitDeposit(wkDepositId);
    const wkDepositAmount = amount.amount;

    logger.info(`Syncing deposit ${deposit.id} - status: ${status}`, {
      module: "TransactionService",
      method: "_syncWkDepositDirectDebitPayment"
    });

    const settledAt = status === "Completed" ? new Date(Date.now()) : undefined;
    let depositStatus: TransactionStatusType;
    if (["Pending", "Collecting", "Collected"].includes(status)) {
      depositStatus = "Pending";
    } else if (status === "Cancelled") {
      depositStatus = "Cancelled";
    } else if (status === "Failed") {
      depositStatus = "Rejected";
    } else if (status === "Completed") {
      depositStatus = "Settled";
    }

    await DepositCashTransaction.findByIdAndUpdate(deposit._id, {
      "directDebit.providers.wealthkernel.status": status,
      settledAt,
      status: depositStatus
    });

    if (status === "Completed") {
      await deposit.populate([
        { path: "owner", populate: { path: "participant" } },
        { path: "portfolio" },
        { path: "linkedAssetTransaction", populate: { path: "orders" } },
        { path: "linkedSavingsTopup" }
      ]);
      const user = deposit.owner as UserDocument;
      const linkedAssetTransaction = deposit.linkedAssetTransaction as AssetTransactionDocument;
      const linkedSavingsTopup = deposit.linkedSavingsTopup as SavingsTopupTransactionDocument;

      if (linkedAssetTransaction) {
        const orders = linkedAssetTransaction.orders as OrderDocument[];

        await TransactionService._returnCashForCancelledOrders(orders, linkedAssetTransaction);
      }

      const eventData = {
        amount: wkDepositAmount,
        currency: deposit.consideration.currency,
        noAssetTransactionPending:
          linkedAssetTransaction?.status !== "PendingDeposit" && linkedSavingsTopup?.status !== "PendingDeposit"
      };
      logger.info("eventEmitter.emit - depositAvailable", {
        module: "TransactionService",
        method: "_syncWkDepositDirectDebitPayment",
        data: { ...eventData, depositId: deposit.id }
      });
      eventEmitter.emit(events.transaction.depositAvailable.eventId, user, eventData);

      logger.info("eventEmitter.emit - depositSuccess", {
        module: "TransactionService",
        method: "_syncWkDepositDirectDebitPayment",
        data: { ...eventData, depositId: deposit.id }
      });
      eventEmitter.emit(events.transaction.depositSuccess.eventId, user, eventData);
    } else if (status === "Failed") {
      await deposit.populate({ path: "owner", populate: { path: "participant" } });
      const user = deposit.owner as UserDocument;

      logger.info("eventEmitter.emit - depositFailure", {
        module: "TransactionService",
        method: "_syncWkDepositDirectDebitPayment"
      });
      eventEmitter.emit(events.transaction.depositFailure.eventId, user);
    } else if (status === "Cancelled") {
      const linkedTransactions = (await TransactionService.getAssetTransactions({
        pendingDeposit: deposit._id,
        statuses: ["PendingDeposit"]
      })) as AssetTransactionDocument[];

      if (linkedTransactions.length === 1) {
        await TransactionService._markPendingDepositAssetTransactionAsDepositFailed(linkedTransactions[0]);
      }
    }
  }

  /**
   * @description This method is used for bulk operations triggered usually from cron jobs.
   * It adds error handling in order to just report the error but not stop the execution of bulk operation.
   * @param deposit (DepositCashTransaction)
   */
  private static async _createWkDepositExpectationSafely(deposit: DepositCashTransactionDocument): Promise<void> {
    try {
      await TransactionService._createWkDepositExpectation(deposit);
    } catch (err) {
      captureException(err);
      logger.error(`Wealthkernel expectation creation failed for deposit ${deposit._id}`, {
        module: "TransactionService",
        method: "_createWkDepositExpectationSafely",
        data: { transactionId: deposit._id, userId: (deposit.owner as UserDocument)._id, error: err }
      });
    }
  }

  private static async _syncWkDepositDirectDebitPaymentSafely(
    deposit: DepositCashTransactionDocument
  ): Promise<void> {
    try {
      await TransactionService._syncWkDepositDirectDebitPayment(deposit);
    } catch (err) {
      captureException(err);
      logger.error(`Wealthkernel direct debit payment syncing failed for deposit ${deposit._id}`, {
        module: "TransactionService",
        method: "_syncWkDepositDirectDebitPaymentSafely",
        data: { transactionId: deposit._id, userId: (deposit.owner as UserDocument)._id, error: err }
      });
    }
  }
  // PRIVATE METHODS
  /**
   * Method that processes a NotStarted rebalance transaction in our DB, by:
   *   1. Only proceeding if there is no pending asset transaction with sell orders/charge/rebalance transaction for that user.
   *   2. Determining what sell orders to create in our DB.
   *   3. Creating those sell orders and submitting them to WK.
   *   4. Setting the transaction status to PendingSell.
   */
  private static async _processNotStartedRebalanceTransaction(rebalanceTransaction: RebalanceTransactionDocument) {
    const owner = rebalanceTransaction.owner as UserDocument;

    // If the user has other pending transactions with sell orders or rebalance transactions, we don't process the rebalance transaction.
    const pendingTransactionsForOwner: TransactionDocument[] = await Transaction.find({
      category: { $in: ["AssetTransaction", "ChargeTransaction"] },
      owner: owner.id,
      status: "Pending"
    }).populate("orders");
    const pendingRebalanceTransactionsForOwner: RebalanceTransactionDocument[] = await RebalanceTransaction.find({
      owner: owner.id,
      rebalanceStatus: { $in: ["PendingSell", "PendingBuy"] }
    });

    const pendingAssetTransactions = pendingTransactionsForOwner
      .filter((transaction) => transaction.category === "AssetTransaction")
      .filter((transaction) => {
        const assetTransaction = transaction as AssetTransactionDocument;
        return assetTransaction.orders.some((order) => order.side === "Sell");
      });
    const chargeTransactionsWithUnmatchedOrders = pendingTransactionsForOwner
      .filter((transaction) => transaction.category === "ChargeTransaction")
      .filter((transaction) => {
        const chargeTransaction = transaction as ChargeTransactionDocument;
        return (
          ["holdings", "combined"].includes(chargeTransaction.chargeMethod) &&
          !chargeTransaction.orders.every((order) => order.providers?.wealthkernel?.status === "Matched")
        );
      });
    if (
      pendingAssetTransactions.length > 0 ||
      chargeTransactionsWithUnmatchedOrders.length > 0 ||
      pendingRebalanceTransactionsForOwner.length > 0
    ) {
      logger.info(
        `Rebalance transaction ${rebalanceTransaction.id} is not going to get processed yet because owner has other pending transactions`,
        {
          module: "TransactionService",
          method: "_processNotStartedRebalanceTransaction",
          data: {
            numberOfPendingAssetTransactions: pendingAssetTransactions.length,
            numberOfPendingChargeTransactions: chargeTransactionsWithUnmatchedOrders.length,
            numberOfRunningRebalanceTransactions: pendingRebalanceTransactionsForOwner.length
          }
        }
      );
      return;
    }

    const portfolio: PortfolioDocument = rebalanceTransaction.portfolio as PortfolioDocument;
    const existingOrders: OrderDocument[] = await Order.find({ transaction: rebalanceTransaction.id });

    /**
     * Determine which sell orders to create in our DB by:
     *   1. Filtering out orders that have already been created (in previous cron runs)
     *   2. Figuring out the target quantity and rewarded quantity per asset
     *   3. From that, figuring out the quantity we want to sell per asset
     *   4. Making sure the quantity is over our investment quantity limits.
     */
    const holdingsToCreateOrdersFor = portfolio.holdings.filter((holding: HoldingsType) => {
      const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(holding.assetCommonId));
      return !existingOrders.find((order) => allAssetIsinsSet.has(order.isin));
    });

    const sellOrdersData = await OrderService.getRebalanceSellOrdersToCreate(
      portfolio,
      rebalanceTransaction.targetAllocation,
      rebalanceTransaction._id,
      holdingsToCreateOrdersFor
    );

    const sellOrderPromises = sellOrdersData.map((order) => OrderService.createDbOrder(order));

    await Promise.all(sellOrderPromises);

    logger.info(
      `Finished processing rebalance transaction ${rebalanceTransaction._id}, updating it to status Pending Sell`,
      {
        module: "TransactionService",
        method: "_processNotStartedRebalanceTransaction"
      }
    );

    const updatedRebalanceTransaction = await TransactionService._setRebalanceTransactionStatus(
      rebalanceTransaction.id,
      "PendingSell"
    );
    /**
     * Edge case: check if target allocation is changed from a concurrent update
     * that could happen while this method was running
     */
    const isTargetAllocationChanged = updatedRebalanceTransaction.targetAllocation.every((newAsset) => {
      const oldAsset = rebalanceTransaction.targetAllocation.find(
        (holding) => holding.assetCommonId === newAsset.assetCommonId
      );
      // If asset is not found, or the percentage has changed, then targetAllocation has changed
      return !oldAsset || oldAsset.percentage !== newAsset.percentage;
    });
    if (isTargetAllocationChanged) {
      logger.warn(
        `Target allocation was changed while processing rebalance transaction ${rebalanceTransaction._id}`,
        {
          module: "TransactionService",
          method: "_processNotStartedRebalanceTransaction"
        }
      );
    }
  }

  /**
   * Method that processes a PendingSell rebalance transaction in our DB, by:
   *   1. Retrieving all sell orders that were created for that rebalance transaction.
   *   2. Syncing ones that are still Pending with WK.
   *   3. If all are now Matched, then create buy orders and submit those to WK.
   *   4. Update the status to PendingBuy.
   */
  private static async _processPendingSellRebalanceTransaction(
    rebalanceTransaction: RebalanceTransactionDocument
  ): Promise<void> {
    const portfolio = rebalanceTransaction.portfolio as PortfolioDocument;
    const [investmentProducts, owner] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      UserService.getUser(portfolio.owner.toString(), {
        addresses: false,
        portfolios: false,
        subscription: true
      })
    ]);

    const existingOrders: OrderDocument[] = await Order.find({
      transaction: rebalanceTransaction._id
    });
    const existingBuyOrders: OrderDocument[] = existingOrders.filter((order) => order.side === "Buy");
    const existingSellOrders: OrderDocument[] = existingOrders.filter((order) => order.side === "Sell");

    // Since there are no sell orders, there won't be any buy orders either, so, we can settle the transaction early!
    if (existingSellOrders.length === 0) {
      logger.info(
        `There are no orders for rebalance transaction ${rebalanceTransaction._id}, moving it immediately to settled status!`,
        {
          module: "TransactionService",
          method: "_processPendingSellRebalanceTransaction"
        }
      );

      await RebalanceTransaction.findByIdAndUpdate(rebalanceTransaction.id, {
        fees: getZeroFees(owner.currency),
        settledAt: new Date(Date.now()),
        rebalanceStatus: "Settled"
      });

      return;
    }

    const matchedOrders = existingSellOrders.filter(
      (order) => order?.providers?.wealthkernel?.status === "Matched"
    );
    const rejectedOrders = existingSellOrders.filter(
      (order) => order?.providers?.wealthkernel?.status === "Rejected"
    );

    if (matchedOrders.length === existingSellOrders.length) {
      // All sell orders are matched, so we proceed to creating the buy orders.=
      const currentHoldings = PortfolioUtil.getTotalAmountPerHolding(owner.currency, portfolio);
      const targetHoldings = PortfolioUtil.getTargetAllocationInCurrency(
        owner.currency,
        portfolio,
        rebalanceTransaction.targetAllocation
      );

      const assetsInTargetAllocationNotHeld = Object.entries(targetHoldings)
        .filter(([assetId]) => currentHoldings.every((holding) => holding.assetCommonId !== assetId))
        .map(([assetId]) => {
          return {
            assetCommonId: assetId,
            amount: 0,
            asset: investmentProducts[ASSET_CONFIG[assetId as investmentUniverseConfig.AssetType].isin]
          };
        }) as {
        amount: number;
        assetCommonId: investmentUniverseConfig.AssetType;
        asset: InvestmentProductDocument;
      }[];

      // Any assets we want to buy have a positive result when doing the calculation
      // targetHolding[assetToBuy] - currentHolding[assetToBuy]. Before doing that, we make sure
      // we haven't also created sell orders for any of those assets (it could happen if current holding
      // amount is close to target holding amount and price fluctuations happen).
      const assetsToBuy = currentHoldings
        .filter((holding) => {
          const allAssetIsinsSet = new Set(
            InvestmentUniverseUtil.getIsinActiveAndDeprecated(holding.assetCommonId)
          );
          return !existingSellOrders.some((sellOrder) => allAssetIsinsSet.has(sellOrder.isin));
        })
        .filter((holding) => Decimal.sub(targetHoldings[holding.assetCommonId] ?? 0, holding.amount).isPositive())
        .concat(assetsInTargetAllocationNotHeld);

      /**
       * Add all sell order consideration amounts and divide by 100 (consideration
       * amounts are stored in cents) to find how much cash we have available for the buy orders.
       */
      const totalReceivedCash = existingSellOrders
        .map((sellOrder) => sellOrder.consideration.amount)
        .reduce((total, current) => total.add(current), new Decimal(0))
        .div(100);

      /**
       * Determine which buy orders to create in our DB by:
       *   1. Filtering out orders that have already been created (in previous cron runs)
       *   2. Figuring out the amount we want to buy, by doing the following calculation:
       *   Amount to buy: (totalValueOfAssetsToBuy + receivedCash) * (Asset target % / percentageOfAssetsToBuy) - Asset latest value
       *   3. After we create each buy order, we update our remaining cash. We make sure to not buy more than that at all times.
       */
      const rebalanceBuyOrdersData = await OrderService.getRebalanceBuyOrdersToCreate(
        owner.currency,
        assetsToBuy,
        rebalanceTransaction.targetAllocation,
        totalReceivedCash.toNumber(),
        owner.companyEntity,
        rebalanceTransaction._id
      );

      const plan = (owner.subscription as SubscriptionDocument).plan;

      const buyOrdersAfterFees = OrderService.applyFeesToOrders(
        plan,
        rebalanceBuyOrdersData,
        owner.currency,
        investmentProducts
      );

      await Promise.all(
        buyOrdersAfterFees
          .filter((orderData) => {
            const assetId = InvestmentUniverseUtil.getAssetIdFromIsin(orderData.isin);
            const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(assetId));
            return !existingBuyOrders.find((existingOrder) => allAssetIsinsSet.has(existingOrder.isin));
          })
          .map((orderData) => OrderService.createDbOrder(orderData as OrderDTOInterface))
      );

      logger.info(
        `Finished processing rebalance transaction ${rebalanceTransaction._id}, updating it to status Pending Buy`,
        {
          module: "TransactionService",
          method: "_processPendingSellRebalanceTransaction"
        }
      );

      await RebalanceTransaction.findByIdAndUpdate(rebalanceTransaction.id, {
        fees: aggregateFees(
          buyOrdersAfterFees.concat(existingSellOrders).map(({ fees }) => fees),
          owner.currency
        ),
        rebalanceStatus: "PendingBuy"
      });
    } else if (rejectedOrders.length === existingSellOrders.length) {
      logger.warn(`Rebalance transaction ${rebalanceTransaction._id} is now of status Rejected`, {
        module: "TransactionService",
        method: "_processPendingSellRebalanceTransaction"
      });
      await TransactionService._setRebalanceTransactionStatus(rebalanceTransaction.id, "Rejected");
    }
  }

  /**
   * Method that processes a PendingBuy rebalance transaction in our DB, by:
   *   1. Retrieving all buy orders that were created for that rebalance transaction.
   *   2. Syncing ones that are still Pending with WK.
   *   3. If all are matched, then update portfolio & holdings of the user.
   *   4. Setting the transaction status to Settled.
   */
  private static async _processPendingBuyRebalanceTransaction(
    transaction: RebalanceTransactionDocument
  ): Promise<void> {
    const existingOrders: OrderDocument[] = await Order.find({
      transaction: transaction._id
    });
    const existingBuyOrders: OrderDocument[] = existingOrders.filter((order) => order.side === "Buy");
    const existingSellOrders: OrderDocument[] = existingOrders.filter((order) => order.side === "Sell");
    // We find which buy orders are still pending and only sync those with WK.
    const pendingBuyOrders = existingBuyOrders.filter((order) =>
      ["Pending", "Open"].includes(order.providers?.wealthkernel?.status)
    );

    /**
     * Get a new array of orders containing all newly synced buy orders plus any
     * buy orders that were already synced from previous runs.
     */
    const allSyncedBuyOrders = pendingBuyOrders.concat(
      existingBuyOrders.filter((order) => {
        const assetId = InvestmentUniverseUtil.getAssetIdFromIsin(order.isin);
        const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(assetId));
        return !pendingBuyOrders.find((syncedOrder) => allAssetIsinsSet.has(syncedOrder.isin));
      })
    );

    if (allSyncedBuyOrders.every((order) => order?.providers?.wealthkernel?.status === "Matched")) {
      logger.info(
        `Finished processing rebalance transaction ${transaction._id}, updating the user's portfolio & cash and its status to Settled`,
        {
          module: "TransactionService",
          method: "_processPendingBuyRebalanceTransaction"
        }
      );

      await PortfolioService.updatePortfolioHoldings(
        (transaction.portfolio as PortfolioDocument).id,
        allSyncedBuyOrders.concat(existingSellOrders)
      );

      await TransactionService._setRebalanceTransactionStatus(transaction.id, "Settled");

      const { fees } = transaction;
      const properties: TrackTransactionInfoType = {
        fxFees: fees?.fx?.amount,
        commissionFees: fees?.commission?.amount
      };

      const triggeredByAutomation = !!transaction.linkedAutomation;

      eventEmitter.emit(events.transaction.rebalanceTransactionSuccess.eventId, transaction.owner, properties, {
        triggeredByAutomation
      });
    } else if (allSyncedBuyOrders.every((order) => order?.providers?.wealthkernel?.status === "Rejected")) {
      logger.warn(`Rebalance transaction ${transaction._id} is now of status Rejected`, {
        module: "TransactionService",
        method: "_processPendingBuyRebalanceTransaction"
      });
      await TransactionService._setRebalanceTransactionStatus(transaction.id, "Rejected");
    }
  }

  /**
   * @description Syncs an existing charge transaction with Wealthkernel. This behaves differently depending on the charge method.
   * 1. In case of 'cash', we want to link this transaction to a charge in Wealthkernel. In this case, we find the most recent charge
   *    of the correct amount and link it to our charge transaction.
   * 2. In case of 'holdings' and 'combined', there are two scenarios:
   *    a) Not all orders are matched, in which case, we sync them similarly to sell asset transaction order syncing,
   *    but without updating cash availability of the user.
   *    b) All orders are matched, in which case, we behave the same way as a cash charge transaction, trying to find
   *    a charge transaction in WK to link with.
   *
   * @param transaction
   * @param investmentProductDict
   * @private
   */
  private static async _syncChargeTransaction(
    transaction: ChargeTransactionDocument,
    investmentProductDict: { [isin: string]: InvestmentProductDocument }
  ): Promise<TransactionDocument> {
    logger.info(`Syncing charge transaction ${transaction._id}`, {
      module: "TransactionService",
      method: "_syncChargeTransaction"
    });

    const orders = transaction.orders;

    if (["holdings", "combined"].includes(transaction.chargeMethod)) {
      const receivedCash = OrderService.calculateReceivedCash(orders);

      const matchedOrders = orders.filter((order) => order?.providers?.wealthkernel?.status === "Matched");

      if (matchedOrders.length === orders.length) {
        logger.info(
          `Charge transaction ${transaction._id} has all matched orders for the first time - updating portfolio holdings & value...`,
          {
            module: "TransactionService",
            method: "_syncChargeTransaction"
          }
        );

        await transaction.populate([
          {
            path: "portfolio",
            populate: [
              { path: "currentTicker" },
              {
                path: "holdings.asset",
                populate: {
                  path: "currentTicker"
                }
              }
            ]
          }
        ]);

        await PortfolioService.updatePortfolioHoldings((transaction.portfolio as PortfolioDocument).id, orders, {
          investmentProductDictToUse: investmentProductDict
        });
        await PortfolioService.conditionallyUpdateGiftedHoldingsOnUnavailableHoldings(
          transaction.portfolio as PortfolioDocument,
          orders
        );

        // We calculate the new holdings amount as WK could have settled some orders at a different price
        const updatedHoldingsAmount = receivedCash.mul(100);

        const totalConsiderationAmount = updatedHoldingsAmount
          .add(transaction.consideration.cashAmount ?? 0)
          .toNumber();

        /**
         * Edge case: some times orders will settle with amount totaling to £0.
         * However, to keep a record of it, instead of deleting the charge transaction, we set it as Settled.
         */
        if (totalConsiderationAmount === 0) {
          logger.warn(
            `Forcing charge transaction ${transaction._id} to 'Settled' status due to 0-value consideration`,
            {
              module: "TransactionService",
              method: "_syncChargeTransaction"
            }
          );
          return ChargeTransaction.findByIdAndUpdate(
            transaction._id,
            {
              "consideration.amount": totalConsiderationAmount,
              "consideration.holdingsAmount": updatedHoldingsAmount.toNumber(),
              status: "Settled",
              settledAt: new Date(Date.now())
            },
            { new: true }
          );
        } else {
          return ChargeTransaction.findByIdAndUpdate(
            transaction._id,
            {
              status: "PendingWealthkernelCharge",
              "consideration.amount": totalConsiderationAmount,
              "consideration.holdingsAmount": updatedHoldingsAmount.toNumber()
            },
            { new: true }
          );
        }
      }
    }
  }

  /**
   * @description
   * @param transaction
   * @private
   */
  private static async _syncRevertRewardTransaction(transaction: RevertRewardTransactionDocument): Promise<void> {
    logger.info(`Syncing revert reward transaction ${transaction._id}`, {
      module: "TransactionService",
      method: "_syncRevertRewardTransaction"
    });

    if (transaction?.orders?.length === 0) {
      const portfolio = transaction.portfolio as PortfolioDocument;
      const reward = transaction.reward as RewardDocument;

      const ownerId = portfolio.populated("owner")
        ? (portfolio.owner as UserDocument).id
        : portfolio.owner.toString();

      const owner = await UserService.getUser(ownerId, {
        addresses: false,
        portfolios: false,
        subscription: true
      });

      // The user could have been charged on their rewarded holding and have less than the initially rewarded amount.
      // Therefore, when reverting the reward, we sell the rewarded quantity unless the current holding is less, in
      // which case we sell that.
      const rewardedAssetHolding = portfolio.holdings.find((holding) => holding.assetCommonId === reward.asset);

      const orderData: OrderDTOInterface = {
        isin: ASSET_CONFIG[reward.asset].isin,
        quantity: Decimal.min(reward.quantity, rewardedAssetHolding.quantity).toNumber(),
        settlementCurrency: owner.currency,
        side: "Sell",
        transaction: transaction._id,
        activeProviders: ProviderService.getProviders(owner.companyEntity, [ProviderScopeEnum.BROKERAGE]),
        fees: getZeroFees(owner.currency),
        consideration: {
          currency: owner.currency
        },
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
      };

      await OrderService.createDbOrder(orderData);
    } else if (transaction.orders?.[0]?.providers?.wealthkernel?.status === "Matched") {
      // For revert reward transactions there is only one sell order.
      const receivedCash = OrderService.calculateReceivedCash(transaction.orders);

      logger.info(
        `Order for revert reward transaction ${transaction._id} has matched, updating portfolio holdings...`,
        {
          module: "TransactionService",
          method: "_syncRevertRewardTransaction"
        }
      );

      await transaction.populate([
        {
          path: "portfolio",
          populate: [
            { path: "currentTicker" },
            {
              path: "holdings.asset",
              populate: {
                path: "currentTicker"
              }
            }
          ]
        }
      ]);

      await PortfolioService.updatePortfolioHoldings(
        (transaction.portfolio as PortfolioDocument).id,
        transaction.orders
      );

      await RevertRewardTransaction.findByIdAndUpdate(transaction._id, {
        status: "PendingWealthkernelCharge",
        "consideration.amount": receivedCash.mul(100).toNumber()
      });
    }
  }

  private static async _setRebalanceTransactionStatus(
    transactionId: string,
    status: RebalanceTransactionStatusType
  ): Promise<RebalanceTransactionDocument> {
    const settledAt = status === "Settled" ? new Date(Date.now()) : undefined;

    return RebalanceTransaction.findOneAndUpdate(
      { _id: new mongoose.Types.ObjectId(transactionId) },
      { rebalanceStatus: status, settledAt },
      { upsert: false, runValidators: true, new: true }
    );
  }

  private static async _isDepositLinkedToPendingDepositTransaction(
    transaction: DepositCashTransactionDocument
  ): Promise<boolean> {
    const [assetTransactionLinkedToDeposit, savingsTopupTransactionLinkedToDeposit] = await Promise.all([
      AssetTransaction.exists({
        pendingDeposit: transaction._id,
        status: "PendingDeposit"
      }),
      SavingsTopupTransaction.exists({
        pendingDeposit: transaction._id,
        status: "PendingDeposit"
      })
    ]);

    return !!assetTransactionLinkedToDeposit || !!savingsTopupTransactionLinkedToDeposit;
  }

  private static async _isDepositLinkedToTransaction(
    transaction: DepositCashTransactionDocument
  ): Promise<boolean> {
    const [assetTransactionLinkedToDeposit, savingsTopupTransactionLinkedToDeposit] = await Promise.all([
      AssetTransaction.exists({
        pendingDeposit: transaction._id
      }),
      SavingsTopupTransaction.exists({
        pendingDeposit: transaction._id
      })
    ]);

    return !!assetTransactionLinkedToDeposit || !!savingsTopupTransactionLinkedToDeposit;
  }

  private static async _syncPendingWealthkernelWithdrawalSafely(
    withdrawal: WithdrawalCashTransactionDocument
  ): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.OWNER);
    const user = withdrawal.owner as UserDocument;

    try {
      const response = await ProviderService.getBrokerageService(user.companyEntity).retrieveWithdrawal(
        withdrawal.providers.wealthkernel.id
      );

      const isSettled = response.status === "Settled";
      const settledAt =
        isSettled && withdrawal.withdrawalMethod !== WithdrawalMethodEnum.WITH_INTERMEDIARY
          ? new Date(Date.now())
          : undefined;
      const withdrawalData = {
        "providers.wealthkernel.status": response.status,
        "providers.wealthkernel.settledAt": isSettled ? new Date(Date.now()) : undefined,
        settledAt
      } as any;

      // If the type was 'Full', then we should update the consideration amount to reflect the one withdrawn by WK
      if (withdrawal.withdrawalRequestType === "Full" && response.status === "Settled") {
        const updatedWithdrawalAmount = Decimal.mul(response.paidOut.amount, 100).toNumber();
        if (updatedWithdrawalAmount !== withdrawal.consideration.amount) {
          logger.warn(`Consideration amount for full withdrawal ${withdrawal.id} has changed!`, {
            module: "TransactionService",
            method: "_syncPendingWealthkernelWithdrawalSafely",
            data: {
              originalAmount: withdrawal.consideration.amount,
              settledAmount: updatedWithdrawalAmount
            }
          });
        }

        withdrawalData.consideration = {
          amount: updatedWithdrawalAmount
        };
      }

      await WithdrawalCashTransaction.findByIdAndUpdate(withdrawal.id, withdrawalData);
    } catch (err) {
      captureException(err);
      logger.error(`Syncing with wealthkernel failed for withdrawal ${withdrawal._id}`, {
        module: "TransactionService",
        method: "_syncPendingWealthkernelWithdrawalSafely",
        data: { transactionId: withdrawal._id, userId: (withdrawal.owner as UserDocument)._id, error: err }
      });
    }
  }

  private static async _requestWealthkernelWithdrawalSafely(withdrawal: WithdrawalCashTransactionDocument) {
    try {
      await Promise.all([
        DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.PORTFOLIO),
        DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.OWNER),
        DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.BANK_ACCOUNT)
      ]);

      const owner = withdrawal.owner as UserDocument;

      if (await TransactionService._isPotentiallyFraudulentWithdrawal(withdrawal)) {
        logger.warn(
          `Did not request withdrawal ${withdrawal.id} because it could be potentially fraudulent, contact user!`,
          {
            module: "TransactionService",
            method: "_requestWealthkernelWithdrawalSafely",
            userEmail: (withdrawal.owner as UserDocument).email
          }
        );
        return;
      }

      const wkWithdrawalRequest = await ProviderService.getBrokerageService(owner.companyEntity).requestWithdrawal(
        withdrawal
      );

      if (wkWithdrawalRequest?.id) {
        // Save the ID of the newly requested withdrawal
        await WithdrawalCashTransaction.findByIdAndUpdate(withdrawal.id, {
          "providers.wealthkernel": { id: wkWithdrawalRequest.id, status: "Pending" }
        });

        const properties: TrackWithdrawalPropertiesType = {
          amount: Decimal.div(withdrawal.consideration.amount, 100).toNumber(),
          currency: withdrawal.consideration.currency
        };

        eventEmitter.emit(events.transaction.withdrawalCreation.eventId, owner, properties);
      }
    } catch (err) {
      captureException(err);
      logger.error(`Failed to fetch withdrawal ${withdrawal._id}`, {
        module: "TransactionService",
        method: "_requestWealthkernelWithdrawalSafely",
        data: { transactionId: withdrawal._id, userId: (withdrawal.owner as UserDocument)._id, error: err }
      });
    }
  }

  /**
   * We'd like to not allow withdrawals in case:
   * 1. The user has direct debit payments **AND**
   * 2. Those direct debit payments are with an unverified bank i.e. one created before the 12th of June (the date when we introduced the
   * connect bank account flow) **AND** not used in a Truelayer payment **AND**
   * 3. They are trying to withdraw to a different bank account than the one used for direct debit payments
   * @param withdrawal
   * @private
   */
  private static async _isPotentiallyFraudulentWithdrawal(
    withdrawal: WithdrawalCashTransactionDocument
  ): Promise<boolean> {
    const withdrawalBankAccountId = (withdrawal.bankAccount as BankAccountDocument).id;

    // If the user has no direct debit payments, then we can safely return false early.
    const settledDirectDebitPayments: DepositCashTransactionDocument[] = await DepositCashTransaction.find({
      owner: withdrawal.owner.id,
      linkedAutomation: { $exists: true },
      "directDebit.providers.wealthkernel.status": "Completed"
    }).populate("bankAccount");

    if (settledDirectDebitPayments.length === 0) {
      return false;
    }

    // Since the user has direct debit payments, we check if those were made with unverified bank accounts (ones not used
    // in Truelayer payments and not connected through Truelayer Data).
    const banksUsedInDirectDebitPayments: BankAccountDocument[] = settledDirectDebitPayments.map(
      (payment) => payment.bankAccount as BankAccountDocument
    );
    const bankIDsUsedInTruelayerPayments = (
      (await DepositCashTransaction.find({
        owner: withdrawal.owner.id,
        "providers.truelayer.status": "executed",
        bankAccount: { $exists: true }
      })) as DepositCashTransactionDocument[]
    ).map((deposit) => deposit.bankAccount.toString());

    // Banks connected before the 12th of June *AND* without Truelayer payments are not verified
    const unverifiedBankIDsUsedInDirectDebitPayments = banksUsedInDirectDebitPayments
      .filter(
        (bank) =>
          (!bank.createdAt || bank.createdAt < new Date("2023-06-12T00:00:00")) &&
          !bankIDsUsedInTruelayerPayments.includes(bank.id)
      )
      .map((bank) => bank.id);

    // The bank accounts used for direct debits are all verified i.e. connected through Truelayer Data or used in
    // Truelayer payments. In this case, we can safely return false.
    if (unverifiedBankIDsUsedInDirectDebitPayments.length === 0) {
      return false;
    }

    return !unverifiedBankIDsUsedInDirectDebitPayments.every((bankId) => bankId === withdrawalBankAccountId);
  }

  /**
   * Populates keys provided with true value of every entry of data provided.
   *
   * @param populate
   * @param data
   */
  private static async _populateAllTransactions(populate: Record<string, boolean>, data: TransactionDocument[]) {
    if (data == null || populate == null) return;

    await Promise.all(
      Object.entries(populate)
        .filter(([, shouldPopulate]) => shouldPopulate)
        .map(([populateKey]) =>
          data
            .filter((entry) => !entry.populated(populateKey))
            .map((entry) => {
              if (populateKey.includes(".")) {
                const [field, nestedField] = populateKey.split(".");
                return entry.populate({
                  path: field,
                  populate: {
                    path: nestedField
                  },
                  strictPopulate: false
                });
              } else return entry.populate({ path: populateKey, strictPopulate: false });
            })
        )
        .flat()
    );
  }

  private static async _emitInvestmentCreationEvent(transaction: AssetTransactionDocument): Promise<void> {
    // if transaction is recurring, event has already been emitted during submission,
    // and we don't want to be emitted again
    if (transaction.linkedAutomation) {
      return;
    }

    const [isFirst] = await Promise.all([
      UserService.userHasSingleInvestment(transaction.owner.id),
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.ORDERS)
    ]);

    const { portfolioTransactionCategory, fees, consideration } = transaction;

    const cashback = transaction.cashback as CashbackTransactionDocument;
    const orders = transaction.orders as OrderDocument[];

    let category: TrackTransactionInfoCategoryType;
    let assetName: string;
    let amountToUse: number;
    if (portfolioTransactionCategory === "update") {
      const { consideration, commonId } = orders[0];

      amountToUse = new Decimal(consideration.originalAmount ?? consideration?.amount).div(100).toNumber();
      assetName = ASSET_CONFIG[commonId as investmentUniverseConfig.AssetType].simpleName;
      category = ASSET_CONFIG[commonId as investmentUniverseConfig.AssetType].category;
    } else {
      category = "portfolio";
      amountToUse = new Decimal(transaction.originalInvestmentAmount ?? transaction.consideration?.amount)
        .div(100)
        .toNumber();
    }

    const transactionInfo: TrackTransactionInfoType = {
      side: "buy",
      category,
      assetName,
      amount: amountToUse,
      currency: consideration.currency,
      cashbackAmount: Decimal.div(cashback?.consideration?.amount ?? 0, 100).toNumber(),
      fxFees: fees?.fx?.amount ?? 0,
      commissionFees: fees?.commission?.amount ?? 0,
      executionSpreadFees: fees?.executionSpread?.amount ?? 0,
      frequency: transaction.linkedAutomation ? "repeating" : "one-off"
    };

    const properties: TrackPropertiesType = {
      isFirst,
      ...transactionInfo
    };

    eventEmitter.emit(events.transaction.investmentCreation.eventId, transaction.owner, properties);
  }

  /**
   * Creates a Wealthyhood dividend transaction for the given portfolio and month based on their average holdings
   * during that month.
   */
  private static async _createWealthyhoodDividend(
    portfolio: PortfolioDocument,
    price: plansConfig.PriceType,
    dividendMonth: string
  ): Promise<WealthyhoodDividendTransactionDocument> {
    if (!portfolio.populated("owner")) {
      await portfolio.populate("owner");
    }

    const owner = portfolio.owner as UserDocument;
    const PRICE_CONFIG = ConfigUtil.getPricing(owner.companyEntity);

    // Dividend month has format YYYY-MM so using that we build the first day of the dividend month.
    const firstDayOfDividendMonth = new Date(`${dividendMonth}-01T00:00:00.000+00:00`);
    const lastDayOfDividendMonth = DateUtil.getDateOfDaysAgo(
      DateUtil.getFirstDayOfNextMonth(firstDayOfDividendMonth),
      1
    );

    const numberOfWeekDaysForDividendMonth = DateUtil.getNumberOfWeekDaysBetween(
      firstDayOfDividendMonth,
      lastDayOfDividendMonth
    );

    const tickersForDividendMonthObjects = await DailyPortfolioTicker.aggregate([
      {
        $match: {
          portfolio: new mongoose.Types.ObjectId(portfolio.id),
          date: {
            $gte: firstDayOfDividendMonth,
            $lt: DateUtil.getFirstDayOfNextMonth(firstDayOfDividendMonth)
          }
        }
      },
      {
        $addFields: {
          dayOfWeek: { $dayOfWeek: "$date" }
        }
      },
      {
        $match: {
          $expr: {
            $not: {
              $in: ["$dayOfWeek", [1, 7]] // Exclude Sunday (1) and Saturday (7)
            }
          }
        }
      }
    ]);

    const tickersForDividendMonth = tickersForDividendMonthObjects.map((obj) => DailyPortfolioTicker.hydrate(obj));

    // We don't want to create WH dividend for users that were fully withdrawn for the whole month
    if (tickersForDividendMonth.every((ticker) => ticker.getPrice(owner.currency) === 0)) {
      return;
    }

    const averagePortfolioTickerValue = tickersForDividendMonth
      .map((ticker) => ticker.getPrice(owner.currency))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(Decimal.max(tickersForDividendMonth.length, numberOfWeekDaysForDividendMonth))
      .round();

    if (averagePortfolioTickerValue.lessThan(MINIMUM_PORTFOLIO_VALUE_FOR_DIVIDEND)) {
      return;
    }

    const plan = PRICE_CONFIG[price].plan;

    // The rate figure is a yearly rate that is given monthly. Therefore, to get the monthly rate, we divide it by 12.
    const monthlyDividendRate = Decimal.div(WEALTHYHOOD_DIVIDEND_RATES[plan], 12);

    // If the calculated average portfolio value is more than the maximum, we create WH dividend for the maximum instead.
    let finalDividendAmount: Decimal;
    if (averagePortfolioTickerValue.greaterThanOrEqualTo(MAXIMUM_PORTFOLIO_VALUE_FOR_DIVIDEND)) {
      finalDividendAmount = Decimal.mul(MAXIMUM_PORTFOLIO_VALUE_FOR_DIVIDEND, monthlyDividendRate).toDecimalPlaces(
        2
      );
    } else {
      finalDividendAmount = averagePortfolioTickerValue.mul(monthlyDividendRate).toDecimalPlaces(2);
    }

    const transactionData: WealthyhoodDividendTransactionDTOInterface = {
      consideration: {
        currency: owner.currency,
        amount: Decimal.mul(finalDividendAmount, 100).toNumber() // amount is stored in cents
      },
      owner: owner.id,
      portfolio: portfolio.id,
      dividendMonth,
      createdAt: new Date(),
      price,
      hasViewedAppModal: false,
      deposit: {
        activeProviders: ProviderService.getProviders(owner.companyEntity, [ProviderScopeEnum.BROKERAGE])
      }
    };

    return await new WealthyhoodDividendTransaction(transactionData).save();
  }

  private static async _createWealthyhoodDividendDeposit(
    wealthyhoodDividend: WealthyhoodDividendTransactionDocument
  ): Promise<void> {
    logger.info(`Creating Wealthyhood dividend bonus for doc ${wealthyhoodDividend.id}`, {
      module: "TransactionService",
      method: "_createWealthyhoodDividendDeposit"
    });

    const portfolio = wealthyhoodDividend.portfolio as PortfolioDocument;

    if (!portfolio.populated("owner")) {
      await portfolio.populate("owner");
    }
    const owner = portfolio.owner as UserDocument;

    const bonusPayment = await ProviderService.getBrokerageService(owner.companyEntity).createBonus({
      destinationPortfolio: portfolio.providers?.wealthkernel?.id,
      consideration: {
        currency: owner.currency,
        amount: Decimal.div(wealthyhoodDividend.consideration.amount, 100).toNumber()
      },
      clientReference: wealthyhoodDividend.id
    });

    logger.info(
      `Created Wealthyhood dividend bonus for doc ${wealthyhoodDividend.id} with WK id ${bonusPayment.id}`,
      {
        module: "TransactionService",
        method: "_createWealthyhoodDividendDeposit"
      }
    );

    await WealthyhoodDividendTransaction.findByIdAndUpdate(wealthyhoodDividend.id, {
      "deposit.providers.wealthkernel": {
        status: "Created",
        id: bonusPayment.id,
        submittedAt: new Date(Date.now())
      }
    });
  }

  private static _emitSubscriptionChargeSuccessEvent(
    user: UserDocument,
    properties: {
      chargeMethod: Omit<ChargeMethodType, "orders">;
      price: plansConfig.PriceType;
      actualCharge: number;
      settledAt: Date;
    }
  ): void {
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const { plan, amount: nominalCharge, recurrence } = PRICE_CONFIG[properties.price];

    const trackProps: TrackSubscriptionChargeSuccess = {
      nominalCharge,
      actualCharge: properties.actualCharge,
      chargeMethod: properties.chargeMethod,
      plan,
      recurrence,
      settledAt: properties.settledAt,
      currency: user.currency
    };

    eventEmitter.emit(events.transaction.subscriptionChargeSuccess.eventId, user, trackProps);
  }

  private static async _submitWkCharges(charges: ChargesType): Promise<void> {
    const {
      commissionAmounts,
      executionSpreadAmounts,
      fxAmounts,
      custodyAmounts,
      revertRewardAmounts,
      dividendFeeAmounts,
      realtimeExecutionFeeAmounts
    } = charges;

    const chargesByPortfolioId = Object.entries(commissionAmounts)
      .map(([portfolioId, charges]) =>
        charges.map((charge) => [portfolioId, { ...charge, chargeType: "commission" }])
      )
      .flat()
      .concat(
        Object.entries(executionSpreadAmounts)
          .map(([portfolioId, charges]) =>
            charges.map((charge) => [portfolioId, { ...charge, chargeType: "executionSpread" }])
          )
          .flat()
      )
      .concat(
        Object.entries(fxAmounts)
          .map(([portfolioId, charges]) => charges.map((charge) => [portfolioId, { ...charge, chargeType: "fx" }]))
          .flat()
      )
      .concat(
        Object.entries(custodyAmounts)
          .map(([portfolioId, charges]) =>
            charges.map((charge) => [portfolioId, { ...charge, chargeType: "custody" }])
          )
          .flat()
      )
      .concat(
        Object.entries(revertRewardAmounts)
          .map(([portfolioId, charges]) =>
            charges.map((charge) => [portfolioId, { ...charge, chargeType: "commission", isRevertReward: true }])
          )
          .flat()
      )
      .concat(
        Object.entries(dividendFeeAmounts)
          .map(([portfolioId, charges]) =>
            charges.map((charge) => [portfolioId, { ...charge, chargeType: "dividendCommission" }])
          )
          .flat()
      )
      .concat(
        Object.entries(realtimeExecutionFeeAmounts)
          .map(([portfolioId, charges]) =>
            charges.map((charge) => [portfolioId, { ...charge, chargeType: "realtimeExecution" }])
          )
          .flat()
      );

    logger.info(`About to submit ${chargesByPortfolioId.length} charges to Wealthkernel`, {
      module: "TransactionService",
      method: "_submitWkCharges"
    });

    for (let i = 0; i < chargesByPortfolioId.length; i++) {
      const [wkPortfolioId, charge] = chargesByPortfolioId[i] as [
        string,
        {
          chargeType: ChargeTypeType;
          transactionId: string;
          amount: number;
          currency: currenciesConfig.MainCurrencyType;
          companyEntity: entitiesConfig.CompanyEntityEnum;
          isRevertReward?: boolean;
        }
      ];

      try {
        const wealthkernelCharge = await ProviderService.getBrokerageService(charge.companyEntity).createCharge(
          {
            portfolioId: wkPortfolioId,
            narrative: TransactionService._mapChargeTypeToNarrative(charge.chargeType) as string,
            clientReference: charge.transactionId,
            consideration: {
              currency: MainCurrencyToWealthkernelCurrency[charge.currency],
              amount: charge.amount
            }
          },
          charge.transactionId + "-retry"
        );

        if (charge.isRevertReward) {
          await RevertRewardTransaction.updateOne(
            { _id: charge.transactionId },
            {
              "providers.wealthkernel.id": wealthkernelCharge.id,
              "providers.wealthkernel.submittedAt": new Date(Date.now())
            }
          );
        } else {
          await ChargeTransaction.updateOne(
            { _id: charge.transactionId },
            {
              "providers.wealthkernel.id": wealthkernelCharge.id,
              "providers.wealthkernel.submittedAt": new Date(Date.now())
            }
          );
        }

        logger.info(`Successfully created WK charge for ${charge.transactionId}`, {
          module: "TransactionService",
          method: "_submitWkCharges"
        });
      } catch (err) {
        captureException(err);
        logger.error(`WK Charge creation failed for ${charge.transactionId}`, {
          module: "TransactionService",
          method: "_submitWkCharges",
          data: {
            transactionId: charge.transactionId,
            error: err
          }
        });
      }
    }
  }

  private static _mapChargeTypeToNarrative(chargeType: ChargeTypeType): Omit<ChargeTypeType, "executionSpread"> {
    const CHARGE_TYPE_NARRATIVE_MAPPING: Record<ChargeTypeType, Omit<ChargeTypeType, "executionSpread">> = {
      fx: "fx",
      commission: "commission",
      subscription: "subscription",
      custody: "custody",
      executionSpread: "commission",
      remainder: "commission",
      dividendCommission: "dividendCommission",
      realtimeExecution: "commission"
    };

    return CHARGE_TYPE_NARRATIVE_MAPPING[chargeType];
  }

  private static async _updateWkDepositStatus(
    transaction: DepositCashTransactionDocument,
    newWkStatus: DepositStatusType
  ): Promise<void> {
    if (transaction?.providers?.wealthkernel?.status === "Settled") {
      logger.warn(`Cannot update WK deposit for settled transaction ${transaction.id}`, {
        module: "TransactionService",
        method: "_updateWkDepositStatus",
        data: {
          transactionId: transaction.id
        }
      });
      return;
    }

    logger.info(`Syncing deposit transaction ${transaction.id}`, {
      module: "TransactionService",
      method: "_updateWkDepositStatus"
    });

    if (newWkStatus === "Settled") {
      await transaction.populate([
        { path: "owner", populate: { path: "participant" } },
        { path: "portfolio" },
        { path: "linkedAssetTransaction", populate: { path: "orders" } },
        { path: "linkedSavingsTopup" },
        { path: "linkedCreditTicket" }
      ]);

      const portfolio = transaction.portfolio as PortfolioDocument;
      const user = transaction.owner as UserDocument;
      const linkedAssetTransaction = transaction.linkedAssetTransaction as AssetTransactionDocument;
      const linkedSavingsTopup = transaction.linkedSavingsTopup as SavingsTopupTransactionDocument;
      const linkedCreditTicket = transaction.linkedCreditTicket as CreditTicketDocument;
      const depositAmount = Decimal.div(transaction.consideration.amount, 100).toNumber();

      const isBankAccountNameChecked = await TransactionService.isTransactionBankAccountNameChecked(transaction);
      if (!isBankAccountNameChecked) {
        return;
      }

      // Update cash field in portfolio if the transaction is not a one-step investment.
      if (
        transaction.depositAction === DepositActionEnum.JUST_PAY &&
        !["Credited", "Settled"].includes(linkedCreditTicket?.status)
      ) {
        await PortfolioService.updateCashAvailability(portfolio.id, user.currency, depositAmount, {
          available: true,
          settled: true
        });
      } else if (
        transaction.depositAction === DepositActionEnum.DEPOSIT_AND_INVEST &&
        ![DepositMethodEnum.DIRECT_DEBIT, DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER].includes(
          transaction.depositMethod
        )
      ) {
        if (linkedAssetTransaction) {
          const orders = linkedAssetTransaction.orders as OrderDocument[];

          // For direct debit payments, we do not allow cash to be returned in this flow
          // as we rely completely on cash being returned within the order cancellation flow.
          await TransactionService._returnCashForCancelledOrders(orders, linkedAssetTransaction);
        }
      }

      const isDepositLinkedToTransaction =
        linkedAssetTransaction?.status === "PendingDeposit" || linkedSavingsTopup?.status === "PendingDeposit";

      const eventData: TrackDepositPropertiesType = {
        amount: depositAmount,
        currency: transaction.consideration.currency,
        noAssetTransactionPending: !isDepositLinkedToTransaction
      };

      logger.info("eventEmitter.emit - depositSuccess", {
        module: "TransactionService",
        method: "_updateWkDepositStatus",
        data: { ...eventData, depositId: transaction.id }
      });

      eventEmitter.emit(events.transaction.depositSuccess.eventId, user, eventData);

      const settledAt = new Date(Date.now());

      await DepositCashTransaction.findByIdAndUpdate(transaction._id, {
        "providers.wealthkernel.status": "Settled",
        "providers.wealthkernel.settledAt": settledAt,
        status: "Settled",
        settledAt
      });

      // If there is a linked transaction to this deposit, we want to update the status from PendingDeposit to Pending
      if (isDepositLinkedToTransaction) {
        await TransactionService._handlePendingDepositSettlement(transaction);
      }

      // If there is a linked credit ticket, we want to update the status from Credited to Settled
      if (["Credited", "CreditPendingNameCheck"].includes(linkedCreditTicket?.status)) {
        await CreditTicketRepository.settleCreditTicket(linkedCreditTicket.id);
      } else {
        // Emit a deposit available notification ONLY if we haven't credited the account already (in that case we have already sent
        // a deposit available notification)
        logger.info("eventEmitter.emit - depositAvailable", {
          module: "TransactionService",
          method: "_updateWkDepositStatus",
          data: { ...eventData, depositId: transaction.id }
        });

        eventEmitter.emit(events.transaction.depositAvailable.eventId, user, eventData);
      }
    } else if (newWkStatus === "Rejected") {
      await transaction.populate([{ path: "owner", populate: { path: "participant" } }]);
      logger.info("eventEmitter.emit - depositFailure", {
        module: "TransactionService",
        method: "_updateWkDepositStatus"
      });
      eventEmitter.emit(events.transaction.depositFailure.eventId, transaction.owner as UserDocument);
      await DepositCashTransaction.findByIdAndUpdate(transaction._id, {
        "providers.wealthkernel.status": "Rejected",
        status: "Rejected"
      });
    } else {
      if (newWkStatus === "Created") {
        await DepositCashTransaction.findByIdAndUpdate(transaction._id, {
          "providers.wealthkernel.status": "Created"
        });
      } else if (newWkStatus === "Cancelled" || newWkStatus === "Cancelling") {
        await DepositCashTransaction.findByIdAndUpdate(transaction._id, {
          "providers.wealthkernel.status": newWkStatus,
          status: "Cancelled"
        });
      } else {
        throw new Error(`Invalid or New status ${newWkStatus} for deposit ${transaction.id}`);
      }
    }
  }

  /**
   * @description
   * Perform post query filtering on transactions:
   *  1. Filter based on given statuses (this also includes virtual statuses)
   *  2. Filter pending deposits linked to valid asset transactions
   *  3. Filter incomplete deposits
   *  4. Fitler cashbacks/asset transactions that are linked to an incomplete deposit
   */
  private static async _filterPostQueryTransactions(
    transactions: TransactionDocument[],
    filter: TransactionsFilter
  ): Promise<TransactionDocument[]> {
    let filteredTransactions = transactions;

    if (filter.statuses) {
      filteredTransactions = filteredTransactions.filter(({ status }) => filter.statuses.includes(status));
    }

    if (filter.filterOutPendingDepositsLinkedToValidAssetTransaction) {
      await TransactionService._populateAllTransactions({ linkedAssetTransaction: true }, filteredTransactions);

      filteredTransactions = filteredTransactions.filter((transaction) => {
        if (transaction.category !== "DepositCashTransaction" || transaction.status !== "Pending") {
          return true; // Keep the transaction if it's not a pending deposit
        }

        const linkedAssetTransaction = (transaction as DepositCashTransactionDocument).linkedAssetTransaction;

        if (!linkedAssetTransaction) {
          return true; // Keep the transaction if there's no linked asset transaction
        }

        // Check if the linked transaction is neither Cancelled nor Rejected
        const isLinkedTransactionCancelledOrRejected =
          linkedAssetTransaction.status === "Cancelled" || linkedAssetTransaction.status === "Rejected";

        // If the linked transaction is valid, filter out the transaction. Otherwise, keep it.
        return isLinkedTransactionCancelledOrRejected;
      });
    }

    if (filter.filterOutIncompleteDeposits) {
      filteredTransactions = filteredTransactions.filter(
        (transaction) =>
          !(
            transaction.category === "DepositCashTransaction" &&
            !(transaction as DepositCashTransactionDocument).isPaymentAuthorised
          )
      );
    }

    /**
     * Filter out transactions linked to incomplete deposits
     */
    if (!filter.includeTransactionsLinkedToIncompleteDeposits) {
      await TransactionService._populateAllTransactions(
        { "linkedAssetTransaction.pendingDeposit": true, pendingDeposit: true },
        filteredTransactions
      );
      filteredTransactions = filteredTransactions.filter((transaction) => {
        if (transaction.category === "CashbackTransaction") {
          const cashback = transaction as CashbackTransactionDocument;
          const pendingDeposit = (cashback?.linkedAssetTransaction as AssetTransactionDocument)
            ?.pendingDeposit as DepositCashTransactionDocument;
          return pendingDeposit?.isPaymentAuthorised ?? true;
        } else if (transaction.category == "AssetTransaction") {
          const pendingDeposit = (transaction as AssetTransactionDocument)
            ?.pendingDeposit as DepositCashTransactionDocument;
          return pendingDeposit?.isPaymentAuthorised ?? true;
        } else {
          return true;
        }
      });
    }

    /**
     * Filter out assetTransactions with depositFailedStatus
     */
    filteredTransactions = filteredTransactions.filter((transaction) => transaction.status !== "DepositFailed");

    return filteredTransactions;
  }

  private static async _fetchCashActivityTransactionDocuments(
    userId: string,
    limit = 0 // Default limit is 0 (no limit)
  ): Promise<TransactionDocument[]> {
    // deposit (includes savings withdrawals)
    // we filter out deposits that are linked to savings top-ups (because on the client we
    // display the corresponding savings top-ups transaction)
    const transactions: TransactionDocument[][] = await Promise.all([
      DepositCashTransaction.find({
        owner: userId,
        depositAction: { $ne: DepositActionEnum.DEPOSIT_AND_SAVE },
        $or: [
          { "providers.truelayer.status": { $in: ["authorized", "executed"] } },
          { "providers.saltedge.status": { $in: ["accepted"] } },
          { "directDebit.providers.wealthkernel.status": { $in: ["Collected", "Completed"] } },
          { "directDebit.providers.gocardless.status": { $in: ["confirmed", "paid_out"] } },
          {
            "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.status": {
              $in: ["created", "confirmed"]
            }
          }
        ]
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("bankAccount linkedAutomation linkedSavingsTopup linkedAssetTransaction linkedCreditTicket"),
      SavingsWithdrawalTransaction.find({
        owner: userId,
        // we cannot have cancelled or rejected savings withdrawals
        status: { $in: ["PendingTopUp", "Pending", "Settled"] }
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("orders"),

      // withdraw (includes savings topups)
      WithdrawalCashTransaction.find({ owner: userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("bankAccount"),
      SavingsTopupTransaction.find({
        owner: userId,
        // we cannot have cancelled or rejected savings withdrawals
        status: { $in: ["Pending", "Settled"] },
        pendingDeposit: { $exists: false }
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate([
          { path: "orders" },
          {
            path: "pendingDeposit",
            populate: { path: "bankAccount" }
          },
          {
            path: "linkedAutomation"
          },
          {
            path: "linkedSavingsDividend"
          }
        ]),

      // investments
      AssetTransaction.find({
        owner: userId,
        status: { $in: ["PendingDeposit", "Pending", "Settled"] },
        // do not include legacy transactions with both buy and sell orders
        $or: [
          { portfolioTransactionCategory: "update", $expr: { $eq: [{ $size: "$orders" }, 1] } },
          { portfolioTransactionCategory: "buy" },
          { portfolioTransactionCategory: "sell" }
        ],
        // do not include transactions that are linked to a gift
        pendingGift: { $exists: false }
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate([
          { path: "orders" },
          {
            path: "pendingDeposit",
            populate: [{ path: "bankAccount" }, { path: "linkedCreditTicket" }]
          },
          {
            path: "linkedAutomation",
            populate: {
              path: "mandate"
            }
          }
        ]),

      // dividends (cash dividends from stocks)
      DividendTransaction.find({ owner: userId, "providers.wealthkernel.status": "Settled" }),

      // bonus (cashback + plan dividends)
      CashbackTransaction.find({ owner: userId, status: { $in: ["Settled", "Pending"] } })
        .populate({
          path: "linkedAssetTransaction",
          populate: { path: "pendingDeposit" }
        })
        .sort({ createdAt: -1 })
        .limit(limit),
      WealthyhoodDividendTransaction.find({
        owner: userId,
        "deposit.providers.wealthkernel.status": "Settled"
      })
        .sort({ createdAt: -1 })
        .limit(limit)
    ]);

    const sortedTransactions = transactions
      .flat()
      .sort((a, b) => b.sortingField.getTime() - a.sortingField.getTime());

    return sortedTransactions.slice(0, limit > 0 ? limit : sortedTransactions.length);
  }

  /**
   * @description Fetches the transaction documents for the investment activity of the user.
   * @param userId
   * @param limit
   */
  private static async _fetchInvestmentActivityTransactionDocuments(
    userId: string,
    limit = 0 // Default limit is 0 (no limit)
  ): Promise<TransactionDocument[]> {
    const transactions: TransactionDocument[][] = await Promise.all([
      // buy & sell
      AssetTransaction.find({
        owner: userId,
        status: { $in: ["PendingDeposit", "PendingGift", "Pending", "Cancelled", "Settled", "Rejected"] },
        // do not include legacy transactions with both buy and sell orders
        $or: [
          { portfolioTransactionCategory: "update", $expr: { $eq: [{ $size: "$orders" }, 1] } },
          { portfolioTransactionCategory: "buy" },
          { portfolioTransactionCategory: "sell" }
        ]
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate([
          { path: "orders" },
          {
            path: "pendingDeposit",
            populate: [{ path: "bankAccount" }, { path: "linkedCreditTicket" }]
          },
          {
            path: "linkedAutomation",
            populate: {
              path: "mandate"
            }
          }
        ]),
      // rebalance
      RebalanceTransaction.find({ owner: userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("orders linkedAutomation"),
      // stock dividends
      DividendTransaction.find({ owner: userId, "providers.wealthkernel.status": "Settled" })
    ]);

    const sortedTransactions = transactions
      .flat()
      .sort((a, b) => b.sortingField.getTime() - a.sortingField.getTime());

    return sortedTransactions.slice(0, limit > 0 ? limit : sortedTransactions.length);
  }
  private static async _getUserActivityTransactions(
    userId: string,
    limit = 0 // Default limit is 0 (no limit)
  ): Promise<TransactionDocument[]> {
    const transactions: TransactionDocument[][] = await Promise.all([
      AssetTransaction.find({
        owner: userId,
        status: { $in: ["PendingDeposit", "PendingGift", "Pending", "Cancelled", "Settled", "Rejected"] }
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate([
          { path: "orders" },
          {
            path: "pendingDeposit",
            populate: [{ path: "bankAccount" }, { path: "linkedCreditTicket" }]
          },
          {
            path: "linkedAutomation",
            populate: {
              path: "mandate"
            }
          }
        ]),
      CashbackTransaction.find({ owner: userId, status: { $in: ["Settled", "Pending"] } })
        .sort({ createdAt: -1 })
        .limit(limit),
      DepositCashTransaction.find({
        owner: userId,
        $or: [
          { "providers.truelayer.status": { $in: ["authorized", "executed"] } },
          { "providers.saltedge.status": { $in: ["accepted"] } },
          { "directDebit.providers.wealthkernel.status": { $in: ["Collected", "Completed"] } },
          { "directDebit.providers.gocardless.status": { $in: ["confirmed", "paid_out"] } }
        ]
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("bankAccount linkedAutomation linkedSavingsTopup linkedAssetTransaction"),
      DividendTransaction.find({ owner: userId, "providers.wealthkernel.status": "Settled" }),
      WithdrawalCashTransaction.find({ owner: userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("bankAccount"),
      SavingsTopupTransaction.find({
        owner: userId,
        status: { $in: ["Pending", "Cancelled", "Settled", "Rejected"] },
        pendingDeposit: { $exists: false }
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate([
          { path: "orders" },
          {
            path: "pendingDeposit",
            populate: { path: "bankAccount" }
          },
          {
            path: "linkedAutomation"
          },
          {
            path: "linkedSavingsDividend"
          }
        ]),
      SavingsWithdrawalTransaction.find({
        owner: userId,
        status: { $in: ["PendingTopUp", "Pending", "Cancelled", "Settled", "Rejected"] }
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("orders"),
      RebalanceTransaction.find({ owner: userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate("orders linkedAutomation"),
      WealthyhoodDividendTransaction.find({
        owner: userId,
        "deposit.providers.wealthkernel.status": "Settled"
      })
        .sort({ createdAt: -1 })
        .limit(limit)
    ]);

    const sortedTransactions = transactions.flat().sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    return sortedTransactions.slice(0, limit > 0 ? limit : sortedTransactions.length);
  }

  private static async _markPendingDepositAssetTransactionAsPending(
    transaction: AssetTransactionDocument,
    options: { emitEvents: boolean }
  ): Promise<void> {
    const updatedAssetTransaction = await AssetTransaction.findOneAndUpdate(
      {
        _id: transaction._id,
        status: "PendingDeposit"
      },
      {
        status: "Pending"
      },
      {
        new: true
      }
    ).populate("owner portfolio orders");

    logger.info(
      `Updated status of linkedAssetTransaction ${updatedAssetTransaction._id} from PendingDeposit to Pending`,
      {
        module: "TransactionService",
        method: "_markPendingDepositAssetTransactionAsPending"
      }
    );

    if (options.emitEvents) {
      const owner = updatedAssetTransaction.owner as UserDocument;
      const isFirstInvestment = await UserService.userHasSingleInvestment(owner.id);
      if (isFirstInvestment) {
        eventEmitter.emit(events.transaction.firstInvestmentCreation.eventId, owner);
      }

      await TransactionService._emitInvestmentCreationEvent(updatedAssetTransaction);
    }

    await OrderService.submitRealtimeOrdersSafely(
      updatedAssetTransaction.orders,
      updatedAssetTransaction.portfolio as PortfolioDocument
    );
  }

  private static async _markPendingDepositSavingsTopupAsPending(
    transaction: SavingsTopupTransactionDocument
  ): Promise<void> {
    const updatedTransaction = await SavingsTopupTransaction.findOneAndUpdate(
      {
        _id: transaction._id,
        status: "PendingDeposit"
      },
      {
        status: "Pending"
      },
      {
        runValidators: true
      }
    ).populate("owner");

    const properties: TrackTransactionInfoType = {
      side: "buy",
      category: "savings",
      assetName: updatedTransaction.savingsProduct,
      amount: Decimal.div(updatedTransaction.consideration.amount, 100).toNumber(),
      currency: updatedTransaction.consideration.currency,
      cashbackAmount: 0,
      fxFees: 0,
      commissionFees: 0,
      executionSpreadFees: 0,
      frequency: "one-off"
    };
    eventEmitter.emit(
      events.transaction.investmentCreation.eventId,
      updatedTransaction.owner as UserDocument,
      properties
    );

    logger.info(`Updated status of savings topup transaction ${transaction._id} from PendingDeposit to Pending`, {
      module: "TransactionService",
      method: "_markPendingDepositSavingsTopupAsPending"
    });
  }

  private static async _handlePendingDepositSettlement(
    pendingDeposit: DepositCashTransactionDocument
  ): Promise<void> {
    const transaction: TransactionDocument = await Transaction.findOne({
      category: { $in: ["AssetTransaction", "SavingsTopupTransaction"] },
      pendingDeposit: pendingDeposit._id,
      status: "PendingDeposit"
    });

    if (transaction.category === "AssetTransaction") {
      const assetTransaction = transaction as AssetTransactionDocument;
      await TransactionService._markPendingDepositAssetTransactionAsPending(assetTransaction, {
        emitEvents: !assetTransaction.linkedAutomation
      });
    } else if (transaction.category === "SavingsTopupTransaction") {
      await TransactionService._markPendingDepositSavingsTopupAsPending(
        transaction as SavingsTopupTransactionDocument
      );
    }
  }

  private static async _markPendingDepositAssetTransactionAsDepositFailed(
    transaction: AssetTransactionDocument
  ): Promise<void> {
    const [investmentProductsDict, updatedAssetTransaction] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      AssetTransaction.findByIdAndUpdate(
        transaction._id,
        {
          status: "DepositFailed"
        },
        {
          runValidators: true,
          new: true
        }
      ).populate("owner orders")
    ]);

    logger.info(
      `Updated status of asset transaction ${updatedAssetTransaction._id} from PendingDeposit to DepositFailed`,
      {
        module: "TransactionService",
        method: "_markPendingDepositAssetTransactionAsDepositFailed"
      }
    );

    await Promise.all([
      ...updatedAssetTransaction.orders.map((order) =>
        OrderService.setOrderStatusToCancelled(
          order,
          updatedAssetTransaction.owner as UserDocument,
          investmentProductsDict[order.isin]
        )
      ),
      TransactionService._cancelCashbackLinkedToAssetTransaction(updatedAssetTransaction)
    ]);
  }

  private static async _markPendingDepositSavingsTopupAsDepositFailed(
    transaction: SavingsTopupTransactionDocument
  ): Promise<void> {
    await SavingsTopupTransaction.findByIdAndUpdate(
      transaction._id,
      {
        status: "DepositFailed"
      },
      {
        new: true,
        runValidators: true
      }
    ).populate("owner");

    logger.info(
      `Updated status of savings topup transaction ${transaction._id} from PendingDeposit to DepositFailed`,
      {
        module: "TransactionService",
        method: "_markPendingDepositSavingsTopupAsDepositFailed"
      }
    );
  }

  private static async _handlePendingDepositFailure(
    pendingDeposit: DepositCashTransactionDocument
  ): Promise<void> {
    const transaction: TransactionDocument = await Transaction.findOne({
      category: { $in: ["AssetTransaction", "SavingsTopupTransaction"] },
      pendingDeposit: pendingDeposit._id,
      status: "PendingDeposit"
    });

    if (transaction.category === "AssetTransaction") {
      await TransactionService._markPendingDepositAssetTransactionAsDepositFailed(
        transaction as AssetTransactionDocument
      );
    } else if (transaction.category === "SavingsTopupTransaction") {
      await TransactionService._markPendingDepositSavingsTopupAsDepositFailed(
        transaction as SavingsTopupTransactionDocument
      );
    }
  }

  /**
   * Creates a savings top-up document if the deposit transaction is for a one-step savings action and it has a
   * completed payment flow.
   * @param transaction
   * @private
   */
  private static async _handleOneStepSavePaymentAuthorized(
    transaction: DepositCashTransactionDocument
  ): Promise<void> {
    const isDepositLinkedToTransaction = await TransactionService._isDepositLinkedToTransaction(transaction);
    if (
      isDepositLinkedToTransaction ||
      transaction.depositAction !== DepositActionEnum.DEPOSIT_AND_SAVE ||
      transaction.linkedAutomation
    ) {
      return;
    }

    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.PORTFOLIO),
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER)
    ]);
    const portfolio = transaction.portfolio as PortfolioDocument;
    const owner = transaction.owner as UserDocument;

    await PortfolioService.topupSavings(
      portfolio,
      DEFAULT_SAVINGS_PRODUCT_CONFIG[owner.companyEntity],
      Decimal.div(transaction.consideration.amount, 100).toNumber(),
      { pendingDeposit: transaction }
    );
  }

  private static _filterTransactionsThatMatchSavingsProduct(
    savingsProductId: savingsUniverseConfig.SavingsProductType
  ): (transaction: TransactionDocument) => boolean {
    return (transaction: TransactionDocument) => {
      if (["SavingsTopupTransaction", "SavingsWithdrawalTransaction"].includes(transaction.category)) {
        const savingsTranfer = transaction as
          | SavingsTopupTransactionDocument
          | SavingsWithdrawalTransactionDocument;
        return savingsTranfer.savingsProduct === savingsProductId;
      } else if (transaction.category === "SavingsDividendTransaction") {
        return (transaction as SavingsDividendTransactionDocument).savingsProduct === savingsProductId;
      } else {
        return false;
      }
    };
  }

  private static _filterOutDepositLinkedToSavingsTopup(transaction: TransactionDocument): boolean {
    if (transaction.category !== "DepositCashTransaction") {
      return true; // Keep the transaction if it's not a deposit
    } else if (
      (transaction as DepositCashTransactionDocument).depositAction !== DepositActionEnum.DEPOSIT_AND_SAVE
    ) {
      return true; // Keep the transaction if there's no linked savings topup transaction
    }

    const linkedSavingsTopupTransaction = (transaction as DepositCashTransactionDocument).linkedSavingsTopup;

    /**
     * Precautionary check. It's not expected for a SavingsTopUp to have a Cancelled or Rejected status.
     * But if this is the case we should not filter the pending deposit.
     */
    const isLinkedTransactionCancelledOrRejected =
      linkedSavingsTopupTransaction?.status === "Cancelled" ||
      linkedSavingsTopupTransaction?.status === "Rejected";

    // If the linked transaction is valid, filter out the transaction. Otherwise, keep it.
    return isLinkedTransactionCancelledOrRejected;
  }

  /**
   *
   * @param portfolio
   * @param wkDividend
   * @param savingsProduct
   * @returns dividend payout with applied fees in cents
   *
   * @description
   * This method calculates savings dividend fees and applies them to payout.
   * It first checks 2 conditions:
   * - The wealthyhood fee isn't less than the minimum estimated fee.
   * - That the monthly accumulation (we store) isn't greater than the dividend payout.
   *
   * Minimum estimated fee = Dividend * (LowestWealthyhoodRate / HighestYieldRate)
   * Wealthyhood fee = Dividend - DailyAccrualSum
   */
  private static async _applyFeesToSavingsDividend(
    portfolio: PortfolioDocument,
    wkDividend: TransactionType,
    savingsProduct: SavingsProductDocument
  ): Promise<{
    dividendPayoutInCents: number;
    dividendAmountPostFeesInCents: number;
    dividendFeeInCents: number;
  }> {
    const originaDividendPayoutInCents = Decimal.mul(wkDividend.consideration.amount, 100);

    const { start, end } = DateUtil.getStartAndEndOfLastMonth();
    const [portfolioSavingsTickerOfLastMonth, savingsProductTickerOfLastMonth] = await Promise.all([
      DailyPortfolioSavingsTicker.find({
        date: { $gte: start, $lte: end },
        portfolio: portfolio.id
      }),
      DailySavingsProductTicker.find({
        date: { $gte: start, $lte: end },
        savingsProduct: savingsProduct.id
      })
    ]);

    const savingsTickers = portfolioSavingsTickerOfLastMonth.filter((ticker) => ticker && ticker.dailyAccrual > 0);
    // Remove any decimals places, as we are working with cents
    const dailyAccrualsSumInCents = savingsTickers
      .reduce((sum, saving) => sum.plus(saving.dailyAccrual), new Decimal(0))
      .toDecimalPlaces(0);

    const smallestWealthyhoodFeeRate = Decimal.min(
      ...savingsTickers.map(({ planFee }) => new Decimal(planFee))
    ).mul(100);

    const highestOneDayYieldRate = Decimal.max(
      ...savingsProductTickerOfLastMonth.map(({ oneDayYield }) => new Decimal(oneDayYield))
    );

    // Remove any decimals places, as we are working with cents
    // We use floor because this is just the minimum estimate
    const estimatedMinimumWealthyhoodFee = new Decimal(originaDividendPayoutInCents)
      .mul(smallestWealthyhoodFeeRate)
      .div(highestOneDayYieldRate)
      .floor();

    let wealthyhoodFee = new Decimal(originaDividendPayoutInCents).minus(dailyAccrualsSumInCents);
    let dividendAmountPostFeesInCents = dailyAccrualsSumInCents;

    // Edge case: If the fee is equal to the original dividend (should only happen for 1 cent original
    // dividend and 1 cent fee), set the fee to 0 and pay out the original dividend to the user
    if (wealthyhoodFee.equals(originaDividendPayoutInCents)) {
      wealthyhoodFee = Decimal(0);
      dividendAmountPostFeesInCents = originaDividendPayoutInCents;
    }

    logger.info(
      `Completed fee calculations for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`,
      {
        module: "TransactionService",
        method: "_calculateSavingsDividendFee",
        data: {
          savingsProductId: savingsProduct.commonId,
          portfolio: portfolio.id,
          dateRange: { start, end },
          originalDividendPayoutInCents: originaDividendPayoutInCents.toNumber(),
          dailyAccrualsSum: dailyAccrualsSumInCents.toNumber(),
          smallestWealthyhoodFeeRate: smallestWealthyhoodFeeRate.toNumber(),
          highestOneDayYieldRate: highestOneDayYieldRate.toNumber(),
          estimatedMinimumWealthyhoodFee: estimatedMinimumWealthyhoodFee.toNumber(),
          wealthyhoodFee: wealthyhoodFee.toNumber(),
          dividendAmountPostFeesInCents: dividendAmountPostFeesInCents.toNumber()
        }
      }
    );

    if (wealthyhoodFee.lt(estimatedMinimumWealthyhoodFee)) {
      logger.warn(
        `Wealthyhood fee is less than the estimated minimum fee for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`,
        {
          module: "TransactionService",
          method: "_calculateSavingsDividendFee",
          data: {
            savingsProductId: savingsProduct.commonId,
            portfolio: portfolio.id,
            estimatedMinimumWealthyhoodFee: estimatedMinimumWealthyhoodFee.toNumber(),
            wealthyhoodFee: wealthyhoodFee.toNumber()
          }
        }
      );
      // throw new Error(
      //   `Wealthyood fee is less than the estimated minimum fee for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`
      // );
    }

    if (dividendAmountPostFeesInCents.gt(originaDividendPayoutInCents)) {
      logger.error(
        `Savings accruals are greater than original payout for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`,
        {
          module: "TransactionService",
          method: "_calculateSavingsDividendFee",
          data: {
            savingsProductId: savingsProduct.commonId,
            portfolio: portfolio.id,
            dailyAccrualsSum: dailyAccrualsSumInCents.toNumber(),
            originalDividendPayoutInCents: originaDividendPayoutInCents.toNumber(),
            dividendAmountPostFeesInCents: dividendAmountPostFeesInCents.toNumber()
          }
        }
      );
      throw new Error(
        `Savings accruals are greater than actual payout for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`
      );
    }

    return {
      dividendPayoutInCents: originaDividendPayoutInCents.toNumber(),
      dividendAmountPostFeesInCents: dividendAmountPostFeesInCents.toNumber(),
      dividendFeeInCents: wealthyhoodFee.toNumber()
    };
  }

  private static _emitSavingsTransactionSuccessEvent(
    user: UserDocument,
    options: {
      savingsProductId: savingsUniverseConfig.SavingsProductType;
      amount: number;
      currency: currenciesConfig.MainCurrencyType;
      side: "buy" | "sell";
    }
  ): void {
    const properties: TrackTransactionInfoType = {
      side: options.side,
      category: "savings",
      assetName: options.savingsProductId,
      amount: options.amount,
      currency: options.currency,
      cashbackAmount: 0,
      fxFees: 0,
      commissionFees: 0,
      executionSpreadFees: 0,
      frequency: "one-off"
    };

    // We should only add OneSignal properties when transaction is not part of deletion request.
    const notificationProperties = {
      side: options.side,
      asset: SAVINGS_PRODUCT_CONFIG_GLOBAL[options.savingsProductId].label,
      /** If user sells etf amount must be shares, otherwise it will be £ cash amount */
      amount: CurrencyUtil.formatCurrency(
        options.amount,
        options.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      )
    };

    // FIXME: this event should not be emitted for savings products
    // The reason why it was initially implemented this way is because we wanted to track savings top-ups as investments on Mixpanel.
    // However, regardless of what we sent to Mixpanel (or other 3rd party tools), we should be emitting the correct event internally.
    // (This means that we may have some code duplication for savings top-ups on the event handler).
    eventEmitter.emit(
      events.transaction.investmentSuccess.eventId,
      user,
      properties,
      {
        type: "assetTransaction"
      },
      notificationProperties
    );
  }

  private static _emitSavingsDividendChargeSuccessEvent(
    user: UserDocument,
    properties: {
      chargedAmount: number;
      originalDividendAmount: number;
    }
  ): void {
    const trackProps: TrackSavingsDividendChargeSuccess = {
      originalDividendAmount: properties.originalDividendAmount,
      chargedAmount: properties.chargedAmount,
      currency: user.currency
    };

    eventEmitter.emit(events.transaction.savingsDividendChargeSuccess.eventId, user, trackProps);
  }

  private static async _createCollectionBankTransferPaymentAndUpdateWithdrawal(
    withdrawal: WithdrawalCashTransactionDocument
  ): Promise<WithdrawalCashTransactionDocument> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.BANK_ACCOUNT)
    ]);
    const user = withdrawal.owner as UserDocument;
    const bankAccount = withdrawal.bankAccount as BankAccountDocument;

    const { id } = await ProviderService.getBankTransferPaymentService(
      user.companyEntity
    ).createBankTransferPayment({
      amount: withdrawal.consideration.amount,
      reference: withdrawal.bankReference,
      linkedTransactionId: withdrawal.id,
      iban: bankAccount.iban,
      recipient: bankAccount.name ?? user.fullName,
      providerData: {
        accountId:
          withdrawal.transferWithIntermediary[TransferWithIntermediaryStageEnum.COLLECTION].incomingPayment
            .providers.devengo.accountId
      }
    });

    return WithdrawalCashTransaction.findByIdAndUpdate(
      withdrawal.id,
      {
        ["transferWithIntermediary.collection.outgoingPayment.providers.devengo"]: {
          id,
          status: "created"
        }
      },
      { new: true }
    );
  }

  /**
   * This method is called when the deposit of a one-step investment (repeating or not) moves from pending to
   * settled. In that case, we want to see what orders have already been cancelled, and add that amount of
   * cash to the user's cash availability.
   * @param orders
   * @param transaction
   * @param options
   * @private
   */
  private static async _returnCashForCancelledOrders(
    orders: OrderDocument[],
    transaction: AssetTransactionDocument,
    options?: {
      session: mongoose.ClientSession;
    }
  ) {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER, {
        session: options?.session
      }),
      DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.PORTFOLIO, {
        session: options?.session
      })
    ]);
    const user = transaction.owner as UserDocument;
    const portfolio = transaction.portfolio as PortfolioDocument;

    // We return cash for any cancelled orders.
    const amountToReturn = orders
      .filter((order) => order.status === "Cancelled")
      .map((order) => order.consideration.originalAmount)
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(100)
      .toNumber();

    await PortfolioService.updateCashAvailability(portfolio.id, user.currency, amountToReturn, {
      session: options?.session,
      available: true,
      settled: true
    });
  }

  private static async _createAutomatedDepositTransaction(
    automation: TopUpAutomationDocument | SavingsTopUpAutomationDocument,
    collectionDate: Date
  ): Promise<DepositCashTransactionDocument> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.MANDATE)
    ]);

    const mandate = automation.mandate as MandateDocument;
    const { companyEntity } = automation.owner as UserDocument;

    let depositAction;
    if (automation.category === "SavingsTopUpAutomation") {
      depositAction = DepositActionEnum.DEPOSIT_AND_SAVE;
    } else if (automation.category === "TopUpAutomation") {
      depositAction = DepositActionEnum.DEPOSIT_AND_INVEST;
    } else {
      throw new Error(
        `Unsupported automation category ${automation.category} in method _createAutomatedDepositTransaction`
      );
    }

    let depositTransactionData: DepositCashTransactionInterfaceDTO;
    if (companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
      depositTransactionData = {
        depositAction,
        depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
        owner: automation.owner._id,
        portfolio: automation.portfolio._id,
        consideration: automation.consideration,
        activeProviders: ProviderService.getProviders(companyEntity, [ProviderScopeEnum.BANK_TRANSFER_PAYMENTS]),
        providers: {},
        linkedAutomation: automation.id,
        bankAccount: mandate.bankAccount._id,
        bankReference: nanoid(),
        createdAt: new Date(),
        status: "Pending",
        createdWhilePendingMandate: mandate?.status === "Pending",
        transferWithIntermediary: {
          collection: {
            incomingPayment: {
              providers: {
                devengo: {
                  accountId: process.env.DEVENGO_GOCARDLESS_PAYOUTS_COLLECTION_ACCOUNT_ID
                }
              }
            }
          }
        },
        directDebit: {
          activeProviders: ProviderService.getProviders(companyEntity, [
            ProviderScopeEnum.DIRECT_DEBIT_DEPOSIT_PAYMENTS
          ]),
          collectionRequestDate: collectionDate,
          providers: {}
        }
      };
    } else if (companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      depositTransactionData = {
        depositAction,
        depositMethod: DepositMethodEnum.DIRECT_DEBIT,
        owner: automation.owner._id,
        portfolio: automation.portfolio._id,
        consideration: automation.consideration,
        activeProviders: [],
        providers: {},
        linkedAutomation: automation._id,
        bankAccount: mandate.bankAccount._id,
        createdAt: new Date(),
        status: "Pending",
        createdWhilePendingMandate: mandate?.status === "Pending",
        directDebit: {
          activeProviders: ProviderService.getProviders(companyEntity, [
            ProviderScopeEnum.DIRECT_DEBIT_DEPOSIT_PAYMENTS
          ]),
          collectionRequestDate: collectionDate,
          providers: {}
        }
      };
    } else {
      throw new Error(`Unsupported company entity ${companyEntity}`);
    }

    return new DepositCashTransaction(depositTransactionData).save();
  }

  private static async _aggregateLowAmountSavingsDividends(dividendId: string) {
    return DbUtil.runInSession<SavingsDividendTransactionDocument>(async (session: mongoose.ClientSession) => {
      const dividend = await SavingsDividendTransaction.findById(dividendId, null, { session }).populate({
        path: "portfolio",
        options: { session }
      });
      const portfolio = dividend.portfolio as PortfolioDocument;

      const pendingSavingsDividends = await SavingsDividendTransaction.find(
        {
          status: "PendingReinvestment",
          portfolio: portfolio.id,
          "consideration.amount": { $lt: Decimal.mul(SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT, 100).toNumber() }
        },
        null,
        { session }
      ).populate({ path: "linkedSavingsTopup", options: { session } });

      if (pendingSavingsDividends.length === 0) {
        return dividend;
      }

      const totalDividendAmount = pendingSavingsDividends
        .map(({ consideration }) => consideration.amount)
        .reduce((sum, value) => sum.plus(value), new Decimal(0))
        .add(dividend.consideration.amount)
        .toNumber();
      const totalDividendFees = aggregateFees(
        [dividend.fees, ...pendingSavingsDividends.map(({ fees }) => fees)],
        portfolio.currency
      );

      const [updatedSavingsDividend] = await Promise.all([
        SavingsDividendTransaction.findByIdAndUpdate(
          dividendId,
          {
            "consideration.amount": totalDividendAmount,
            fees: totalDividendFees
          },
          { session, new: true }
        ).populate("portfolio"),
        ...pendingSavingsDividends.flatMap((previouslyPendingDividend) => [
          SavingsDividendTransaction.findByIdAndUpdate(
            previouslyPendingDividend.id,
            {
              status: "Cancelled",
              cancelledBy: dividendId
            },
            { session }
          ),
          SavingsTopupTransaction.findByIdAndUpdate(
            previouslyPendingDividend.linkedSavingsTopup.id,
            {
              status: "Cancelled"
            },
            { session }
          )
        ])
      ]);

      return updatedSavingsDividend;
    });
  }
}
