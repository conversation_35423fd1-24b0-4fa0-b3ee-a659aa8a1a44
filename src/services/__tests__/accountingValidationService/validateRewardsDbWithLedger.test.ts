import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { AccountingValidationService } from "../../accountingValidationService";
import { buildUser, buildReward } from "../../../tests/utils/generateModels";
import { AccountingEventType, LedgerAccounts } from "../../../types/accounting";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { ProviderEnum } from "../../../configs/providersConfig";

describe("AccountingValidationService.validateRewardsDbWithLedger", () => {
  const LEGACY_DATE = "2025-01-15";
  const MODERN_DATE = "2025-10-05";
  const TODAY = MODERN_DATE;
  const FROMDATE = "2025-01-01";

  beforeAll(async () => {
    await connectDb("validateRewardsDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => {
    await closeDb();
  });

  beforeEach(async () => {
    // Clear all test data
    await clearSqliteDb();
    await clearDb();
  });

  /* ------------------------------------------------------------------
   * Reward Deposit Settlement Tests
   * ------------------------------------------------------------------ */
  describe("Reward Deposit Settlement", () => {
    it("should validate modern reward deposit settlement with matching ledger entries", async () => {
      const REWARD_AMOUNT_CENTS = 500_000; // €5,000.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber(); // 5000 euros

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: REWARD_AMOUNT_CENTS, // For deposit-only test, bonusAmount = amount
          orderAmount: REWARD_AMOUNT_CENTS
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        createdAt: new Date(MODERN_DATE),
        updatedAt: new Date(MODERN_DATE)
      });

      // Create matching ledger entries for deposit settlement
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_ACCOUNT,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      expect(results).toHaveLength(2);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_deposit",
          isValid: true,
          dbTotalAmount: REWARD_AMOUNT_EUROS,
          ledgerTotalAmount: REWARD_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 4
        })
      );
      expect(depositResult!.discrepancies).toBeUndefined();
    });

    it("should validate legacy reward deposit settlement with matching ledger entries", async () => {
      const REWARD_AMOUNT_CENTS = 200_000; // €2,000.00
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: REWARD_AMOUNT_CENTS,
          orderAmount: REWARD_AMOUNT_CENTS
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-legacy",
              status: "Settled",
              submittedAt: new Date(LEGACY_DATE)
            }
          }
        },
        createdAt: new Date(LEGACY_DATE),
        updatedAt: new Date(LEGACY_DATE)
      });

      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: LEGACY_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: LEGACY_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_deposit",
          isValid: true,
          dbTotalAmount: REWARD_AMOUNT_EUROS,
          ledgerTotalAmount: REWARD_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 2
        })
      );

      expect(depositResult!.discrepancies).toBeUndefined();
    });

    it("should detect discrepancies in modern reward deposit settlement", async () => {
      const REWARD_AMOUNT_CENTS = 300_000; // €3,000.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();
      const LEDGER_AMOUNT_EUROS = 2500; // Different amount in ledger

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: REWARD_AMOUNT_CENTS, // For deposit-only test, bonusAmount = amount
          orderAmount: REWARD_AMOUNT_CENTS
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(MODERN_DATE)
            }
          }
        },
        createdAt: new Date(MODERN_DATE),
        updatedAt: new Date(MODERN_DATE)
      });

      // Create mismatched ledger entries
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "debit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_ACCOUNT,
          side: "credit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toBeDefined();
      expect(depositResult!.isValid).toBe(false);
      expect(depositResult!.dbTotalAmount).toBe(REWARD_AMOUNT_EUROS);
      expect(depositResult!.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
      expect(depositResult!.difference).toBe(REWARD_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
      expect(depositResult!.discrepancies).toHaveLength(4);
      expect(depositResult!.discrepancies![0].transactionId).toBe(reward.id);
      expect(depositResult!.discrepancies![0].difference).toBeCloseTo(REWARD_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
    });

    it("should handle multiple reward deposits across legacy and modern rules", async () => {
      const legacyAmountCents = 2000 * 100;
      const modernAmountCents = 3500 * 100;
      const totalAmountEuros = Decimal.div(legacyAmountCents + modernAmountCents, 100).toNumber();

      const legacyUser = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const modernUser = await buildUser({
        residencyCountry: "FR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      const legacyReward = await buildReward({
        targetUser: legacyUser.id,
        consideration: {
          amount: legacyAmountCents,
          currency: "EUR",
          bonusAmount: legacyAmountCents,
          orderAmount: legacyAmountCents
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-legacy",
              status: "Settled",
              submittedAt: new Date(LEGACY_DATE)
            }
          }
        },
        createdAt: new Date(LEGACY_DATE),
        updatedAt: new Date(LEGACY_DATE)
      });

      const modernReward = await buildReward({
        targetUser: modernUser.id,
        consideration: {
          amount: modernAmountCents,
          currency: "EUR",
          bonusAmount: modernAmountCents,
          orderAmount: modernAmountCents
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-modern",
              status: "Settled",
              submittedAt: new Date(MODERN_DATE)
            }
          }
        },
        createdAt: new Date(MODERN_DATE),
        updatedAt: new Date(MODERN_DATE)
      });

      const legacyAmountEuros = Decimal.div(legacyAmountCents, 100).toNumber();
      const modernAmountEuros = Decimal.div(modernAmountCents, 100).toNumber();

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: legacyAmountEuros,
          description: `${legacyUser.id}|${legacyReward.id}|${AccountingEventType.BONUS}`,
          article_date: LEGACY_DATE,
          reference_number: undefined,
          document_id: legacyReward.id,
          owner_id: legacyUser.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: legacyAmountEuros,
          description: `${legacyUser.id}|${legacyReward.id}|${AccountingEventType.BONUS}`,
          article_date: LEGACY_DATE,
          reference_number: undefined,
          document_id: legacyReward.id,
          owner_id: legacyUser.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_EU_EEA,
          side: "credit",
          amount: modernAmountEuros,
          description: `${modernUser.id}|${modernReward.id}|${AccountingEventType.BONUS}`,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: modernReward.id,
          owner_id: modernUser.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "debit",
          amount: modernAmountEuros,
          description: `${modernUser.id}|${modernReward.id}|${AccountingEventType.BONUS}`,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: modernReward.id,
          owner_id: modernUser.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.BONUS_ACCOUNT,
          side: "credit",
          amount: modernAmountEuros,
          description: `${modernUser.id}|${modernReward.id}|${AccountingEventType.BONUS}`,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: modernReward.id,
          owner_id: modernUser.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: modernAmountEuros,
          description: `${modernUser.id}|${modernReward.id}|${AccountingEventType.BONUS}`,
          article_date: MODERN_DATE,
          reference_number: undefined,
          document_id: modernReward.id,
          owner_id: modernUser.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");

      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_deposit",
          isValid: true,
          dbTotalAmount: totalAmountEuros,
          ledgerTotalAmount: totalAmountEuros,
          difference: 0,
          transactionCount: 2,
          ledgerEntryCount: 6
        })
      );
      expect(depositResult!.discrepancies).toBeUndefined();
    });
  });

  /* ------------------------------------------------------------------
   * Reward Order Settlement Tests
   * ------------------------------------------------------------------ */
  describe("Reward Order Settlement", () => {
    it("should validate reward order settlement with fees", async () => {
      const REWARD_AMOUNT_CENTS = 400_000; // €4,000.00 in cents
      const FX_FEE_AMOUNT_EUROS = 20; // €20.00 FX fee
      const FX_FEE_AMOUNT_CENTS = Decimal.mul(FX_FEE_AMOUNT_EUROS, 100).toNumber();
      const TOTAL_CLIENT_IMPACT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS + FX_FEE_AMOUNT_CENTS, 100).toNumber();

      // Create European user (domestic client)
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Calculate bonusAmount and orderAmount
      const BONUS_AMOUNT_CENTS = Decimal.add(REWARD_AMOUNT_CENTS, FX_FEE_AMOUNT_CENTS).toNumber();

      // Create reward with settled order
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: BONUS_AMOUNT_CENTS,
          orderAmount: REWARD_AMOUNT_CENTS
        },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-1",
              status: "Matched",
              submittedAt: new Date(TODAY)
            }
          }
        },
        status: "Settled",
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries for order settlement
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      const netAmount = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Client account debits (net settlement + FX fee)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: netAmount,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: FX_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: reward.id,
          owner_id: user.id
        },
        // Omnibus credit (net order amount only - fees are charged separately)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: netAmount,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: reward.id,
          owner_id: user.id
        },
        // Commission revenue credit (FX fee)
        {
          aa: 1,
          account_code: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: FX_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const orderResult = results.find((r) => r.transactionType === "rewards_order");
      expect(orderResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_order",
          isValid: true,
          dbTotalAmount: TOTAL_CLIENT_IMPACT_EUROS,
          ledgerTotalAmount: TOTAL_CLIENT_IMPACT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 4 // 2 client debits + 1 omnibus credit + 1 commission credit
        })
      );
      expect(orderResult!.discrepancies).toBeUndefined();
    });

    it("should validate reward order settlement with broker fees", async () => {
      const REWARD_AMOUNT_CENTS = 500_000; // €5,000.00 in cents
      const FX_FEE_AMOUNT_EUROS = 25; // €25.00 FX fee
      const BROKER_FEE_AMOUNT_EUROS = 5; // €5.00 broker fee
      const FX_FEE_AMOUNT_CENTS = Decimal.mul(FX_FEE_AMOUNT_EUROS, 100).toNumber();
      const BROKER_FEE_AMOUNT_CENTS = Decimal.mul(BROKER_FEE_AMOUNT_EUROS, 100).toNumber();

      // Total client impact = net order amount + total commission
      const NET_ORDER_AMOUNT_CENTS = REWARD_AMOUNT_CENTS - BROKER_FEE_AMOUNT_CENTS;
      const TOTAL_CLIENT_IMPACT_EUROS = Decimal.div(
        NET_ORDER_AMOUNT_CENTS + FX_FEE_AMOUNT_CENTS + BROKER_FEE_AMOUNT_CENTS,
        100
      ).toNumber();
      const NET_ORDER_AMOUNT_EUROS = Decimal.div(NET_ORDER_AMOUNT_CENTS, 100).toNumber();

      // Create European user (domestic client)
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Calculate bonusAmount and orderAmount
      const BONUS_AMOUNT_CENTS = Decimal.add(REWARD_AMOUNT_CENTS, FX_FEE_AMOUNT_CENTS).toNumber();

      // Create reward with settled order and broker fee
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          currency: "EUR",
          bonusAmount: BONUS_AMOUNT_CENTS,
          orderAmount: REWARD_AMOUNT_CENTS
        },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-broker-fee",
              status: "Matched",
              submittedAt: new Date(TODAY),
              accountingBrokerFxFee: BROKER_FEE_AMOUNT_EUROS
            }
          }
        },
        status: "Settled",
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries for order settlement
      const rewardId = reward._id.toString();
      const description = `${user.id}|${rewardId}|${AccountingEventType.BONUS}`;

      // Create separate balanced transactions for movements, revenues, and expenses
      // (matching how the production AccountingService creates them)

      // 1. Movement entries (aa: 1) - Asset movements and net order amount
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Client debit for net order amount
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: NET_ORDER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        },
        // Omnibus credit for net order amount
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: NET_ORDER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        },
        // Asset movements
        {
          aa: 1,
          account_code: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: NET_ORDER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: NET_ORDER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        }
      ]);

      // 2. Revenue entries (aa: 2) - Commission revenue with reference number
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: FX_FEE_AMOUNT_EUROS + BROKER_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: rewardId,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: FX_FEE_AMOUNT_EUROS + BROKER_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: rewardId,
          owner_id: user.id
        }
      ]);

      // 3. Expense entries (aa: 3) - Broker fee expense and omnibus credit
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 3,
          account_code: LedgerAccounts.BROKER_FEE_EXPENSE,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        },
        {
          aa: 3,
          account_code: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        },
        {
          aa: 3,
          account_code: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        },
        {
          aa: 3,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          document_id: rewardId,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const orderResult = results.find((r) => r.transactionType === "rewards_order");
      expect(orderResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_order",
          isValid: true,
          dbTotalAmount: TOTAL_CLIENT_IMPACT_EUROS,
          ledgerTotalAmount: TOTAL_CLIENT_IMPACT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 5 // 2 client debits + 2 omnibus credits + 1 commission credit
        })
      );
      expect(orderResult!.discrepancies).toBeUndefined();
    });

    it("should validate reward order settlement without fees", async () => {
      const REWARD_AMOUNT_CENTS = 250_000; // €2,500.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();

      // Create European user (EU/EEA client)
      const user = await buildUser({
        residencyCountry: "DE",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled order and no fees
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          amount: REWARD_AMOUNT_CENTS,
          bonusAmount: REWARD_AMOUNT_CENTS,
          orderAmount: REWARD_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-2",
              status: "Matched",
              submittedAt: new Date(TODAY)
            }
          }
        },
        status: "Settled",
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries for order settlement
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Client account debit (net settlement only)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_EU_EEA,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined, // Movement entries don't have invoice references
          document_id: reward.id,
          owner_id: user.id
        },
        // Omnibus credit
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined, // Movement entries don't have invoice references
          document_id: reward.id,
          owner_id: user.id
        },
        // Revenue entries (even with 0 commission) - always generated to ensure invoice reference
        // Note: These are NOT counted in ledgerEntryCount by the validation service
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_EU_EEA,
          side: "debit",
          amount: 0, // Zero commission
          description,
          article_date: TODAY,
          reference_number: "INV-002",
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: 0, // Zero commission
          description,
          article_date: TODAY,
          reference_number: "INV-002",
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const orderResult = results.find((r) => r.transactionType === "rewards_order");
      expect(orderResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_order",
          isValid: true,
          dbTotalAmount: REWARD_AMOUNT_EUROS,
          ledgerTotalAmount: REWARD_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 4 // 1 client debit + 1 omnibus credit + 1 revenue client debit + 1 commission fees credit
        })
      );
      expect(orderResult!.discrepancies).toBeUndefined();
    });

    it("should detect discrepancies in reward order settlement", async () => {
      const REWARD_AMOUNT_CENTS = 600_000; // €6,000.00 in cents
      const FX_FEE_AMOUNT_EUROS = 30; // €30.00 FX fee
      const FX_FEE_AMOUNT_CENTS = Decimal.mul(FX_FEE_AMOUNT_EUROS, 100).toNumber();
      const TOTAL_DB_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS + FX_FEE_AMOUNT_CENTS, 100).toNumber();
      const LEDGER_AMOUNT_EUROS = 5000; // Different amount in ledger

      // Create European user (international client)
      const user = await buildUser({
        residencyCountry: "US",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled order
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          currency: "EUR",
          amount: REWARD_AMOUNT_CENTS,
          bonusAmount: REWARD_AMOUNT_CENTS,
          orderAmount: REWARD_AMOUNT_CENTS
        },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-3",
              status: "Matched",
              submittedAt: new Date(TODAY)
            }
          }
        },
        status: "Settled",
        updatedAt: new Date(TODAY)
      });

      // Create mismatched ledger entries
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Client account debit (wrong amount)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_INTERNATIONAL,
          side: "debit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-003",
          document_id: reward.id,
          owner_id: user.id
        },
        // Omnibus credit (matching the wrong client debit)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-003",
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const orderResult = results.find((r) => r.transactionType === "rewards_order");
      expect(orderResult).toEqual(
        expect.objectContaining({
          isValid: false,
          dbTotalAmount: TOTAL_DB_AMOUNT_EUROS,
          ledgerTotalAmount: LEDGER_AMOUNT_EUROS,
          difference: Decimal.sub(TOTAL_DB_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber(),
          transactionCount: 1,
          ledgerEntryCount: 2,
          discrepancies: expect.arrayContaining([
            expect.objectContaining({
              transactionId: reward.id,
              difference: Decimal.sub(TOTAL_DB_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber()
            })
          ])
        })
      );
    });
  });

  /* ------------------------------------------------------------------
   * Edge Cases and Error Handling
   * ------------------------------------------------------------------ */
  describe("Edge Cases", () => {
    it("should handle rewards with no matching ledger entries", async () => {
      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit but no ledger entries
      await buildReward({
        targetUser: user.id,
        consideration: {
          currency: "EUR",
          amount: 100_000,
          bonusAmount: 100_000,
          orderAmount: 100_000
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toBeDefined();
      expect(depositResult!.isValid).toBe(false);
      expect(depositResult!.dbTotalAmount).toBe(1000); // €1,000.00
      expect(depositResult!.ledgerTotalAmount).toBe(0);
      expect(depositResult!.difference).toBe(1000);
      expect(depositResult!.transactionCount).toBe(1);
      expect(depositResult!.ledgerEntryCount).toBe(0);
    });

    it("should filter rewards by date correctly", async () => {
      const OLD_DATE = "2024-12-01";

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create old reward (before fromDate)
      await buildReward({
        targetUser: user.id,
        consideration: {
          currency: "EUR",
          amount: 100_000,
          bonusAmount: 100_000,
          orderAmount: 100_000
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-old",
              status: "Settled",
              submittedAt: new Date(OLD_DATE)
            }
          }
        },
        updatedAt: new Date(OLD_DATE)
      });

      // Create new reward (after fromDate)
      const newReward = await buildReward({
        targetUser: user.id,
        consideration: {
          currency: "EUR",
          amount: 200_000,
          bonusAmount: 200_000,
          orderAmount: 200_000
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-new",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      // Create ledger entries for both rewards
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Old reward entries (should be ignored)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: 1000,
          description: `${user.id}|old-reward-id|${AccountingEventType.BONUS}`,
          article_date: OLD_DATE,
          reference_number: undefined,
          document_id: "old-reward-id",
          owner_id: user.id
        },
        // New reward entries (should be included)
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: 2000,
          description: `${user.id}|${newReward.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: newReward.id,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: 2000,
          description: `${user.id}|${newReward.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: newReward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toBeDefined();
      expect(depositResult!.transactionCount).toBe(1); // Only new reward should be counted
      expect(depositResult!.dbTotalAmount).toBe(2000); // Only €2,000.00 from new reward
    });
  });

  /* ------------------------------------------------------------------
   * Integration with validateAllDbWithLedger
   * ------------------------------------------------------------------ */
  describe("Integration", () => {
    it("should include rewards validation in validateAllDbWithLedger", async () => {
      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit
      const reward = await buildReward({
        targetUser: user.id,
        consideration: {
          currency: "EUR",
          amount: 100_000,
          bonusAmount: 100_000,
          orderAmount: 100_000
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: 1000,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_ACCOUNT,
          side: "credit",
          amount: 1000,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "debit",
          amount: 1000,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: 1000,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const allResults = await AccountingValidationService.validateAllDbWithLedger(FROMDATE);

      // Should include rewards results alongside other validation results
      const rewardsResults = allResults.rewards;
      expect(rewardsResults).toHaveLength(2);

      const depositResult = rewardsResults.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult!.isValid).toBe(true);
      expect(depositResult!.dbTotalAmount).toBe(1000);
    });
  });
});
