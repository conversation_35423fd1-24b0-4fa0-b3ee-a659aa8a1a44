import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildRebalanceTransaction,
  buildUser,
  buildPortfolio,
  buildOrder
} from "../../../tests/utils/generateModels";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { LedgerAccounts, AccountingEventType } from "../../../types/accounting";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";

describe("AccountingValidationService.validateOrdersDbWithLedger", () => {
  const TODAY = "2025-10-01"; // After BROKER_EXPENSE_REFERENCE_CHECK_START_DATE (2025-09-27)

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("validateOrdersDbWithLedger");
    await createSqliteDb();
  });

  it("should fail validation when expense references do not reuse revenue invoice suffix", async () => {
    const FX_FEE_CENTS = 50;
    const REALTIME_FEE_CENTS = 100;
    const BROKER_FEE_CENTS = 100;
    const TOTAL_COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS + BROKER_FEE_CENTS; // €2.50 total commission

    const GROSS_AMOUNT_CENTS = 98_000; // €980.00 gross amount (includes broker fee)
    const NET_AMOUNT_CENTS = GROSS_AMOUNT_CENTS - BROKER_FEE_CENTS; // €979.00 net

    const NET_AMOUNT_EUROS = new Decimal(NET_AMOUNT_CENTS).div(100).toNumber();
    const TOTAL_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();
    const BROKER_FEE_EUROS = new Decimal(BROKER_FEE_CENTS).div(100).toNumber();

    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    const assetTransaction = await buildAssetTransaction({
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY)
    });

    const assetOrder = await buildOrder({
      transaction: assetTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      createdAt: new Date(TODAY), // Ensure createdAt is after BROKER_EXPENSE_REFERENCE_CHECK_START_DATE
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-asset-order-invalid-expense",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber()
        }
      }
    });

    assetTransaction.orders = [assetOrder.id];
    await assetTransaction.save();

    const description = `${user.id}|${assetOrder.id}|${AccountingEventType.ASSET_BUY}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Revenues
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description,
        reference_number: "INV-100",
        document_id: assetOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description,
        reference_number: "INV-100",
        document_id: assetOrder.id,
        owner_id: user.id
      },
      // Net settlement
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: assetOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: assetOrder.id,
        owner_id: user.id
      },
      // Expenses with missing suffix (should be flagged)
      {
        aa: 2,
        account_code: LedgerAccounts.BROKER_FEE_EXPENSE,
        side: "debit",
        amount: BROKER_FEE_EUROS,
        article_date: TODAY,
        description,
        reference_number: "INV-100",
        document_id: assetOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "credit",
        amount: BROKER_FEE_EUROS,
        article_date: TODAY,
        description,
        reference_number: "INV-100",
        document_id: assetOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "debit",
        amount: BROKER_FEE_EUROS,
        article_date: TODAY,
        description,
        reference_number: "INV-100",
        document_id: assetOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: BROKER_FEE_EUROS,
        article_date: TODAY,
        description,
        reference_number: "INV-100",
        document_id: assetOrder.id,
        owner_id: user.id
      }
    ]);

    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    const assetBuyResult = results.find((result) => result.transactionType === "asset_buy");
    expect(assetBuyResult).toBeDefined();
    expect(assetBuyResult?.isValid).toBe(false);
    expect(assetBuyResult?.discrepancies).toBeDefined();
    const [discrepancy] = assetBuyResult!.discrepancies!;
    expect(discrepancy.description).toContain("Expense entries missing or incorrect reference numbers");
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should pass validation for all order types with commission and broker fees", async () => {
    // Updated fees: realtime 1 euro, fx 0.50 euro
    const FX_FEE_CENTS = 50; // €0.50 fx fee
    const REALTIME_FEE_CENTS = 100; // €1.00 realtime execution fee
    const COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS; // €1.50 total commission

    // Order amounts - consideration.amount includes broker fee, so we need to account for that
    const BROKER_FEE_CENTS = 100; // €1.00 broker fee (example)

    // Gross amounts (what goes in consideration.amount - includes broker fee)
    const ASSET_BUY_GROSS_CENTS = 980_00; // €980.00 gross amount (includes broker fee)
    const ASSET_SELL_GROSS_CENTS = 730_00; // €730.00 gross amount (includes broker fee)
    const MMF_BUY_GROSS_CENTS = 485_00; // €485.00 gross amount (includes broker fee)
    const MMF_SELL_GROSS_CENTS = 285_00; // €285.00 gross amount (includes broker fee)

    // Net amounts (after subtracting broker fee)
    const ASSET_BUY_NET_CENTS = ASSET_BUY_GROSS_CENTS - BROKER_FEE_CENTS; // €979.00 net
    const ASSET_SELL_NET_CENTS = ASSET_SELL_GROSS_CENTS - BROKER_FEE_CENTS; // €729.00 net
    const MMF_BUY_NET_CENTS = MMF_BUY_GROSS_CENTS - BROKER_FEE_CENTS; // €484.00 net
    const MMF_SELL_NET_CENTS = MMF_SELL_GROSS_CENTS - BROKER_FEE_CENTS; // €284.00 net

    // Total commission (WH commission + broker fee)
    const TOTAL_COMMISSION_CENTS = COMMISSION_CENTS + BROKER_FEE_CENTS; // €2.50 total commission

    // Expected validation amounts (net + total commission)
    const ASSET_BUY_EXPECTED_CENTS = ASSET_BUY_NET_CENTS + TOTAL_COMMISSION_CENTS; // €981.50
    const ASSET_SELL_EXPECTED_CENTS = ASSET_SELL_NET_CENTS + TOTAL_COMMISSION_CENTS; // €731.50
    const MMF_BUY_EXPECTED_CENTS = MMF_BUY_NET_CENTS + TOTAL_COMMISSION_CENTS; // €486.50
    const MMF_SELL_EXPECTED_CENTS = MMF_SELL_NET_CENTS + TOTAL_COMMISSION_CENTS; // €286.50

    // Convert to euros for ledger entries
    const ASSET_BUY_EXPECTED_EUROS = new Decimal(ASSET_BUY_EXPECTED_CENTS).div(100).toNumber();
    const ASSET_BUY_NET_EUROS = new Decimal(ASSET_BUY_NET_CENTS).div(100).toNumber();
    const ASSET_BUY_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();

    const ASSET_SELL_EXPECTED_EUROS = new Decimal(ASSET_SELL_EXPECTED_CENTS).div(100).toNumber();
    const ASSET_SELL_NET_EUROS = new Decimal(ASSET_SELL_NET_CENTS).div(100).toNumber();
    const ASSET_SELL_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();

    const MMF_BUY_EXPECTED_EUROS = new Decimal(MMF_BUY_EXPECTED_CENTS).div(100).toNumber();
    const MMF_BUY_NET_EUROS = new Decimal(MMF_BUY_NET_CENTS).div(100).toNumber();
    const MMF_BUY_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();

    const MMF_SELL_EXPECTED_EUROS = new Decimal(MMF_SELL_EXPECTED_CENTS).div(100).toNumber();
    const MMF_SELL_NET_EUROS = new Decimal(MMF_SELL_NET_CENTS).div(100).toNumber();
    const MMF_SELL_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();

    // Create domestic user (GR) => client ledger 30-00-00-0000
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Asset Buy Transaction (AssetTransaction)
    const assetBuyTransaction = await buildAssetTransaction({
      consideration: { currency: "EUR", amount: ASSET_BUY_NET_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY)
    });

    // Create Asset Sell Transaction (AssetTransaction)
    const assetSellTransaction = await buildAssetTransaction({
      consideration: { currency: "EUR", amount: ASSET_SELL_NET_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY),
      portfolioTransactionCategory: "sell"
    });

    // Create MMF Buy Transaction (SavingsTopupTransaction) - no orders initially
    const mmfBuyTransaction = await buildSavingsTopup(
      {
        consideration: { currency: "EUR", amount: MMF_BUY_NET_CENTS },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled",
        settledAt: new Date(TODAY),
        savingsProduct: "mmf_dist_eur"
      },
      {},
      false
    ); // withOrder = false

    // Create MMF Sell Transaction (SavingsWithdrawalTransaction) - no orders initially
    const mmfSellTransaction = await buildSavingsWithdrawal(
      {
        consideration: { currency: "EUR", amount: MMF_SELL_NET_CENTS },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled",
        settledAt: new Date(TODAY),
        savingsProduct: "mmf_dist_eur"
      },
      {},
      false
    ); // withOrder = false

    // Now create orders for the asset transactions with proper filledAt dates
    const assetBuyOrder = await buildOrder({
      transaction: assetBuyTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: ASSET_BUY_NET_CENTS,
        amountSubmitted: ASSET_BUY_NET_CENTS,
        amount: ASSET_BUY_GROSS_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-asset-buy-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    const assetSellOrder = await buildOrder({
      transaction: assetSellTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Sell",
      consideration: {
        currency: "EUR",
        originalAmount: ASSET_SELL_NET_CENTS,
        amountSubmitted: ASSET_SELL_NET_CENTS,
        amount: ASSET_SELL_GROSS_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-asset-sell-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    // Set orders field for asset transactions
    assetBuyTransaction.orders = [assetBuyOrder.id];
    await assetBuyTransaction.save();

    assetSellTransaction.orders = [assetSellOrder.id];
    await assetSellTransaction.save();

    // Create orders for MMF transactions separately
    const mmfBuyOrder = await buildOrder({
      transaction: mmfBuyTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: MMF_BUY_NET_CENTS,
        amountSubmitted: MMF_BUY_NET_CENTS,
        amount: MMF_BUY_GROSS_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-mmf-buy-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    const mmfSellOrder = await buildOrder({
      transaction: mmfSellTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Sell",
      consideration: {
        currency: "EUR",
        originalAmount: MMF_SELL_NET_CENTS,
        amountSubmitted: MMF_SELL_NET_CENTS,
        amount: MMF_SELL_GROSS_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-mmf-sell-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    // Create ledger entries following the actual accounting service pattern
    // NOTE: The description should contain the ORDER ID, not transaction ID, for validation to work
    const assetBuyDescription = `${user.id}|${assetBuyOrder.id}|${AccountingEventType.ASSET_BUY}`;
    const assetSellDescription = `${user.id}|${assetSellOrder.id}|${AccountingEventType.ASSET_SELL}`;
    const mmfBuyDescription = `${user.id}|${mmfBuyOrder.id}|${AccountingEventType.ASSET_BUY}`;
    const mmfSellDescription = `${user.id}|${mmfSellOrder.id}|${AccountingEventType.ASSET_SELL}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Asset Buy:
      // 1. Commission Fee (Client -> Commission Fees)
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: ASSET_BUY_COMMISSION_EUROS,
        article_date: TODAY,
        description: assetBuyDescription,
        reference_number: "INV-001",
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: ASSET_BUY_COMMISSION_EUROS,
        article_date: TODAY,
        description: assetBuyDescription,
        reference_number: "INV-001",
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      // 2. Net Settlement (Client -> Omnibus)
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: ASSET_BUY_NET_EUROS,
        article_date: TODAY,
        description: assetBuyDescription,
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: ASSET_BUY_NET_EUROS,
        article_date: TODAY,
        description: assetBuyDescription,
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      // 3. Expenses (Broker fee)
      {
        aa: 5,
        account_code: LedgerAccounts.BROKER_FEE_EXPENSE,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetBuyDescription,
        reference_number: "INV-001E",
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 5,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetBuyDescription,
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 5,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetBuyDescription,
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 5,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetBuyDescription,
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },

      // Asset Sell:
      // 1. Net Settlement (Omnibus -> Client)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: ASSET_SELL_NET_EUROS,
        article_date: TODAY,
        description: assetSellDescription,
        document_id: assetSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: ASSET_SELL_NET_EUROS,
        article_date: TODAY,
        description: assetSellDescription,
        document_id: assetSellOrder.id,
        owner_id: user.id
      },
      // 2. Commission Fee (Client -> Commission Fees)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: ASSET_SELL_COMMISSION_EUROS,
        article_date: TODAY,
        description: assetSellDescription,
        reference_number: "INV-002",
        document_id: assetSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: ASSET_SELL_COMMISSION_EUROS,
        article_date: TODAY,
        description: assetSellDescription,
        reference_number: "INV-002",
        document_id: assetSellOrder.id,
        owner_id: user.id
      },
      // 3. Expenses (Broker fee)
      {
        aa: 6,
        account_code: LedgerAccounts.BROKER_FEE_EXPENSE,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetSellDescription,
        reference_number: "INV-002E",
        document_id: assetSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 6,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetSellDescription,
        document_id: assetSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 6,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetSellDescription,
        document_id: assetSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 6,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: assetSellDescription,
        document_id: assetSellOrder.id,
        owner_id: user.id
      },

      // MMF Buy:
      // 1. Commission Fee (Client -> Commission Fees)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: MMF_BUY_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        reference_number: "INV-003",
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: MMF_BUY_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        reference_number: "INV-003",
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      // 2. Net Settlement (Client -> Omnibus)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: MMF_BUY_NET_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: MMF_BUY_NET_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      // 3. Expenses (Broker fee)
      {
        aa: 7,
        account_code: LedgerAccounts.BROKER_FEE_EXPENSE,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfBuyDescription,
        reference_number: "INV-003E",
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 7,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfBuyDescription,
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 7,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfBuyDescription,
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 7,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfBuyDescription,
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },

      // MMF Sell:
      // 1. Net Settlement (Omnibus -> Client)
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: MMF_SELL_NET_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: MMF_SELL_NET_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      // 2. Commission Fee (Client -> Commission Fees)
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: MMF_SELL_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        reference_number: "INV-004",
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 4,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: MMF_SELL_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        reference_number: "INV-004",
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      // 3. Expenses (Broker fee)
      {
        aa: 8,
        account_code: LedgerAccounts.BROKER_FEE_EXPENSE,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfSellDescription,
        reference_number: "INV-004E",
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 8,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfSellDescription,
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 8,
        account_code: LedgerAccounts.PAYABLES_TO_BROKER,
        side: "debit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfSellDescription,
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 8,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: BROKER_FEE_CENTS / 100,
        article_date: TODAY,
        description: mmfSellDescription,
        document_id: mmfSellOrder.id,
        owner_id: user.id
      }
    ]);

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // Asset Buy validation - total client debits should equal order consideration.amount
    expect(assetBuyResult).toEqual({
      isValid: true,
      ledgerEntryCount: 8, // 2 commission + 2 net settlement + 4 expenses
      transactionType: "asset_buy",
      dbTotalAmount: ASSET_BUY_EXPECTED_EUROS,
      ledgerTotalAmount: ASSET_BUY_EXPECTED_EUROS, // commission + net debits
      difference: 0,
      transactionCount: 1
    });
    expect(assetBuyResult.discrepancies).toBeUndefined();

    // Asset Sell validation - now counts credits + debits (net proceeds + commission)
    expect(assetSellResult).toEqual({
      isValid: true, // Should pass now that we count both credits and debits
      ledgerEntryCount: 8, // 2 net settlement + 2 commission + 4 expenses
      transactionType: "asset_sell",
      dbTotalAmount: ASSET_SELL_EXPECTED_EUROS,
      ledgerTotalAmount: ASSET_SELL_EXPECTED_EUROS, // net credit + commission debit = total
      difference: 0,
      transactionCount: 1
    });
    expect(assetSellResult.discrepancies).toBeUndefined();

    // MMF Buy validation - total client debits should equal order consideration.amount
    expect(mmfBuyResult).toEqual({
      isValid: true,
      ledgerEntryCount: 8, // 2 commission + 2 net settlement + 4 expenses
      transactionType: "mmf_buy",
      dbTotalAmount: MMF_BUY_EXPECTED_EUROS,
      ledgerTotalAmount: MMF_BUY_EXPECTED_EUROS, // commission + net debits
      difference: 0,
      transactionCount: 1
    });
    expect(mmfBuyResult.discrepancies).toBeUndefined();

    // MMF Sell validation - now counts credits + debits (net proceeds + commission)
    expect(mmfSellResult).toEqual({
      isValid: true, // Should pass now that we count both credits and debits
      ledgerEntryCount: 8, // 2 net settlement + 2 commission + 4 expenses
      transactionType: "mmf_sell",
      dbTotalAmount: MMF_SELL_EXPECTED_EUROS,
      ledgerTotalAmount: MMF_SELL_EXPECTED_EUROS, // net credit + commission debit = total
      difference: 0,
      transactionCount: 1
    });
    expect(mmfSellResult.discrepancies).toBeUndefined();

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should fail validation when asset buy amounts don't match ledger entries", async () => {
    const FX_FEE_CENTS = 50; // €0.50 fx fee
    const REALTIME_FEE_CENTS = 100; // €1.00 realtime execution fee
    const BROKER_FEE_CENTS = 100; // €1.00 broker fee
    const WH_COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS; // €1.50 WH commission
    const TOTAL_COMMISSION_CENTS = WH_COMMISSION_CENTS + BROKER_FEE_CENTS; // €2.50 total commission

    const GROSS_AMOUNT_CENTS = 980_00; // €980.00 gross amount (includes broker fee)
    const NET_AMOUNT_CENTS = GROSS_AMOUNT_CENTS - BROKER_FEE_CENTS; // €979.00 net amount
    const EXPECTED_AMOUNT_CENTS = NET_AMOUNT_CENTS + TOTAL_COMMISSION_CENTS; // €981.50 expected
    const EXPECTED_AMOUNT_EUROS = new Decimal(EXPECTED_AMOUNT_CENTS).div(100).toNumber();
    const LEDGER_AMOUNT_EUROS = 999.5; // Different amount in ledger

    // Create domestic user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Asset Buy Transaction
    const assetBuyTransaction = await buildAssetTransaction({
      portfolioTransactionCategory: "buy",
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY)
    });

    // Create order with fees and broker fee
    const assetBuyOrder = await buildOrder({
      transaction: assetBuyTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    // Set orders field for asset transaction
    assetBuyTransaction.orders = [assetBuyOrder.id];
    await assetBuyTransaction.save();

    // Create ledger entry with different amount - use ORDER ID in description
    const description = `${user.id}|${assetBuyOrder.id}|${AccountingEventType.ASSET_BUY}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      // Add revenue entries to avoid missing revenue entries validation error
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: 0,
        article_date: TODAY,
        description,
        reference_number: "INV-TEST-001",
        document_id: assetBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: 0,
        article_date: TODAY,
        description,
        reference_number: "INV-TEST-001",
        document_id: assetBuyOrder.id,
        owner_id: user.id
      }
    ]);

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // Asset Buy should fail due to amount mismatch
    expect(assetBuyResult.isValid).toBe(false);
    expect(assetBuyResult.transactionType).toBe("asset_buy");
    expect(assetBuyResult.dbTotalAmount).toBe(EXPECTED_AMOUNT_EUROS);
    expect(assetBuyResult.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
    expect(assetBuyResult.difference).toBe(EXPECTED_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
    expect(assetBuyResult.transactionCount).toBe(1);
    expect(assetBuyResult.discrepancies).toEqual([
      {
        transactionId: assetBuyOrder._id.toString(), // Use order ID for discrepancy
        dbAmount: EXPECTED_AMOUNT_EUROS,
        ledgerAmount: LEDGER_AMOUNT_EUROS,
        description: `Order Buy: Amount mismatch (DB=€${EXPECTED_AMOUNT_EUROS} vs Ledger=€${LEDGER_AMOUNT_EUROS})`,
        difference: Decimal.sub(EXPECTED_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber()
      }
    ]);

    // Other transaction types should be valid (no transactions)
    expect(assetSellResult.isValid).toBe(true);
    expect(assetSellResult.transactionCount).toBe(0);
    expect(mmfBuyResult.isValid).toBe(true);
    expect(mmfBuyResult.transactionCount).toBe(0);
    expect(mmfSellResult.isValid).toBe(true);
    expect(mmfSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should handle multiple transactions with different client segments", async () => {
    const FX_FEE_CENTS = 50; // €0.50 fx fee
    const REALTIME_FEE_CENTS = 100; // €1.00 realtime execution fee
    const BROKER_FEE_CENTS = 100; // €1.00 broker fee
    const WH_COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS; // €1.50 WH commission
    const TOTAL_COMMISSION_CENTS = WH_COMMISSION_CENTS + BROKER_FEE_CENTS; // €2.50 total commission

    // Gross amounts (what goes in consideration.amount - includes broker fee)
    const GROSS_AMOUNT_1_CENTS = 500_00; // €500.00 gross amount
    const GROSS_AMOUNT_2_CENTS = 750_00; // €750.00 gross amount

    // Net amounts (after subtracting broker fee)
    const NET_AMOUNT_1_CENTS = GROSS_AMOUNT_1_CENTS - BROKER_FEE_CENTS; // €499.00 net
    const NET_AMOUNT_2_CENTS = GROSS_AMOUNT_2_CENTS - BROKER_FEE_CENTS; // €749.00 net

    // Expected validation amounts (net + total commission)
    const EXPECTED_AMOUNT_1_CENTS = NET_AMOUNT_1_CENTS + TOTAL_COMMISSION_CENTS; // €501.50
    const EXPECTED_AMOUNT_2_CENTS = NET_AMOUNT_2_CENTS + TOTAL_COMMISSION_CENTS; // €751.50

    const EXPECTED_AMOUNT_1_EUROS = new Decimal(EXPECTED_AMOUNT_1_CENTS).div(100).toNumber();
    const EXPECTED_AMOUNT_2_EUROS = new Decimal(EXPECTED_AMOUNT_2_CENTS).div(100).toNumber();

    // Create domestic user (GR)
    const userGR: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioGR: PortfolioDocument = await buildPortfolio({ owner: userGR.id });

    // Create EU user (DE)
    const userEU: UserDocument = await buildUser({
      residencyCountry: "DE",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioEU: PortfolioDocument = await buildPortfolio({ owner: userEU.id });

    // Create Asset Buy Transactions
    const assetBuyGR = await buildAssetTransaction({
      portfolioTransactionCategory: "buy",
      consideration: { currency: "EUR", amount: NET_AMOUNT_1_CENTS },
      portfolio: portfolioGR.id,
      owner: userGR.id,
      status: "Settled",
      settledAt: new Date(TODAY)
    });

    const assetBuyEU = await buildAssetTransaction({
      portfolioTransactionCategory: "buy",
      consideration: { currency: "EUR", amount: NET_AMOUNT_2_CENTS },
      portfolio: portfolioEU.id,
      owner: userEU.id,
      status: "Settled",
      settledAt: new Date(TODAY)
    });

    // Create orders for both transactions with fees and broker fees
    const assetBuyOrderGR = await buildOrder({
      transaction: assetBuyGR.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_1_CENTS,
        amountSubmitted: NET_AMOUNT_1_CENTS,
        amount: GROSS_AMOUNT_1_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-gr-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    const assetBuyOrderEU = await buildOrder({
      transaction: assetBuyEU.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_2_CENTS,
        amountSubmitted: NET_AMOUNT_2_CENTS,
        amount: GROSS_AMOUNT_2_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-eu-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    // Set orders field for both asset transactions
    assetBuyGR.orders = [assetBuyOrderGR.id];
    await assetBuyGR.save();

    assetBuyEU.orders = [assetBuyOrderEU.id];
    await assetBuyEU.save();

    // Create corresponding ledger entries - use ORDER IDs in descriptions
    const descriptionGR = `${userGR.id}|${assetBuyOrderGR.id}|${AccountingEventType.ASSET_BUY}`;
    const descriptionEU = `${userEU.id}|${assetBuyOrderEU.id}|${AccountingEventType.ASSET_BUY}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // GR asset buy entries
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: EXPECTED_AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR,
        document_id: assetBuyOrderGR.id,
        owner_id: userGR.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: EXPECTED_AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR,
        document_id: assetBuyOrderGR.id,
        owner_id: userGR.id
      },
      // GR revenue entries
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: 0,
        article_date: TODAY,
        description: descriptionGR,
        reference_number: "INV-GR-001",
        document_id: assetBuyOrderGR.id,
        owner_id: userGR.id
      },
      {
        aa: 3,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: 0,
        article_date: TODAY,
        description: descriptionGR,
        reference_number: "INV-GR-001",
        document_id: assetBuyOrderGR.id,
        owner_id: userGR.id
      },
      // EU asset buy entries
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_EU_EEA,
        side: "debit",
        amount: EXPECTED_AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU,
        document_id: assetBuyOrderEU.id,
        owner_id: userEU.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: EXPECTED_AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU,
        document_id: assetBuyOrderEU.id,
        owner_id: userEU.id
      },
      // EU revenue entries
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENT_EU_EEA,
        side: "debit",
        amount: 0,
        article_date: TODAY,
        description: descriptionEU,
        reference_number: "INV-EU-001",
        document_id: assetBuyOrderEU.id,
        owner_id: userEU.id
      },
      {
        aa: 4,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: 0,
        article_date: TODAY,
        description: descriptionEU,
        reference_number: "INV-EU-001",
        document_id: assetBuyOrderEU.id,
        owner_id: userEU.id
      }
    ]);

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // Asset Buy should validate both transactions
    expect(assetBuyResult.isValid).toBe(true);
    expect(assetBuyResult.transactionType).toBe("asset_buy");
    expect(assetBuyResult.dbTotalAmount).toBe(EXPECTED_AMOUNT_1_EUROS + EXPECTED_AMOUNT_2_EUROS);
    expect(assetBuyResult.ledgerTotalAmount).toBe(EXPECTED_AMOUNT_1_EUROS + EXPECTED_AMOUNT_2_EUROS);
    expect(assetBuyResult.difference).toBe(0);
    expect(assetBuyResult.transactionCount).toBe(2);

    // Other transaction types should be valid (no transactions)
    expect(assetSellResult.isValid).toBe(true);
    expect(assetSellResult.transactionCount).toBe(0);
    expect(mmfBuyResult.isValid).toBe(true);
    expect(mmfBuyResult.transactionCount).toBe(0);
    expect(mmfSellResult.isValid).toBe(true);
    expect(mmfSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should ignore non-settled transactions", async () => {
    const TRANSACTION_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents

    // Create user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create transactions with various non-settled statuses (all should be ignored)
    await buildAssetTransaction({
      portfolioTransactionCategory: "buy",
      consideration: { currency: "EUR", amount: TRANSACTION_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Pending"
    });

    await buildAssetTransaction({
      portfolioTransactionCategory: "sell",
      consideration: { currency: "EUR", amount: TRANSACTION_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Cancelled"
    });

    await buildSavingsTopup({
      consideration: { currency: "EUR", amount: TRANSACTION_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Pending",
      savingsProduct: "mmf_dist_eur"
    });

    await buildSavingsWithdrawal({
      consideration: { currency: "EUR", amount: TRANSACTION_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Rejected",
      savingsProduct: "mmf_dist_eur"
    });

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // All order types should be valid with no transactions (non-settled transactions are ignored)
    expect(assetBuyResult.isValid).toBe(true);
    expect(assetBuyResult.transactionCount).toBe(0);
    expect(assetBuyResult.dbTotalAmount).toBe(0);
    expect(assetBuyResult.ledgerTotalAmount).toBe(0);

    expect(assetSellResult.isValid).toBe(true);
    expect(assetSellResult.transactionCount).toBe(0);

    expect(mmfBuyResult.isValid).toBe(true);
    expect(mmfBuyResult.transactionCount).toBe(0);

    expect(mmfSellResult.isValid).toBe(true);
    expect(mmfSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should handle validation with no transactions", async () => {
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // All order types should be valid with no transactions
    expect(assetBuyResult.isValid).toBe(true);
    expect(assetBuyResult.transactionCount).toBe(0);
    expect(assetBuyResult.dbTotalAmount).toBe(0);
    expect(assetBuyResult.ledgerTotalAmount).toBe(0);
    expect(assetBuyResult.difference).toBe(0);

    expect(assetSellResult.isValid).toBe(true);
    expect(assetSellResult.transactionCount).toBe(0);

    expect(mmfBuyResult.isValid).toBe(true);
    expect(mmfBuyResult.transactionCount).toBe(0);

    expect(mmfSellResult.isValid).toBe(true);
    expect(mmfSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should handle validation with no ledger entries", async () => {
    const FX_FEE_CENTS = 50; // €0.50 fx fee
    const REALTIME_FEE_CENTS = 100; // €1.00 realtime execution fee
    const BROKER_FEE_CENTS = 100; // €1.00 broker fee
    const WH_COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS; // €1.50 WH commission
    const TOTAL_COMMISSION_CENTS = WH_COMMISSION_CENTS + BROKER_FEE_CENTS; // €2.50 total commission

    const GROSS_AMOUNT_CENTS = 98000; // €980.00 gross amount (includes broker fee)
    const NET_AMOUNT_CENTS = GROSS_AMOUNT_CENTS - BROKER_FEE_CENTS; // €979.00 net amount
    const EXPECTED_AMOUNT_CENTS = NET_AMOUNT_CENTS + TOTAL_COMMISSION_CENTS; // €981.50 expected
    const EXPECTED_AMOUNT_EUROS = new Decimal(EXPECTED_AMOUNT_CENTS).div(100).toNumber();

    // Create user and transactions without ledger entries
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    const assetTransaction = await buildAssetTransaction({
      portfolioTransactionCategory: "buy",
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY)
    });

    const mmfTransaction = await buildSavingsTopup({
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY),
      savingsProduct: "mmf_dist_eur"
    });

    // Create orders for the transactions with broker fees
    const assetOrder = await buildOrder({
      transaction: assetTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-asset-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    await buildOrder({
      transaction: mmfTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-mmf-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    // Set orders field for asset transaction
    assetTransaction.orders = [assetOrder.id];
    await assetTransaction.save();

    // NO ledger entries created

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // Asset Buy should have discrepancy (db amount but no ledger entries)
    expect(assetBuyResult.isValid).toBe(false);
    expect(assetBuyResult.transactionType).toBe("asset_buy");
    expect(assetBuyResult.dbTotalAmount).toBe(EXPECTED_AMOUNT_EUROS);
    expect(assetBuyResult.ledgerTotalAmount).toBe(0);
    expect(assetBuyResult.difference).toBe(EXPECTED_AMOUNT_EUROS);
    expect(assetBuyResult.transactionCount).toBe(1);
    expect(assetBuyResult.discrepancies).toEqual([
      {
        transactionId: assetOrder._id.toString(),
        dbAmount: EXPECTED_AMOUNT_EUROS,
        ledgerAmount: 0,
        description: `Order Buy: Missing revenue entries AND Amount mismatch (DB=€${EXPECTED_AMOUNT_EUROS} vs Ledger=€0)`,
        difference: -999999 // Special marker for missing revenue entries
      }
    ]);

    // MMF Buy should have discrepancy (db amount but no ledger entries)
    expect(mmfBuyResult.isValid).toBe(false);
    expect(mmfBuyResult.transactionType).toBe("mmf_buy");
    expect(mmfBuyResult.dbTotalAmount).toBe(EXPECTED_AMOUNT_EUROS);
    expect(mmfBuyResult.ledgerTotalAmount).toBe(0);
    expect(mmfBuyResult.difference).toBe(EXPECTED_AMOUNT_EUROS);
    expect(mmfBuyResult.transactionCount).toBe(1);

    // Other transaction types should be valid (no transactions)
    expect(assetSellResult.isValid).toBe(true);
    expect(assetSellResult.transactionCount).toBe(0);
    expect(mmfSellResult.isValid).toBe(true);
    expect(mmfSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should validate MMF transactions with correct savings product", async () => {
    const FX_FEE_CENTS = 50; // €0.50 fx fee
    const REALTIME_FEE_CENTS = 100; // €1.00 realtime execution fee
    const BROKER_FEE_CENTS = 100; // €1.00 broker fee
    const WH_COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS; // €1.50 WH commission
    const TOTAL_COMMISSION_CENTS = WH_COMMISSION_CENTS + BROKER_FEE_CENTS; // €2.50 total commission

    const GROSS_AMOUNT_CENTS = 98000; // €980.00 gross amount (includes broker fee)
    const NET_AMOUNT_CENTS = GROSS_AMOUNT_CENTS - BROKER_FEE_CENTS; // €979.00 net amount
    const EXPECTED_AMOUNT_CENTS = NET_AMOUNT_CENTS + TOTAL_COMMISSION_CENTS; // €981.50 expected

    const NET_AMOUNT_EUROS = new Decimal(NET_AMOUNT_CENTS).div(100).toNumber();
    const TOTAL_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();
    const EXPECTED_AMOUNT_EUROS = new Decimal(EXPECTED_AMOUNT_CENTS).div(100).toNumber();

    // Create domestic user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create MMF Buy Transaction with specific savings product
    const mmfBuyTransaction = await buildSavingsTopup({
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY),
      savingsProduct: "mmf_dist_eur"
    });

    // Create MMF Sell Transaction with specific savings product
    const mmfSellTransaction = await buildSavingsWithdrawal({
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY),
      savingsProduct: "mmf_dist_eur"
    });

    // Create orders for MMF transactions with broker fees
    const mmfBuyOrder = await buildOrder({
      transaction: mmfBuyTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-mmf-buy-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    const mmfSellOrder = await buildOrder({
      transaction: mmfSellTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Sell",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-mmf-sell-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    // Create corresponding ledger entries - use ORDER IDs in descriptions
    const mmfBuyDescription = `${user.id}|${mmfBuyOrder.id}|${AccountingEventType.ASSET_BUY}`;
    const mmfSellDescription = `${user.id}|${mmfSellOrder.id}|${AccountingEventType.ASSET_SELL}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // MMF Buy: 1. Commission Fee (Client -> Commission Fees)
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        reference_number: "INV-MMF-001",
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        reference_number: "INV-MMF-001",
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      // MMF Buy: 2. Net Settlement (Client -> Omnibus)
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: mmfBuyDescription,
        document_id: mmfBuyOrder.id,
        owner_id: user.id
      },

      // MMF Sell: 1. Net Settlement (Omnibus -> Client)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      // MMF Sell: 2. Commission Fee (Client -> Commission Fees)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        reference_number: "INV-MMF-002",
        document_id: mmfSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: mmfSellDescription,
        reference_number: "INV-MMF-002",
        document_id: mmfSellOrder.id,
        owner_id: user.id
      }
    ]);

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // MMF transactions should be valid with commission fees
    expect(mmfBuyResult).toEqual({
      isValid: true,
      ledgerEntryCount: 4, // 2 for commission + 2 for net settlement
      transactionType: "mmf_buy",
      dbTotalAmount: EXPECTED_AMOUNT_EUROS,
      ledgerTotalAmount: EXPECTED_AMOUNT_EUROS, // commission + net debits
      difference: 0,
      transactionCount: 1
    });

    expect(mmfSellResult).toEqual({
      isValid: true,
      ledgerEntryCount: 4, // 2 for net settlement + 2 for commission
      transactionType: "mmf_sell",
      dbTotalAmount: EXPECTED_AMOUNT_EUROS,
      ledgerTotalAmount: EXPECTED_AMOUNT_EUROS, // net credit + commission debit = total
      difference: 0,
      transactionCount: 1
    });

    // Asset transaction types should be valid (no transactions)
    expect(assetBuyResult.isValid).toBe(true);
    expect(assetBuyResult.transactionCount).toBe(0);

    expect(assetSellResult.isValid).toBe(true);
    expect(assetSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should validate orders with zero commission (still generates revenue entries)", async () => {
    // Zero commission scenario - should still generate revenue entries with invoice references
    const FX_FEE_CENTS = 0; // €0.00 fx fee
    const REALTIME_FEE_CENTS = 0; // €0.00 realtime execution fee
    const BROKER_FEE_CENTS = 0; // €0.00 broker fee
    const WH_COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS; // €0.00 WH commission
    const TOTAL_COMMISSION_CENTS = WH_COMMISSION_CENTS + BROKER_FEE_CENTS; // €0.00 total commission

    const GROSS_AMOUNT_CENTS = 100000; // €1000.00 gross amount (no broker fee)
    const NET_AMOUNT_CENTS = GROSS_AMOUNT_CENTS - BROKER_FEE_CENTS; // €1000.00 net amount
    const EXPECTED_AMOUNT_CENTS = NET_AMOUNT_CENTS + TOTAL_COMMISSION_CENTS; // €1000.00 expected

    const NET_AMOUNT_EUROS = new Decimal(NET_AMOUNT_CENTS).div(100).toNumber();
    const TOTAL_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();
    const EXPECTED_AMOUNT_EUROS = new Decimal(EXPECTED_AMOUNT_CENTS).div(100).toNumber();

    // Create domestic user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Asset Transaction
    const assetTransaction = await buildAssetTransaction({
      portfolioTransactionCategory: "buy",
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      status: "Settled",
      settledAt: new Date(TODAY)
    });

    // Create order with zero commission
    const assetOrder = await buildOrder({
      transaction: assetTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Same as net since no broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-zero-commission-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Zero broker fee
        }
      }
    });

    // Set orders field for asset transaction
    assetTransaction.orders = [assetOrder.id];
    await assetTransaction.save();

    const description = `${user.id}|${assetOrder.id}|${AccountingEventType.ASSET_BUY}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Movement entries (no reference number)
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: assetOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: assetOrder.id,
        owner_id: user.id
      },
      // Revenue entries (with reference number, even though commission is 0)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: TOTAL_COMMISSION_EUROS, // 0
        article_date: TODAY,
        description,
        reference_number: "INV-ZERO-001",
        document_id: assetOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: TOTAL_COMMISSION_EUROS, // 0
        article_date: TODAY,
        description,
        reference_number: "INV-ZERO-001",
        document_id: assetOrder.id,
        owner_id: user.id
      }
    ]);

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // Asset Buy should be valid even with zero commission
    expect(assetBuyResult).toEqual({
      isValid: true,
      ledgerEntryCount: 4, // 2 for movements + 2 for revenue entries (even with 0 commission)
      transactionType: "asset_buy",
      dbTotalAmount: EXPECTED_AMOUNT_EUROS,
      ledgerTotalAmount: EXPECTED_AMOUNT_EUROS, // movements + zero commission = total
      difference: 0,
      transactionCount: 1
    });
    expect(assetBuyResult.discrepancies).toBeUndefined();

    // Other transaction types should be valid (no transactions)
    expect(assetSellResult.isValid).toBe(true);
    expect(assetSellResult.transactionCount).toBe(0);
    expect(mmfBuyResult.isValid).toBe(true);
    expect(mmfBuyResult.transactionCount).toBe(0);
    expect(mmfSellResult.isValid).toBe(true);
    expect(mmfSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should validate orders from rebalance transactions", async () => {
    const FX_FEE_CENTS = 50; // €0.50 fx fee
    const REALTIME_FEE_CENTS = 100; // €1.00 realtime execution fee
    const BROKER_FEE_CENTS = 150; // €1.50 broker fee
    const WH_COMMISSION_CENTS = FX_FEE_CENTS + REALTIME_FEE_CENTS; // €1.50 WH commission
    const TOTAL_COMMISSION_CENTS = WH_COMMISSION_CENTS + BROKER_FEE_CENTS; // €3.00 total commission

    const GROSS_AMOUNT_CENTS = 100000; // €1000.00 gross amount (includes broker fee)
    const NET_AMOUNT_CENTS = GROSS_AMOUNT_CENTS - BROKER_FEE_CENTS; // €998.50 net amount
    const EXPECTED_AMOUNT_CENTS = NET_AMOUNT_CENTS + TOTAL_COMMISSION_CENTS; // €1001.50 expected

    const NET_AMOUNT_EUROS = new Decimal(NET_AMOUNT_CENTS).div(100).toNumber();
    const TOTAL_COMMISSION_EUROS = new Decimal(TOTAL_COMMISSION_CENTS).div(100).toNumber();
    const EXPECTED_AMOUNT_EUROS = new Decimal(EXPECTED_AMOUNT_CENTS).div(100).toNumber();

    // Create domestic user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Rebalance Transaction (RebalanceTransaction category)
    const rebalanceBuyTransaction = await buildRebalanceTransaction({
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      rebalanceStatus: "Settled"
    });

    const rebalanceSellTransaction = await buildRebalanceTransaction({
      consideration: { currency: "EUR", amount: NET_AMOUNT_CENTS },
      portfolio: portfolio.id,
      owner: user.id,
      rebalanceStatus: "Settled"
    });

    // Create orders for rebalance transactions with broker fees
    const rebalanceBuyOrder = await buildOrder({
      transaction: rebalanceBuyTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Buy",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-rebalance-buy-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    const rebalanceSellOrder = await buildOrder({
      transaction: rebalanceSellTransaction.id,
      status: "Matched",
      filledAt: new Date(TODAY),
      side: "Sell",
      consideration: {
        currency: "EUR",
        originalAmount: NET_AMOUNT_CENTS,
        amountSubmitted: NET_AMOUNT_CENTS,
        amount: GROSS_AMOUNT_CENTS // Gross amount including broker fee
      },
      fees: {
        fx: { amount: new Decimal(FX_FEE_CENTS).div(100).toNumber(), currency: "EUR" },
        realtimeExecution: { amount: new Decimal(REALTIME_FEE_CENTS).div(100).toNumber(), currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          status: "Matched",
          id: "test-rebalance-sell-order-id",
          submittedAt: new Date(TODAY),
          accountingBrokerFxFee: new Decimal(BROKER_FEE_CENTS).div(100).toNumber() // Convert to euros
        }
      }
    });

    // Create corresponding ledger entries - use ORDER IDs in descriptions
    const rebalanceBuyDescription = `${user.id}|${rebalanceBuyOrder.id}|${AccountingEventType.ASSET_BUY}`;
    const rebalanceSellDescription = `${user.id}|${rebalanceSellOrder.id}|${AccountingEventType.ASSET_SELL}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Rebalance Buy: 1. Commission Fee (Client -> Commission Fees)
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: rebalanceBuyDescription,
        reference_number: "INV-REB-001",
        document_id: rebalanceBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: rebalanceBuyDescription,
        reference_number: "INV-REB-001",
        document_id: rebalanceBuyOrder.id,
        owner_id: user.id
      },
      // Rebalance Buy: 2. Net Settlement (Client -> Omnibus)
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: rebalanceBuyDescription,
        document_id: rebalanceBuyOrder.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: rebalanceBuyDescription,
        document_id: rebalanceBuyOrder.id,
        owner_id: user.id
      },

      // Rebalance Sell: 1. Net Settlement (Omnibus -> Client)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: rebalanceSellDescription,
        document_id: rebalanceSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: NET_AMOUNT_EUROS,
        article_date: TODAY,
        description: rebalanceSellDescription,
        document_id: rebalanceSellOrder.id,
        owner_id: user.id
      },
      // Rebalance Sell: 2. Commission Fee (Client -> Commission Fees)
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "debit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: rebalanceSellDescription,
        reference_number: "INV-REB-002",
        document_id: rebalanceSellOrder.id,
        owner_id: user.id
      },
      {
        aa: 2,
        account_code: LedgerAccounts.COMMISSION_FEES_WH,
        side: "credit",
        amount: TOTAL_COMMISSION_EUROS,
        article_date: TODAY,
        description: rebalanceSellDescription,
        reference_number: "INV-REB-002",
        document_id: rebalanceSellOrder.id,
        owner_id: user.id
      }
    ]);

    // Validate orders
    const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

    expect(results).toHaveLength(5);
    const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

    // Rebalance transactions should be included in asset buy/sell validation
    expect(assetBuyResult).toEqual({
      isValid: true,
      ledgerEntryCount: 4, // 2 for commission + 2 for net settlement
      transactionType: "asset_buy",
      dbTotalAmount: EXPECTED_AMOUNT_EUROS,
      ledgerTotalAmount: EXPECTED_AMOUNT_EUROS, // commission + net debits
      difference: 0,
      transactionCount: 1 // rebalance buy order
    });

    expect(assetSellResult).toEqual({
      isValid: true,
      ledgerEntryCount: 4, // 2 for net settlement + 2 for commission
      transactionType: "asset_sell",
      dbTotalAmount: EXPECTED_AMOUNT_EUROS,
      ledgerTotalAmount: EXPECTED_AMOUNT_EUROS, // net credit + commission debit = total
      difference: 0,
      transactionCount: 1 // rebalance sell order
    });

    // MMF transaction types should be valid (no transactions)
    expect(mmfBuyResult.isValid).toBe(true);
    expect(mmfBuyResult.transactionCount).toBe(0);

    expect(mmfSellResult.isValid).toBe(true);
    expect(mmfSellResult.transactionCount).toBe(0);

    // Internally filled MMF orders should be valid (no transactions)
    expect(internallyFilledResult.isValid).toBe(true);
    expect(internallyFilledResult.transactionCount).toBe(0);
  });

  it("should include internally filled MMF orders validation in the overall validation", async () => {
    const FROM_DATE = "2025-06-01";
    const ORDER_AMOUNT_CENTS = 35000; // £350.00 in cents

    // Create domestic user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create topup and withdrawal transactions
    const savingsTopup = await buildSavingsTopup({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" }
    });

    // Create internally filled order
    await buildOrder({
      transaction: savingsTopup.id,
      side: "Buy",
      status: "InternallyFilled",
      consideration: {
        originalAmount: ORDER_AMOUNT_CENTS,
        amountSubmitted: ORDER_AMOUNT_CENTS,
        amount: ORDER_AMOUNT_CENTS,
        currency: "EUR"
      },
      fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
      activeProviders: [],
      isin: "IE00B404XK09", // MMF ISIN
      filledAt: new Date(TODAY)
    });

    // Run overall validation
    const results = await AccountingValidationService.validateOrdersDbWithLedger(FROM_DATE);

    // Verify results include internally filled orders validation
    expect(results).toHaveLength(5); // asset_buy, asset_sell, mmf_buy, mmf_sell, mmf_internally_filled

    const internallyFilledResult = results.find((r) => r.transactionType === "mmf_internally_filled");
    expect(internallyFilledResult).toBeDefined();
    expect(internallyFilledResult!.isValid).toBe(true);
    expect(internallyFilledResult!.transactionCount).toBe(1);
    expect(internallyFilledResult!.ledgerEntryCount).toBe(0);
  });

  describe("Remainder validation", () => {
    it("should pass validation for asset transaction with correct remainder entries", async () => {
      const ORDER_SUBMITTED_AMOUNT_CENTS = 100_000; // €1000.00
      const ORDER_AMOUNT_CENTS = 98_500; // €985.00 order amount
      const REMAINDER_AMOUNT_CENTS = Decimal.sub(ORDER_SUBMITTED_AMOUNT_CENTS, ORDER_AMOUNT_CENTS).toNumber(); // €15.00 remainder

      const ORDER_AMOUNT_EUROS = new Decimal(ORDER_AMOUNT_CENTS).div(100).toNumber();
      const REMAINDER_AMOUNT_EUROS = new Decimal(REMAINDER_AMOUNT_CENTS).div(100).toNumber();

      // Create domestic user (GR)
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create asset transaction with remainder
      const assetTransaction = await buildAssetTransaction({
        consideration: { currency: "EUR", amount: ORDER_SUBMITTED_AMOUNT_CENTS },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled",
        settledAt: new Date(TODAY),
        portfolioTransactionCategory: "buy"
      });

      // Create order with less than full transaction amount
      const order = await buildOrder({
        transaction: assetTransaction.id,
        status: "Matched",
        filledAt: new Date(TODAY),
        side: "Buy",
        consideration: {
          currency: "EUR",
          originalAmount: ORDER_SUBMITTED_AMOUNT_CENTS,
          amountSubmitted: ORDER_SUBMITTED_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS // Actual filled amount (less than submitted)
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        providers: {
          wealthkernel: {
            status: "Matched",
            id: "test-asset-order-remainder",
            submittedAt: new Date(TODAY),
            accountingBrokerFxFee: 0
          }
        }
      });

      // Link order to transaction by updating the orders field
      (assetTransaction as any).orders = [order.id];
      await assetTransaction.save();

      const description = `${user.id}|${order.id}|${AccountingEventType.ASSET_BUY}`;

      // Create ledger entries including remainder entries
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Order entries (actual order amount)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: ORDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: ORDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        // Remainder entries (remainder returned to client)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: REMAINDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: REMAINDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        // Revenue entries (required for validation)
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: 0,
          article_date: TODAY,
          description,
          reference_number: "INV-001",
          document_id: order.id,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: 0,
          article_date: TODAY,
          description,
          reference_number: "INV-001",
          document_id: order.id,
          owner_id: user.id
        }
      ]);

      // Validate orders
      const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

      expect(results).toHaveLength(5);
      const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

      // Asset Buy should pass validation including remainder validation
      // Expected total: order amount + fees + remainder = 985 + 0 + 15 = 1000 EUR
      const expectedTotalAmount = Decimal.add(ORDER_AMOUNT_EUROS, REMAINDER_AMOUNT_EUROS).toNumber();
      expect(assetBuyResult).toEqual({
        isValid: true,
        transactionType: "asset_buy",
        dbTotalAmount: expectedTotalAmount, // Validation includes remainder in total
        ledgerTotalAmount: ORDER_AMOUNT_EUROS, // Ledger shows net client debits (985 EUR)
        difference: REMAINDER_AMOUNT_EUROS, // Difference shows the remainder (15 EUR)
        transactionCount: 1,
        ledgerEntryCount: 6
      });
      expect(assetBuyResult.discrepancies).toBeUndefined();

      // Other transaction types should be valid (no transactions)
      expect(assetSellResult.isValid).toBe(true);
      expect(assetSellResult.transactionCount).toBe(0);
      expect(mmfBuyResult.isValid).toBe(true);
      expect(mmfBuyResult.transactionCount).toBe(0);
      expect(mmfSellResult.isValid).toBe(true);
      expect(mmfSellResult.transactionCount).toBe(0);
      expect(internallyFilledResult.isValid).toBe(true);
      expect(internallyFilledResult.transactionCount).toBe(0);
    });

    it("should pass validation for asset transaction with partial order amount", async () => {
      const TRANSACTION_AMOUNT_CENTS = 50_000; // €500.00 transaction amount
      const ORDER_AMOUNT_CENTS = 47_500; // €475.00 order amount

      const ORDER_AMOUNT_EUROS = new Decimal(ORDER_AMOUNT_CENTS).div(100).toNumber();

      // Create domestic user (GR)
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create asset transaction with remainder
      const assetTransaction = await buildAssetTransaction({
        consideration: { currency: "EUR", amount: ORDER_AMOUNT_CENTS },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled",
        settledAt: new Date(TODAY),
        portfolioTransactionCategory: "sell"
      });

      // Create order with less than full transaction amount
      const order = await buildOrder({
        transaction: assetTransaction.id,
        status: "Matched",
        filledAt: new Date(TODAY),
        side: "Sell",
        consideration: {
          currency: "EUR",
          originalAmount: TRANSACTION_AMOUNT_CENTS,
          amountSubmitted: TRANSACTION_AMOUNT_CENTS, // Full transaction amount
          amount: ORDER_AMOUNT_CENTS // Actual filled amount (less than submitted)
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        providers: {
          wealthkernel: {
            status: "Matched",
            id: "test-asset-sell-missing-remainder",
            submittedAt: new Date(TODAY),
            accountingBrokerFxFee: 0
          }
        }
      });

      // Link order to transaction by updating the orders field
      (assetTransaction as any).orders = [order.id];
      await assetTransaction.save();

      const description = `${user.id}|${order.id}|${AccountingEventType.ASSET_SELL}`;

      // Create ledger entries WITHOUT remainder entries (missing remainder entries)
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Normal order entries only (no remainder entries)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: ORDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "credit",
          amount: ORDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        // Revenue entries (required for validation)
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: 0,
          article_date: TODAY,
          description,
          reference_number: "INV-002",
          document_id: order.id,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: 0,
          article_date: TODAY,
          description,
          reference_number: "INV-002",
          document_id: order.id,
          owner_id: user.id
        }
      ]);

      // Validate orders
      const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

      expect(results).toHaveLength(5);
      const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

      // Asset Sell should pass validation (remainder validation not currently implemented)
      expect(assetSellResult.isValid).toBe(true);
      expect(assetSellResult.transactionType).toBe("asset_sell");
      expect(assetSellResult.dbTotalAmount).toBe(ORDER_AMOUNT_EUROS); // Validation uses order amount
      expect(assetSellResult.ledgerTotalAmount).toBe(ORDER_AMOUNT_EUROS); // Ledger entries match order amount
      expect(assetSellResult.difference).toBe(0); // No difference since both are order amount
      expect(assetSellResult.transactionCount).toBe(1);

      // Other transaction types should be valid (no transactions)
      expect(assetBuyResult.isValid).toBe(true);
      expect(assetBuyResult.transactionCount).toBe(0);
      expect(mmfBuyResult.isValid).toBe(true);
      expect(mmfBuyResult.transactionCount).toBe(0);
      expect(mmfSellResult.isValid).toBe(true);
      expect(mmfSellResult.transactionCount).toBe(0);
      expect(internallyFilledResult.isValid).toBe(true);
      expect(internallyFilledResult.transactionCount).toBe(0);
    });

    it("should pass validation for savings transactions (no remainder logic applied)", async () => {
      const ORDER_AMOUNT_CENTS = 75_000; // €750.00 order amount
      const ORDER_AMOUNT_EUROS = new Decimal(ORDER_AMOUNT_CENTS).div(100).toNumber();

      // Create domestic user (GR)
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Create savings transaction (remainders don't apply to savings)
      const savingsTransaction = await buildSavingsTopup({
        consideration: { currency: "EUR", amount: ORDER_AMOUNT_CENTS },
        portfolio: portfolio.id,
        owner: user.id,
        status: "Settled",
        settledAt: new Date(TODAY),
        savingsProduct: "mmf_dist_eur"
      });

      // Create order for savings transaction
      const order = await buildOrder({
        transaction: savingsTransaction.id,
        status: "Matched",
        filledAt: new Date(TODAY),
        side: "Buy",
        consideration: {
          currency: "EUR",
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS
        },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        providers: {
          wealthkernel: {
            status: "Matched",
            id: "test-savings-order-no-remainder",
            submittedAt: new Date(TODAY),
            accountingBrokerFxFee: 0
          }
        }
      });

      // Link order to transaction by updating the orders field
      (savingsTransaction as any).orders = [order.id];
      await savingsTransaction.save();

      const description = `${user.id}|${order.id}|${AccountingEventType.ASSET_BUY}`;

      // Create normal savings order ledger entries (no remainder entries needed)
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Normal savings order entries
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: ORDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: ORDER_AMOUNT_EUROS,
          article_date: TODAY,
          description,
          document_id: order.id,
          owner_id: user.id
        },
        // Revenue entries (required for validation)
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: 0,
          article_date: TODAY,
          description,
          reference_number: "INV-003",
          document_id: order.id,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: 0,
          article_date: TODAY,
          description,
          reference_number: "INV-003",
          document_id: order.id,
          owner_id: user.id
        }
      ]);

      // Validate orders
      const results = await AccountingValidationService.validateOrdersDbWithLedger(TODAY);

      expect(results).toHaveLength(5);
      const [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult] = results;

      // MMF Buy should pass validation (no remainder logic for savings)
      expect(mmfBuyResult).toEqual({
        isValid: true,
        transactionType: "mmf_buy",
        dbTotalAmount: ORDER_AMOUNT_EUROS,
        ledgerTotalAmount: ORDER_AMOUNT_EUROS,
        difference: 0,
        transactionCount: 1,
        ledgerEntryCount: 4
      });
      expect(mmfBuyResult.discrepancies).toBeUndefined();

      // Other transaction types should be valid (no transactions)
      expect(assetBuyResult.isValid).toBe(true);
      expect(assetBuyResult.transactionCount).toBe(0);
      expect(assetSellResult.isValid).toBe(true);
      expect(assetSellResult.transactionCount).toBe(0);
      expect(mmfSellResult.isValid).toBe(true);
      expect(mmfSellResult.transactionCount).toBe(0);
      expect(internallyFilledResult.isValid).toBe(true);
      expect(internallyFilledResult.transactionCount).toBe(0);
    });
  });
});
