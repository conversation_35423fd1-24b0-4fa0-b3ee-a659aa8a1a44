import "jest";
import { connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildUser,
  buildPortfolio,
  buildHoldingDTO,
  buildReward,
  buildRevertRewardTransaction
} from "../../../tests/utils/generateModels";
import { ProviderEnum } from "../../../configs/providersConfig";
import { faker } from "@faker-js/faker";
import { TransactionService } from "../../transactionService";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";
import { RevertRewardTransactionDocument } from "../../../models/Transaction";

describe("TransactionService.getRevertRewardByWealthkernelId", () => {
  let user: UserDocument;
  let portfolio: PortfolioDocument;
  let revertRewardTransaction: RevertRewardTransactionDocument;

  beforeAll(async () => {
    await connectDb("getRevertRewardByWealthkernelId");

    user = await buildUser();
    portfolio = await buildPortfolio({
      holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
    });

    const reward = await buildReward({
      targetUser: user.id,
      asset: "equities_uk",
      quantity: 100,
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      },
      order: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched"
          }
        }
      }
    });

    revertRewardTransaction = await buildRevertRewardTransaction({
      providers: {
        wealthkernel: {
          id: faker.string.uuid()
        }
      },
      wealthkernel: {},
      owner: user.id,
      portfolio: portfolio.id,
      status: "Pending",
      consideration: {
        currency: "GBP"
      },
      createdAt: new Date("2022-10-31"),
      reward: reward.id
    });
  });

  afterEach(async () => {
    await clearDb();
  });

  afterAll(async () => await closeDb());

  it("should return the revert reward transaction when it exists", async () => {
    const result = await TransactionService.getRevertRewardByWealthkernelId(
      revertRewardTransaction.providers!.wealthkernel!.id
    );

    expect(result.id).toBe(revertRewardTransaction.id);
  });

  it("should return undefined when it does not exist", async () => {
    const result = await TransactionService.getRevertRewardByWealthkernelId(faker.string.uuid());

    expect(result).toBeNull();
  });
});
