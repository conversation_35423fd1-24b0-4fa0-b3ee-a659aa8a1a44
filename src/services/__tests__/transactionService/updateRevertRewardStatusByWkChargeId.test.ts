import "jest";
import { connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildUser,
  buildPortfolio,
  buildHoldingDTO,
  buildReward,
  buildRevertRewardTransaction
} from "../../../tests/utils/generateModels";
import { ProviderEnum } from "../../../configs/providersConfig";
import { faker } from "@faker-js/faker";
import { TransactionService } from "../../transactionService";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";
import { RevertRewardTransaction, RevertRewardTransactionDocument } from "../../../models/Transaction";
import { NotFoundError } from "../../../models/ApiErrors";

describe("TransactionService.updateRevertRewardStatusByWkChargeId", () => {
  const TODAY = new Date("2024-01-10");
  Date.now = jest.fn(() => +TODAY);

  let user: UserDocument;
  let portfolio: PortfolioDocument;
  let revertRewardTransaction: RevertRewardTransactionDocument;

  beforeAll(async () => {
    await connectDb("getRevertRewardByWealthkernelId");
  });

  beforeEach(async () => {
    user = await buildUser();
    portfolio = await buildPortfolio({
      holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
    });

    const reward = await buildReward({
      targetUser: user.id,
      asset: "equities_uk",
      quantity: 100,
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      },
      order: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched"
          }
        }
      }
    });

    revertRewardTransaction = await buildRevertRewardTransaction({
      providers: {
        wealthkernel: {
          id: faker.string.uuid(),
          submittedAt: new Date()
        }
      },
      wealthkernel: {},
      owner: user.id,
      portfolio: portfolio.id,
      status: "Pending",
      consideration: {
        currency: "GBP"
      },
      createdAt: new Date("2022-10-31"),
      reward: reward.id
    });
  });

  afterEach(async () => {
    await clearDb();
  });

  afterAll(async () => {
    await closeDb();
  });

  it("should throw error when transaction does not exist", () => {
    expect(
      TransactionService.updateRevertRewardStatusByWkChargeId(faker.string.uuid(), "Requested")
    ).rejects.toThrow(NotFoundError);
  });

  it("should update transaction when it exists and new charge status is not booked/completed/cancelled", async () => {
    await TransactionService.updateRevertRewardStatusByWkChargeId(
      revertRewardTransaction.providers!.wealthkernel!.id,
      "Requested"
    );

    const result = await RevertRewardTransaction.findOne({ _id: revertRewardTransaction.id });

    expect(result!.toObject()).toEqual(
      expect.objectContaining({
        providers: expect.objectContaining({
          wealthkernel: expect.objectContaining({
            id: revertRewardTransaction.providers?.wealthkernel?.id,
            status: "Requested",
            submittedAt: revertRewardTransaction.providers?.wealthkernel?.submittedAt
          })
        })
      })
    );
  });

  it("should update transaction when it exists and new charge status is booked", async () => {
    await TransactionService.updateRevertRewardStatusByWkChargeId(
      revertRewardTransaction.providers!.wealthkernel!.id,
      "Booked"
    );

    const result = await RevertRewardTransaction.findOne({ _id: revertRewardTransaction.id });

    expect(result!.toObject()).toEqual(
      expect.objectContaining({
        status: "Settled",
        settledAt: TODAY,
        providers: expect.objectContaining({
          wealthkernel: expect.objectContaining({
            id: revertRewardTransaction.providers?.wealthkernel?.id,
            status: "Booked",
            submittedAt: revertRewardTransaction.providers?.wealthkernel?.submittedAt
          })
        })
      })
    );
  });

  it("should update transaction when it exists and new charge status is completed", async () => {
    await TransactionService.updateRevertRewardStatusByWkChargeId(
      revertRewardTransaction.providers!.wealthkernel!.id,
      "Completed"
    );

    const result = await RevertRewardTransaction.findOne({ _id: revertRewardTransaction.id });

    expect(result!.toObject()).toEqual(
      expect.objectContaining({
        status: "Settled",
        settledAt: TODAY,
        providers: expect.objectContaining({
          wealthkernel: expect.objectContaining({
            id: revertRewardTransaction.providers?.wealthkernel?.id,
            status: "Completed",
            submittedAt: revertRewardTransaction.providers?.wealthkernel?.submittedAt
          })
        })
      })
    );
  });

  it("should update transaction when it exists and new charge status is cancelled", async () => {
    await TransactionService.updateRevertRewardStatusByWkChargeId(
      revertRewardTransaction.providers!.wealthkernel!.id,
      "Cancelled"
    );

    const result = await RevertRewardTransaction.findOne({ _id: revertRewardTransaction.id });

    expect(result!.toObject()).toEqual(
      expect.objectContaining({
        status: "Cancelled",
        providers: expect.objectContaining({
          wealthkernel: expect.objectContaining({
            id: revertRewardTransaction.providers?.wealthkernel?.id,
            status: "Cancelled",
            submittedAt: revertRewardTransaction.providers?.wealthkernel?.submittedAt
          })
        })
      })
    );
  });
});
