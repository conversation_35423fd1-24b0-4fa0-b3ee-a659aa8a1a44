import { faker } from "@faker-js/faker";
import { entitiesConfig, fees, investmentUniverseConfig, whitelistConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import events from "../../event-handlers/events";
import logger from "../../external-services/loggerService";
import MailchimpService, { AudienceIdEnum } from "../../external-services/mailchimpService";
import eventEmitter from "../../loaders/eventEmitter";
import { PortfolioModeEnum } from "../../models/Portfolio";
import { Reward } from "../../models/Reward";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildInvestmentProduct,
  buildNotificationSettings,
  buildOrder,
  buildParticipant,
  buildPortfolio,
  buildReward,
  buildUser,
  buildUserDataRequest
} from "../../tests/utils/generateModels";
import { DepositMethodEnum } from "../../types/transactions";
import CurrencyUtil from "../../utils/currencyUtil";
import DateUtil from "../../utils/dateUtil";
import RewardService from "../rewardService";
import { WHITELISTED_EU_EMAILS } from "@wealthyhood/shared-configs/dist/whitelist";

const { MINIMUM_FX_FEE, MINIMUM_COMMISSION_FEE } = fees;

describe("RewardService", () => {
  beforeAll(async () => await connectDb("RewardService"));
  afterAll(async () => await closeDb());

  describe("createAllCRMCampaignRewards", () => {
    describe("when Mailchimp does not have eligible users", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(0);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([]);

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });
    });

    describe("when Mailchimp has user who has not signed up", () => {
      const email = faker.internet.email();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(logger, "info");
        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(1);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email
          }
        ]);

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(`Not creating reward for ${email} as they're not signed up!`, {
          module: "RewardService",
          method: "_createCRMCampaignRewardsForPage",
          userEmail: email
        });
      });
    });

    describe("when Mailchimp has user who is not KYC passed", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ kycStatus: KycStatusEnum.PENDING });
        jest.spyOn(logger, "info");
        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(1);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email: user.email
          }
        ]);

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          `We tried to give a reward to ${user.id} but we couldn't because the user has not passed KYC.`,
          {
            module: "UserService",
            method: "isRewardEligible",
            userEmail: user.email,
            data: {
              targetUserEmail: user.email,
              referral: user.id
            }
          }
        );
      });
    });

    describe("when Mailchimp has UK user who does not have an investment over £100", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        jest.spyOn(logger, "info");
        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(1);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email: user.email
          }
        ]);

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          `We tried to give a reward to ${user.id} but they do not have investments over £100.00`,
          {
            module: "UserService",
            method: "isRewardEligible",
            userEmail: user.email,
            data: {
              targetUserEmail: user.email,
              referral: user.id
            }
          }
        );
      });
    });

    describe("when Mailchimp has EU user who does not have an deposit over £100", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "GR",
          currency: "EUR"
        });
        jest.spyOn(logger, "info");
        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(1);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email: user.email
          }
        ]);

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          `We tried to give a reward to ${user.id} but they do not have deposits over ${CurrencyUtil.formatCurrency(100, "EUR", "el")}`,
          {
            module: "UserService",
            method: "isRewardEligible",
            userEmail: user.email,
            data: {
              targetUserEmail: user.email,
              referral: user.id
            }
          }
        );
      });
    });

    describe("when Mailchimp has eligible user who already has pending reward", () => {
      const referralCampaign = "<EMAIL>";

      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, referredByEmail: referralCampaign });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });
        await buildReward({ targetUser: user.id, referral: user.id });

        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(1);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email: user.email
          }
        ]);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any new rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(1); // We should still have one reward (the one created in the test setup)
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          `Target user ${user.id} already has pending rewards, aborting...`,
          {
            module: "RewardService",
            method: "_processUserCRMCampaignReward",
            userEmail: user.email
          }
        );
      });
    });

    describe("when Mailchimp has eligible user in status Single reward from CRM Campaign", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildNotificationSettings({ owner: user.id });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 10000,
          consideration: {
            amount: 10000,
            currency: "GBP"
          }
        });

        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(1);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email: user.email
          }
        ]);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referralCampaign: "CRM Campaign",
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            consideration: {
              currency: "GBP",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            },
            fees: expect.objectContaining({
              fx: {
                amount: MINIMUM_FX_FEE,
                currency: "GBP"
              },
              commission: {
                amount: MINIMUM_COMMISSION_FEE,
                currency: "GBP"
              },
              executionSpread: {
                amount: expect.any(Number),
                currency: "GBP"
              }
            })
          })
        );
      });

      it("should hit Mailchimp to update the user's ETF reward status", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          user.email,
          {
            merge_fields: {
              CAMPLIVE: "Reward settled"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when Mailchimp has two UK users out of which one is eligible", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildNotificationSettings({ owner: user.id });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 10000,
          consideration: {
            amount: 10000,
            currency: "GBP"
          }
        });

        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(2);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email: faker.internet.email()
          },
          {
            email: user.email
          }
        ]);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referralCampaign: "CRM Campaign",
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            }
          })
        );
      });

      it("should hit Mailchimp to update the user's ETF reward status", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          user.email,
          {
            merge_fields: {
              CAMPLIVE: "Reward settled"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when Mailchimp has two EU users out of which one is eligible", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "GR",
          currency: "EUR"
        });
        await buildNotificationSettings({ owner: user.id });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          status: "Settled",
          consideration: { currency: "EUR", amount: 10000 }
        });

        jest.spyOn(MailchimpService, "updateMember");
        jest.spyOn(MailchimpService, "getPendingRewardMemberCount").mockResolvedValue(2);
        jest.spyOn(MailchimpService, "getPendingRewardMembers").mockResolvedValue([
          {
            email: faker.internet.email()
          },
          {
            email: user.email
          }
        ]);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllCRMCampaignRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referralCampaign: "CRM Campaign",
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "EUR",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            }
          })
        );
      });

      it("should hit Mailchimp to update the user's ETF reward status", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          user.email,
          {
            merge_fields: {
              CAMPLIVE: "Reward settled"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });
  });

  describe("createAllReferralRewards", () => {
    describe("when there is a referred user but they signed up more than 10 days ago", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const anotherUser = await buildUser({ email: faker.internet.email() });
        await buildUser({
          referredByEmail: anotherUser.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 11)
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });
    });

    describe("when there is a referred user that signed up yesterday but they are KYC failed", () => {
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser({ email: faker.internet.email() });
        await buildUser({
          kycStatus: "failed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should not log anything", async () => {
        expect(logger.info).not.toHaveBeenCalled();
      });
    });

    describe("when there is a referred user that signed up yesterday BUT are eu whitelisted", () => {
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser({ email: faker.internet.email() });
        await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          usedWhitelistCode: true
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should not log anything", async () => {
        expect(logger.info).not.toHaveBeenCalled();
      });
    });

    describe("when there is a UK referred user that signed up yesterday but they don't have investments totalling >= £100", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser({ email: faker.internet.email() });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 3000,
            currency: "GBP"
          },
          originalInvestmentAmount: 3000
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 1900,
            currency: "GBP"
          },
          originalInvestmentAmount: 1900
        });
        const assetBuyTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled",
          consideration: {
            currency: "GBP",
            amount: 90
          }
        });
        assetBuyTransaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: assetBuyTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              amount: 90,
              currency: "GBP"
            }
          })
        ];
        await assetBuyTransaction.save();

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          `We tried to give a reward to ${user.id} but they do not have investments over £100.00`,
          {
            module: "UserService",
            method: "isRewardEligible",
            userEmail: user.email,
            data: {
              targetUserEmail: user.email,
              referral: user.id,
              referrer: referrer.id
            }
          }
        );
      });

      it("should NOT emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should NOT emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a EU referred user that signed up yesterday but they don't have deposits totalling >= £100", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser({ email: faker.internet.email() });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "DE",
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          status: "Settled",
          consideration: { currency: "EUR", amount: 9000 }
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          `We tried to give a reward to ${user.id} but they do not have deposits over ${CurrencyUtil.formatCurrency(100, "EUR", "el")}`,
          {
            module: "UserService",
            method: "isRewardEligible",
            userEmail: user.email,
            data: {
              targetUserEmail: user.email,
              referral: user.id,
              referrer: referrer.id
            }
          }
        );
      });

      it("should NOT emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should NOT emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a UK referred user that signed up yesterday, they have investments totalling £100, and the referrer an e-mail of a non-existing user", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: "<EMAIL>",
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
        await buildNotificationSettings({ owner: user.id });
        const portfolio = await buildPortfolio({
          owner: user.id
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 8000,
            currency: "GBP"
          },
          originalInvestmentAmount: 8000
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 1900,
            currency: "GBP"
          },
          originalInvestmentAmount: 1910
        });
        const assetBuyTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled",
          consideration: {
            currency: "GBP",
            amount: 90
          }
        });
        assetBuyTransaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: assetBuyTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              amount: 90,
              currency: "GBP"
            }
          })
        ];
        await assetBuyTransaction.save();

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referralCampaign: "<EMAIL>",
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            }
          })
        );
      });

      it("should NOT emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.anything()
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a UK referred user that signed up yesterday, they have investments totalling £100, and the referrer an e-mail of a deleted user", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const referrerEmail = faker.internet.email();
        referrer = await buildUser({ email: `deleted_${referrerEmail}`, kycStatus: "passed" });
        await buildUserDataRequest({
          owner: referrer.id,
          requestType: "disassociation"
        });

        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrerEmail,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
        const portfolio = await buildPortfolio({
          owner: user.id
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 8000,
            currency: "GBP"
          },
          originalInvestmentAmount: 8000
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 1900,
            currency: "GBP"
          },
          originalInvestmentAmount: 1910
        });
        const assetBuyTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled",
          consideration: {
            currency: "GBP",
            amount: 90
          }
        });
        assetBuyTransaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: assetBuyTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              amount: 90,
              currency: "GBP"
            }
          })
        ];
        await assetBuyTransaction.save();

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            }
          })
        );
      });

      it("should not create another reward for the referrer", async () => {
        const rewards = await Reward.find({ targetUser: referrer.id });
        expect(rewards.length).toEqual(0);
      });
    });

    describe("when there is a UK referred user that signed up yesterday, they have investments totalling £100, and the referrer is KYC passed", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser({ email: faker.internet.email(), kycStatus: "passed" });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
        const [portfolio] = await Promise.all([
          buildPortfolio({
            owner: user.id
          }),
          buildNotificationSettings({ owner: user.id }),
          buildNotificationSettings({ owner: referrer.id })
        ]);

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 8000,
            currency: "GBP"
          },
          originalInvestmentAmount: 8000
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 1900,
            currency: "GBP"
          },
          originalInvestmentAmount: 1910
        });
        const assetBuyTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled",
          consideration: {
            currency: "GBP",
            amount: 90
          }
        });
        assetBuyTransaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: assetBuyTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              amount: 90,
              currency: "GBP"
            }
          })
        ];
        await assetBuyTransaction.save();

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should create another reward for the referrer", async () => {
        const rewards = await Reward.find({ targetUser: referrer.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: referrer._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a EU referred user that signed up yesterday, they have direct debit deposits totalling 100 euros, and the referrer is KYC passed", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        referrer = await buildUser({
          email: faker.internet.email(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "DE",
          currency: "EUR"
        });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "DE",
          currency: "EUR"
        });
        const [portfolio] = await Promise.all([
          buildPortfolio({
            owner: user.id
          }),
          buildNotificationSettings({ owner: user.id }),
          buildNotificationSettings({ owner: referrer.id })
        ]);

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          status: "Pending",
          consideration: { currency: "EUR", amount: 10000 },
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Collected" }
            }
          }
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward.toObject()).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "EUR",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should create another reward for the referrer", async () => {
        const rewards = await Reward.find({ targetUser: referrer.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward.toObject()).toEqual(
          expect.objectContaining({
            targetUser: referrer._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "EUR",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a EU referred user that signed up yesterday, they have deposits totalling 100 euros, and the referrer is KYC passed", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        referrer = await buildUser({
          email: faker.internet.email(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "DE",
          currency: "EUR"
        });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "DE",
          currency: "EUR"
        });
        const [portfolio] = await Promise.all([
          buildPortfolio({
            owner: user.id
          }),
          buildNotificationSettings({ owner: user.id }),
          buildNotificationSettings({ owner: referrer.id })
        ]);

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          status: "Pending",
          consideration: { currency: "EUR", amount: 10000 },
          transferWithIntermediary: {
            acquisition: {
              incomingPayment: {
                providers: {
                  devengo: {
                    status: "confirmed",
                    accountId: "test-account-id"
                  }
                }
              }
            }
          }
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward.toObject()).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "EUR",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should create another reward for the referrer", async () => {
        const rewards = await Reward.find({ targetUser: referrer.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward.toObject()).toEqual(
          expect.objectContaining({
            targetUser: referrer._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "EUR",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a EU referred user that has whitelisted email, signed up yesterday, they have deposits totalling 100 euros, and the referrer is KYC passed", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        referrer = await buildUser({
          email: faker.internet.email(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "DE",
          currency: "EUR"
        });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          email: WHITELISTED_EU_EMAILS[0],
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "DE",
          currency: "EUR"
        });
        const [portfolio] = await Promise.all([
          buildPortfolio({
            owner: user.id
          }),
          buildNotificationSettings({ owner: user.id }),
          buildNotificationSettings({ owner: referrer.id })
        ]);

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          status: "Pending",
          consideration: { currency: "EUR", amount: 10000 },
          transferWithIntermediary: {
            acquisition: {
              incomingPayment: {
                providers: {
                  devengo: {
                    status: "confirmed",
                    accountId: "test-account-id"
                  }
                }
              }
            }
          }
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward.toObject()).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "EUR",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should create another reward for the referrer", async () => {
        const rewards = await Reward.find({ targetUser: referrer.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward.toObject()).toEqual(
          expect.objectContaining({
            targetUser: referrer._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "EUR",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a UK referred user that signed up yesterday, they have investments totalling £100, and the referrer is not KYC passed", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser({ email: faker.internet.email(), kycStatus: "failed" });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
        await buildNotificationSettings({ owner: user.id });
        const portfolio = await buildPortfolio({
          owner: user.id
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 8000,
            currency: "GBP"
          },
          originalInvestmentAmount: 8000
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 1900,
            currency: "GBP"
          },
          originalInvestmentAmount: 2000
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.anything(),
              bonusAmount: expect.anything(),
              orderAmount: expect.anything()
            },
            fees: expect.objectContaining({
              fx: {
                amount: MINIMUM_FX_FEE,
                currency: "GBP"
              },
              commission: {
                amount: MINIMUM_COMMISSION_FEE,
                currency: "GBP"
              }
            })
          })
        );
      });

      it("should not create another reward for the referrer", async () => {
        const rewards = await Reward.find({ targetUser: referrer.id });
        expect(rewards.length).toEqual(0);
      });

      it("should NOT emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when there is a UK referred user that signed up yesterday, they have investments totalling £100, and the referrer is an ambassador", () => {
      let user: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const referrerParticipant = await buildParticipant({ participantRole: "AMBASSADOR" });
        referrer = await buildUser({ email: referrerParticipant.email, kycStatus: "passed" });
        user = await buildUser({
          kycStatus: "passed",
          referredByEmail: referrer.email,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
        await buildNotificationSettings({ owner: user.id });
        const portfolio = await buildPortfolio({
          owner: user.id
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 8000,
            currency: "GBP"
          },
          originalInvestmentAmount: 8000
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          consideration: {
            amount: 1900,
            currency: "GBP"
          },
          originalInvestmentAmount: 2000
        });

        await RewardService.createAllReferralRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward for the referred user", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referrer: referrer._id,
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            },
            fees: expect.objectContaining({
              fx: {
                amount: MINIMUM_FX_FEE,
                currency: "GBP"
              },
              commission: {
                amount: MINIMUM_COMMISSION_FEE,
                currency: "GBP"
              }
            })
          })
        );
      });

      it("should not create another reward for the referrer", async () => {
        const rewards = await Reward.find({ targetUser: referrer.id });
        expect(rewards.length).toEqual(0);
      });

      it("should NOT emit 'onReferrerRewardCreation'", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.referral.referrerRewardCreation.eventId,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });
  });

  describe("createAllEuWhitelistedRewards", () => {
    describe("when the user is not whitelisted", () => {
      const email = "<EMAIL>";

      beforeAll(async () => {
        jest.resetAllMocks();

        await buildUser({ kycStatus: KycStatusEnum.PASSED, email: email, usedWhitelistCode: false });

        await RewardService.createAllEuWhitelistRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });
    });

    describe("when the user is whitelisted, but already has reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(logger, "info");

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, usedWhitelistCode: true });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });
        await buildReward({ targetUser: user.id, referral: user.id });

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllEuWhitelistRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any new rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(1); // We should still have one reward (the one created in the test setup)
      });

      it("should log the fact that we could not create a reward for the user", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          `Target user ${user.id} already has reward, not processing their reward...`,
          {
            module: "RewardService",
            method: "_processUserWhitelistReward",
            userEmail: user.email
          }
        );
      });
    });

    describe("when user has used a whitelist promo code", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, usedWhitelistCode: true });
        await buildNotificationSettings({ owner: user.id });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllEuWhitelistRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referralCampaign: "Whitelisted EU Users",
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            consideration: {
              currency: "GBP",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            }
          })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user has a whitelisted email", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          email: whitelistConfig.WHITELISTED_EU_EMAILS[0]
        });
        await buildNotificationSettings({ owner: user.id });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllEuWhitelistRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referralCampaign: "Whitelisted EU Users",
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            consideration: {
              currency: "GBP",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            }
          })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user has a whitelisted email, but is also referred by another user", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          email: whitelistConfig.WHITELISTED_EU_EMAILS[0],
          referredByEmail: faker.internet.email()
        });
        await buildNotificationSettings({ owner: user.id });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllEuWhitelistRewards();
      });
      afterAll(async () => await clearDb());

      it("should not create any rewards", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });
    });

    describe("when there are 2 whitelised users out of which one is eligible", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        // Eligible user
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, usedWhitelistCode: true });

        // Ineligible user
        await buildUser({ kycStatus: KycStatusEnum.PENDING, usedWhitelistCode: true });

        await buildNotificationSettings({ owner: user.id });
        const holdings = [await buildHoldingDTO(true, "equities_uk", 1)];
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await RewardService.createAllEuWhitelistRewards();
      });
      afterAll(async () => await clearDb());

      it("should create one reward", async () => {
        const rewards = await Reward.find({ targetUser: user.id });
        expect(rewards.length).toEqual(1);

        const reward = rewards[0];
        expect(reward).toEqual(
          expect.objectContaining({
            targetUser: user._id,
            referral: user._id,
            referralCampaign: "Whitelisted EU Users",
            asset: "equities_apple" as investmentUniverseConfig.AssetType,
            consideration: {
              currency: "GBP",
              amount: expect.any(Number),
              bonusAmount: expect.any(Number),
              orderAmount: expect.any(Number)
            }
          })
        );
      });

      it("should emit 'onReferralRewardCreation'", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.referralRewardCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });
  });
});
