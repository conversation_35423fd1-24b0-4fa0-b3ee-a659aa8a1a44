import { faker } from "@faker-js/faker";
import { countriesConfig } from "@wealthyhood/shared-configs";
import events from "../../../event-handlers/events";
import eventEmitter from "../../../loaders/eventEmitter";
import { User, UserDocument } from "../../../models/User";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { buildUser } from "../../../tests/utils/generateModels";
import UserService from "../../userService";
import { IdDocTypeEnum } from "../../../external-services/sumsubService";

describe("UserService.updateKycProviderDataIfMissing", () => {
  beforeAll(async () => await connectDb("updateKycProviderDataIfMissing"));
  afterAll(async () => {
    await clearDb();
    await closeDb();
  });

  describe("updateKycProviderDataIfMissing", () => {
    describe("when the user has already submitted passport details", () => {
      let user: UserDocument;

      const APPLICANT_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();

        await UserService.updateKycProviderDataIfMissing(user.id, APPLICANT_ID, {
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
          nationality: "GB"
        });
      });
      afterAll(async () => await clearDb());

      it("should not overwrite the existing passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.firstName).toEqual(user.firstName);
        expect(updatedUser?.lastName).toEqual(user.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(user.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual(user.nationalities);
      });

      it("should not emit any event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when the user hasn't submitted passport details previously", () => {
      let user: UserDocument;

      const APPLICANT_ID = faker.string.uuid();
      const PASSPORT_DETAILS = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
        nationality: "GB" as countriesConfig.CountryCodesType,
        documentNumber: faker.string.uuid(),
        documentType: IdDocTypeEnum.PASSPORT,
        documentCountryCode: "GB" as countriesConfig.CountryCodesType
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser({}, false, true);

        await UserService.updateKycProviderDataIfMissing(user.id, APPLICANT_ID, PASSPORT_DETAILS);
      });
      afterAll(async () => await clearDb());

      it("should update user with new passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.providers?.sumsub?.id).toEqual(APPLICANT_ID);
        expect(updatedUser?.firstName).toEqual(PASSPORT_DETAILS.firstName);
        expect(updatedUser?.lastName).toEqual(PASSPORT_DETAILS.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(PASSPORT_DETAILS.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual([PASSPORT_DETAILS.nationality]);
      });

      it("should update user's id document", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.idDocument).toEqual({
          countryCode: PASSPORT_DETAILS.documentCountryCode,
          documentType: PASSPORT_DETAILS.documentType,
          value: PASSPORT_DETAILS.documentNumber
        });
      });

      it("should emit passport details event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.passportDetailsSubmission.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when the user hasn't submitted passport details previously but nationality is missing", () => {
      let user: UserDocument;
      const PASSPORT_DETAILS = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
        documentNumber: faker.string.uuid(),
        documentType: IdDocTypeEnum.PASSPORT,
        documentCountryCode: "GB" as countriesConfig.CountryCodesType
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser({}, false, true);

        await UserService.updateKycProviderDataIfMissing(user.id, faker.string.uuid(), PASSPORT_DETAILS);
      });
      afterAll(async () => await clearDb());

      it("should update user with new passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.firstName).toEqual(PASSPORT_DETAILS.firstName);
        expect(updatedUser?.lastName).toEqual(PASSPORT_DETAILS.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(PASSPORT_DETAILS.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual([]);
      });

      it("should update user's id document", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.idDocument).toEqual({
          countryCode: PASSPORT_DETAILS.documentCountryCode,
          documentType: PASSPORT_DETAILS.documentType,
          value: PASSPORT_DETAILS.documentNumber
        });
      });

      it("should not emit any event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });
  });
});
