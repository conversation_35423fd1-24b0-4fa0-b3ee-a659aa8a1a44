import { ProviderEnum } from "../../../configs/providersConfig";
import ProviderService from "../../providerService";
import RewardService from "../../rewardService";
import { Reward } from "../../../models/Reward";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { buildPortfolio, buildReward, buildUser } from "../../../tests/utils/generateModels";
import { CurrencyEnum } from "../../../external-services/wealthkernelService";
import { buildWealthkernelOrderResponse } from "../../../tests/utils/generateWealthkernel";
import events from "../../../event-handlers/events";
import eventEmitter from "../../../loaders/eventEmitter";
import { RedisClientService } from "../../../loaders/redis";
import InvestmentProductService from "../../investmentProductService";

const MATCHED_ORDER_AMOUNT = 9.95;

describe("RewardService.updateRewardOrderStatus", () => {
  beforeAll(async () => connectDb("updateRewardOrderStatus"));

  afterEach(async () => {
    jest.restoreAllMocks();
    await clearDb();
  });

  afterAll(async () => closeDb());

  it("stores the executed WealthKernel amount in cents when the reward order matches", async () => {
    const wkOrderId = "wk-order-id";

    const user = await buildUser({ currency: "GBP" });

    await buildPortfolio({
      owner: user.id,
      providers: { wealthkernel: { id: "portfolio-id", status: "Active" } }
    });

    const reward = await buildReward({
      asset: "equities_apple",
      targetUser: user.id,
      consideration: { currency: "GBP", amount: 1000, bonusAmount: 1200, orderAmount: 1000 },
      order: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: wkOrderId,
            status: "Pending",
            submittedAt: new Date("2024-01-01T00:00:00Z")
          }
        }
      }
    });

    const wkOrderResponse = buildWealthkernelOrderResponse(
      {
        id: wkOrderId,
        isin: reward.isin,
        portfolioId: "wk-portfolio-id",
        settlementCurrency: CurrencyEnum.GBP,
        side: "Buy",
        clientReference: "reward-order",
        quantity: 1,
        reason: "",
        receivedAt: new Date("2024-01-01T09:00:00Z"),
        status: "Matched"
      },
      {
        transactionId: "fill-id",
        price: { currency: CurrencyEnum.GBP, amount: 10 },
        consideration: { currency: CurrencyEnum.GBP, amount: MATCHED_ORDER_AMOUNT },
        quantity: 1,
        status: "Matched",
        filledAt: new Date("2024-01-01T10:00:00Z"),
        settlementDate: new Date("2024-01-02T10:00:00Z"),
        exchangeRate: 1,
        baseExchangeRate: 1
      }
    );

    const retrieveOrderMock = jest.fn().mockResolvedValue(wkOrderResponse);
    jest.spyOn(ProviderService, "getBrokerageService").mockReturnValue({
      retrieveOrder: retrieveOrderMock
    } as any);

    jest.spyOn(InvestmentProductService, "getInvestmentProduct").mockResolvedValue({
      tradedCurrency: "GBP"
    } as any);

    const redisDelMock = jest.fn().mockResolvedValue(undefined);
    jest.spyOn(RedisClientService, "Instance", "get").mockReturnValue({
      del: redisDelMock
    } as unknown as RedisClientService);

    await RewardService.updateRewardOrderStatus(reward, "Matched");

    const updatedReward = await Reward.findById(reward.id).lean();

    expect(updatedReward?.order?.providers?.wealthkernel?.status).toBe("Matched");
    expect(updatedReward?.order?.providers?.wealthkernel?.amount).toBe(995);
    expect(retrieveOrderMock).toHaveBeenCalledWith(wkOrderId);
  });

  it("does not call WealthKernel when the reward order is already in a terminal status", async () => {
    const user = await buildUser({ currency: "GBP" });

    const reward = await buildReward({
      targetUser: user.id,
      referrer: user.id,
      referral: user.id,
      consideration: { currency: "GBP", amount: 1000, bonusAmount: 1200, orderAmount: 1000 },
      order: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: "terminal-order-id",
            status: "Matched"
          }
        }
      }
    });

    const brokerageSpy = jest.spyOn(ProviderService, "getBrokerageService");

    await RewardService.updateRewardOrderStatus(reward, "Matched");

    expect(brokerageSpy).not.toHaveBeenCalled();

    const persistedReward = await Reward.findById(reward.id).lean();
    expect(persistedReward?.order?.providers?.wealthkernel?.amount).toBeUndefined();
    expect(persistedReward?.order?.providers?.wealthkernel?.status).toBe("Matched");
  });

  it("throws when the WealthKernel order status does not match the webhook status", async () => {
    const wkOrderId = "wk-order-id";

    const user = await buildUser({ currency: "GBP" });

    const reward = await buildReward({
      asset: "equities_apple",
      targetUser: user.id,
      referrer: user.id,
      referral: user.id,
      consideration: { currency: "GBP", amount: 1000, bonusAmount: 1200, orderAmount: 1000 },
      order: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: wkOrderId,
            status: "Pending",
            submittedAt: new Date("2024-01-01T00:00:00Z")
          }
        }
      }
    });

    const wkOrderResponse = buildWealthkernelOrderResponse({
      id: wkOrderId,
      isin: reward.isin,
      status: "Pending"
    });

    const retrieveOrderMock = jest.fn().mockResolvedValue(wkOrderResponse);
    jest.spyOn(ProviderService, "getBrokerageService").mockReturnValue({
      retrieveOrder: retrieveOrderMock
    } as any);

    await expect(RewardService.updateRewardOrderStatus(reward, "Matched")).rejects.toThrow(
      `Received WK reward order webhook with status Matched, but order in WK is ${wkOrderResponse.status}`
    );

    expect(retrieveOrderMock).toHaveBeenCalledWith(wkOrderId);

    const persistedReward = await Reward.findById(reward.id).lean();
    expect(persistedReward?.order?.providers?.wealthkernel?.status).toBe("Pending");
    expect(persistedReward?.order?.providers?.wealthkernel?.amount).toBeUndefined();
  });

  it("emits an order rejection event when the WealthKernel order is rejected", async () => {
    const wkOrderId = "wk-order-id";

    const user = await buildUser({ currency: "GBP" });

    await buildPortfolio({
      owner: user.id,
      providers: { wealthkernel: { id: "portfolio-id", status: "Active" } }
    });

    const reward = await buildReward({
      asset: "equities_apple",
      targetUser: user.id,
      referrer: user.id,
      referral: user.id,
      consideration: { currency: "GBP", amount: 1000, bonusAmount: 1200, orderAmount: 1000 },
      order: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: wkOrderId,
            status: "Pending",
            submittedAt: new Date("2024-01-01T00:00:00Z")
          }
        }
      }
    });

    const wkOrderResponse = buildWealthkernelOrderResponse(
      {
        id: wkOrderId,
        isin: reward.isin,
        status: "Rejected",
        reason: "Rejected by broker",
        fills: []
      },
      {}
    );

    const retrieveOrderMock = jest.fn().mockResolvedValue(wkOrderResponse);
    jest.spyOn(ProviderService, "getBrokerageService").mockReturnValue({
      retrieveOrder: retrieveOrderMock
    } as any);

    const emitSpy = jest.spyOn(eventEmitter, "emit").mockImplementation(() => true);

    await RewardService.updateRewardOrderStatus(reward, "Rejected");

    expect(emitSpy).toHaveBeenCalledWith(
      events.order.orderRejection.eventId,
      expect.objectContaining({ id: user.id }),
      expect.objectContaining({
        asset: reward.asset,
        amount: 10,
        currency: "GBP",
        rejectionReason: wkOrderResponse.reason,
        side: "Buy"
      })
    );

    const persistedReward = await Reward.findById(reward.id).lean();
    expect(persistedReward?.order?.providers?.wealthkernel?.status).toBe("Pending");
    expect(persistedReward?.order?.providers?.wealthkernel?.amount).toBeUndefined();
  });
});
