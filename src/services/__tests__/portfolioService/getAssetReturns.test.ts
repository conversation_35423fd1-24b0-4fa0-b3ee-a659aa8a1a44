import { faker } from "@faker-js/faker";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildInvestmentProduct,
  buildIntraDayAssetTicker,
  buildOrder,
  buildPortfolio,
  buildUser
} from "../../../tests/utils/generateModels";
import PortfolioService from "../../portfolioService";
import { KycStatusEnum, UserDocument } from "../../../models/User";
import * as Xirr from "@webcarrot/xirr";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("PortfolioService.getAssetReturns", () => {
  let user: UserDocument;

  beforeAll(async () => {
    await connectDb("getAssetReturns");
    user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
  });
  afterAll(async () => {
    await closeDb();
  });
  beforeEach(async () => jest.restoreAllMocks());
  afterEach(async () => await clearDb());

  it("should return 0 when the current price is same as the average price per share and XIRR fails", async () => {
    jest.spyOn(Xirr, "xirr").mockImplementation(() => {
      throw new Error("XIRR failed!");
    });

    const PRICE = 15;
    const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_eu" });
    const portfolio = await buildPortfolio({
      owner: user.id,
      holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_eu", quantity: 6 }]
    });
    expect(ASSET_CONFIG["equities_eu"]?.tradedCurrency).toBe("GBP");
    await buildIntraDayAssetTicker({
      investmentProduct: investmentProduct?.id,
      pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
    });
    await Promise.all([
      investmentProduct.populate("currentTicker"),
      portfolio.populate([
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ])
    ]);

    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      },
      {
        // price => 20 / 1 = 20
        amount: 20 * 100,
        quantity: 1,
        exchangeRate: 1
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_eu"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const assetReturns = await PortfolioService.getAssetReturns(portfolio, portfolio.holdings[0], {
      dividendTransactions: [],
      rewards: []
    });
    // 15/15 - 1
    expect(assetReturns).toBeCloseTo(0, 4);
  });

  it("should return positive performance when the asset price is larger than the average price per share and XIRR fails", async () => {
    jest.spyOn(Xirr, "xirr").mockImplementation(() => {
      throw new Error("XIRR failed!");
    });

    const PRICE = 22.5;
    const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_eu" });
    const portfolio = await buildPortfolio({
      owner: user.id,
      holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_eu", quantity: 6 }]
    });
    expect(ASSET_CONFIG["equities_eu"]?.tradedCurrency).toBe("GBP");
    await buildIntraDayAssetTicker({
      investmentProduct: investmentProduct?.id,
      pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
    });
    await Promise.all([
      investmentProduct.populate("currentTicker"),
      portfolio.populate([
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ])
    ]);

    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      },
      {
        // price => 20 / 1 = 20
        amount: 20 * 100,
        quantity: 1
        // here we intentionally leave exchange rate empty (it will use fallback of 1)
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_eu"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const assetReturns = await PortfolioService.getAssetReturns(portfolio, portfolio.holdings[0], {
      dividendTransactions: [],
      rewards: []
    });
    // 22.5/15 - 1
    expect(assetReturns).toBe(0.5);
  });

  it("should return negative performance when the asset price is larger than the average price per share and XIRR fails", async () => {
    jest.spyOn(Xirr, "xirr").mockImplementation(() => {
      throw new Error("XIRR failed!");
    });

    const PRICE = 7.5;
    const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_eu" });
    const portfolio = await buildPortfolio({
      owner: user.id,
      holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_eu", quantity: 6 }]
    });
    expect(ASSET_CONFIG["equities_eu"]?.tradedCurrency).toBe("GBP");
    await buildIntraDayAssetTicker({
      investmentProduct: investmentProduct?.id,
      pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
    });
    await Promise.all([
      investmentProduct.populate("currentTicker"),
      portfolio.populate([
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ])
    ]);

    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      },
      {
        // price => 20 / 1 = 20
        amount: 20 * 100,
        quantity: 1,
        exchangeRate: 1
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_eu"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const assetReturns = await PortfolioService.getAssetReturns(portfolio, portfolio.holdings[0], {
      dividendTransactions: [],
      rewards: []
    });
    // 7.5/15 - 1
    expect(assetReturns).toBe(-0.5);
  });

  it("should take into account the traded price when XIRR fails", async () => {
    jest.spyOn(Xirr, "xirr").mockImplementation(() => {
      throw new Error("XIRR failed!");
    });

    const PRICE = 30;
    const USD_PRICE = PRICE * 1.2;
    const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });
    const portfolio = await buildPortfolio({
      owner: user.id,
      holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_apple", quantity: 6 }]
    });
    expect(ASSET_CONFIG["equities_apple"]?.tradedCurrency).toBe("USD");
    await buildIntraDayAssetTicker({
      investmentProduct: investmentProduct?.id,
      pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: USD_PRICE }
    });
    await Promise.all([
      investmentProduct.populate("currentTicker"),
      portfolio.populate([
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ])
    ]);

    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      },
      {
        // price => 20 / 1 = 20
        amount: 20 * 100,
        quantity: 1
        // here we intentionally leave exchange rate empty (it will use fallback of 1)
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const assetReturns = await PortfolioService.getAssetReturns(portfolio, portfolio.holdings[0], {
      dividendTransactions: [],
      rewards: []
    });
    // returns = 30/15 -1
    // if we wanted returns based on traded currency that would be: 30/15 * 1.2 -1
    expect(assetReturns).toBe(1);
  });

  it("should take into account orders with deprecated isin when XIRR fails", async () => {
    jest.spyOn(Xirr, "xirr").mockImplementation(() => {
      throw new Error("XIRR failed!");
    });

    const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro";
    const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro_deprecated_1";
    const PRICE = 22.5;
    await buildInvestmentProduct(false, { assetId: DEPRECATED_ASSET_ID });
    const investmentProduct = await buildInvestmentProduct(false, { assetId: ACTIVE_ASSET_ID });
    const portfolio = await buildPortfolio({
      owner: user.id,
      holdings: [{ asset: investmentProduct.id, assetCommonId: ACTIVE_ASSET_ID, quantity: 6 }]
    });

    await buildIntraDayAssetTicker({
      investmentProduct: investmentProduct?.id,
      pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
    });
    await Promise.all([
      investmentProduct.populate("currentTicker"),
      portfolio.populate([
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ])
    ]);

    const assetTransaction = await buildAssetTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      status: "Settled"
    });

    // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
    const orderConfig = [
      {
        // price => 30 / 3 = 10
        amount: 30 * 100,
        quantity: 3,
        exchangeRate: 1
      },
      {
        // price => 40 / 2 = 20
        amount: 40 * 100,
        quantity: 2,
        exchangeRate: 1
      },
      {
        // price => 20 / 1 = 20
        amount: 20 * 100,
        quantity: 1
        // here we intentionally leave exchange rate empty (it will use fallback of 1)
      }
    ];
    assetTransaction.orders = await Promise.all(
      orderConfig.map(({ amount, quantity, exchangeRate }) =>
        buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID]?.isin,
          consideration: {
            amountSubmitted: amount,
            amount,
            currency: "GBP"
          },
          quantity,
          exchangeRate
        })
      )
    );
    await assetTransaction.save();
    await assetTransaction.populate("orders");

    const assetReturns = await PortfolioService.getAssetReturns(portfolio, portfolio.holdings[0], {
      dividendTransactions: [],
      rewards: []
    });
    // 22.5/15 - 1
    expect(assetReturns).toBe(0.5);
  });
});
