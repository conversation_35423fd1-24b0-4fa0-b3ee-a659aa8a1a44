import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PortfolioDocument, PortfolioModeEnum } from "../../../models/Portfolio";
import { connectDb, closeDb, clearDb } from "../../../tests/utils/db";
import { buildUser, buildPortfolio, buildHoldingDTO, buildReward } from "../../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import { ProviderEnum } from "../../../configs/providersConfig";
import DateUtil from "../../../utils/dateUtil";
import PortfolioService from "../../portfolioService";

/**
 * Creates a user and a portfolio based on the given arguments. The number of holdings in the portfolio will be 5, which also makes it the maximum number of rewards.
 * @param countOfRewards The number of rewards that the portfolio of the user should have (Maximum 5).
 * @param percentageOfHoldingQuantitiesAsReward The percentage of the holding quantities that should be given as a reward quantity for each holding (0 to 1). Default is 1
 * @param countOfRewardsToApplyQuantityPercentageOn The number of rewards that the `percentageOfHoldingQuantitiesAsReward` should be applied on (Maximum 5). Default is 0
 * @returns The created portfolio of the user with 5 holdings and the requested rewards
 */
const createPortfolio = async (
  countOfRewards: number,
  percentageOfHoldingQuantitiesAsReward?: number,
  countOfRewardsToApplyQuantityPercentageOn?: number
): Promise<PortfolioDocument> => {
  const ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
    "equities_global_clean_energy",
    "equities_eu",
    "real_estate_uk",
    "equities_us",
    "equities_global"
  ];

  const user = await buildUser({ portfolioConversionStatus: "completed" });
  // The user has £10 of each holding, with a quantity of 1
  const holdings = await Promise.all(
    ASSET_COMMON_IDS.map((assetId) =>
      buildHoldingDTO(true, assetId, 1, {
        price: 10
      })
    )
  );
  const portfolio = await buildPortfolio({
    owner: user.id,
    cash: { GBP: { available: 100, reserved: 0, settled: 90 } },
    providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
    mode: PortfolioModeEnum.REAL,
    holdings
  });

  for (let i = 0; i < countOfRewards; i++) {
    const holding = holdings[i];

    let rewardQuantity;
    if (percentageOfHoldingQuantitiesAsReward && countOfRewardsToApplyQuantityPercentageOn) {
      rewardQuantity =
        countOfRewardsToApplyQuantityPercentageOn > i
          ? holding.quantity * percentageOfHoldingQuantitiesAsReward
          : holding.quantity;
    } else {
      rewardQuantity = holding.quantity;
    }

    await buildReward({
      targetUser: user.id,
      asset: holding.assetCommonId,
      quantity: rewardQuantity,
      updatedAt: new Date(),
      status: "Settled",
      accepted: true,
      unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), -30),
      deposit: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      },
      order: {
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched"
          }
        }
      }
    });
  }

  return portfolio;
};

describe("PortfolioService.onlyHasRewardedHoldings", () => {
  beforeAll(async () => {
    await connectDb("onlyHasRewardedHoldings");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("when user has only rewarded holdings", () => {
    let portfolio: PortfolioDocument;

    beforeAll(async () => {
      portfolio = await createPortfolio(5);
    });

    it("should return that the user has only rewarded holdings", async () => {
      const result = await PortfolioService.onlyHasRewardedHoldings(portfolio);

      expect(result).toBe(true);
    });
  });

  describe("when user has only non-rewarded holdings", () => {
    let portfolio: PortfolioDocument;

    beforeAll(async () => {
      portfolio = await createPortfolio(0);
    });

    it("should return that the user does not have only rewarded holdings", async () => {
      const result = await PortfolioService.onlyHasRewardedHoldings(portfolio);

      expect(result).toBe(false);
    });
  });

  describe("when user has only rewarded holdings but has invested further on all of them", () => {
    let portfolio: PortfolioDocument;

    beforeAll(async () => {
      portfolio = await createPortfolio(5, 0.5, 5);
    });

    it("should return that the user does not have only rewarded holdings", async () => {
      const result = await PortfolioService.onlyHasRewardedHoldings(portfolio);

      expect(result).toBe(false);
    });
  });

  describe("when user has only rewarded holdings but has invested further on some of them", () => {
    let portfolio: PortfolioDocument;

    beforeAll(async () => {
      portfolio = await createPortfolio(5, 0.5, 2);
    });

    it("should return that the user does not have only rewarded holdings", async () => {
      const result = await PortfolioService.onlyHasRewardedHoldings(portfolio);

      expect(result).toBe(false);
    });
  });

  describe("when user has both rewarded and non-rewarded holdings", () => {
    let portfolio: PortfolioDocument;

    beforeAll(async () => {
      portfolio = await createPortfolio(3);
    });

    it("should return that the user does not have only rewarded holdings", async () => {
      const result = await PortfolioService.onlyHasRewardedHoldings(portfolio);

      expect(result).toBe(false);
    });
  });
});
