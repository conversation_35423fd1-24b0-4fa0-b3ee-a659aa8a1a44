import { captureException } from "@sentry/node";
import Decimal from "decimal.js";
import ScriptRunner from "../../jobs/services/scriptRunner";
import logger from "../../external-services/loggerService";
import { Reward } from "../../models/Reward";
import OrderService from "../../services/orderService";
import { WealthkernelService, CurrencyEnum } from "../../external-services/wealthkernelService";

const MODULE = "script:rewards:backfill-wealthkernel-order-amount";
const BATCH_SIZE = 10;

class BackfillRewardWealthkernelAmountScript extends ScriptRunner {
  scriptName = "rewards:backfill-wealthkernel-order-amount";

  public async processFn(): Promise<void> {
    const cursor = Reward.find({
      "order.providers.wealthkernel.status": { $in: ["Matched", "Settled"] },
      $or: [
        { "order.providers.wealthkernel.amount": { $exists: false } },
        { "order.providers.wealthkernel.amount": null }
      ],
      "order.providers.wealthkernel.id": { $exists: true, $ne: null }
    })
      .lean()
      .cursor();

    let processed = 0;
    let updated = 0;

    const processBatch = async (batch: any[]) => {
      if (batch.length === 0) {
        return;
      }

      const results = await Promise.all(
        batch.map(async (reward) => {
          const rewardId = reward._id.toString();
          const wealthkernelOrderId = reward.order?.providers?.wealthkernel?.id;

          if (!wealthkernelOrderId) {
            logger.warn(`Skipping reward ${rewardId} because the WealthKernel order id is missing`, {
              module: MODULE
            });
            return false;
          }

          try {
            const wealthkernelService =
              reward.consideration?.currency === CurrencyEnum.EUR
                ? WealthkernelService.EUInstance
                : WealthkernelService.UKInstance;

            const wkOrder = await wealthkernelService.retrieveOrder(wealthkernelOrderId);

            if (!wkOrder) {
              logger.warn(`Could not retrieve WealthKernel order ${wealthkernelOrderId} for reward ${rewardId}`, {
                module: MODULE
              });
              return false;
            }

            const matchedAmount = OrderService.calculateMatchedOrderAmount(wkOrder);
            const amountInCents = Decimal.mul(matchedAmount, 100).toDecimalPlaces(0).toNumber();

            await Reward.updateOne(
              { _id: reward._id },
              {
                "order.providers.wealthkernel.amount": amountInCents
              }
            );

            logger.info(`Backfilled WealthKernel amount for reward ${rewardId} with ${amountInCents} cents`, {
              module: MODULE,
              data: { rewardId, wealthkernelOrderId, amountInCents }
            });

            return true;
          } catch (error) {
            captureException(error);
            logger.error(`Failed to backfill WealthKernel amount for reward ${rewardId}`, {
              module: MODULE,
              data: {
                rewardId,
                wealthkernelOrderId,
                error: error instanceof Error ? error.message : error
              }
            });
            return false;
          }
        })
      );

      processed += batch.length;
      updated += results.filter(Boolean).length;
    };

    let batch: any[] = [];

    for await (const reward of cursor) {
      batch.push(reward);

      if (batch.length === BATCH_SIZE) {
        await processBatch(batch);
        batch = [];
      }
    }

    if (batch.length > 0) {
      await processBatch(batch);
    }

    logger.info(`Backfill completed. Processed ${processed} rewards and updated ${updated} of them.`, {
      module: MODULE,
      data: { processed, updated }
    });
  }
}

new BackfillRewardWealthkernelAmountScript().run();
