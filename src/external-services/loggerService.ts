import "../loaders/environment";
import winston, { createLogger, format, transports } from "winston";
import { captureException } from "@sentry/node";
import { envIsDev } from "../utils/environmentUtil";
import { SENSITIVE_KEYS } from "../configs/headerConfig";
import util from "util";

const HTTP_TRANSPORT_OPTIONS = {
  host: "http-intake.logs.datadoghq.eu",
  path: `/v1/input/${process.env.DATADOG_API_KEY}?ddsource=nodejs&service=wealthyhood-app-api-${process.env.NODE_ENV}`,
  ssl: true
};

type LoggerOptionsType = {
  userEmail?: string;
  module?: string;
  method?: string;
  successStep?: boolean;
  data?: any;
};

class Logger {
  private _logger: winston.Logger;
  constructor() {
    this._logger = createLogger({
      level: "info",
      format: format.json(),
      exitOnError: false,
      transports: [new transports.Http(HTTP_TRANSPORT_OPTIONS)]
    });

    if (envIsDev()) {
      this._logger.add(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.uncolorize(), // Remove any existing colors first
            winston.format.colorize(),
            winston.format.timestamp(),
            winston.format.printf((info) => {
              // Extract the main parts
              const { timestamp, level, message } = info;

              // Get all metadata (everything except winston's internal fields)
              const meta = { ...info };
              delete meta.timestamp;
              delete meta.level;
              delete meta.env;
              delete meta.message;
              delete meta[Symbol.for("message")]; // Remove winston's internal message symbol
              delete meta[Symbol.for("level")];
              delete meta[Symbol.for("splat")];

              // Format metadata if it exists
              const metaStr = Object.keys(meta).length
                ? "\n" + util.inspect(meta, { depth: 4, colors: true })
                : "";

              return `${timestamp} [${level}]: ${message}${metaStr}`;
            })
          )
        })
      );
    }

    this._logger.on("error", (err) => {
      captureException(err);
    });
  }

  public info(message: string, options?: LoggerOptionsType): void {
    this._logger.info(message, {
      env: process.env.NODE_ENV,
      ...filterSensitiveValues(options)
    });
  }

  public warn(message: string, options?: LoggerOptionsType): void {
    this._logger.warn(message, {
      env: process.env.NODE_ENV,
      ...filterSensitiveValues(options)
    });
  }

  public error(message: string, options?: LoggerOptionsType): void {
    const optionsWithSanitizedError = {
      ...options,
      data: options ? sanitizeError(options.data) : {}
    };

    this._logger.error(message, {
      env: process.env.NODE_ENV,
      ...filterSensitiveValues(optionsWithSanitizedError)
    });
  }
}

/**
 * This function filters out any keys we want to avoid logging by replacing their value
 * with '***'. For example, if the array contains "authorization", any authorization
 * token values will be replaced by '***' before being sent to our logging destination.
 */
function filterSensitiveValues(obj: any): any {
  if (!obj) {
    return {};
  }

  let stringifiedObject = JSON.stringify(obj);

  SENSITIVE_KEYS.forEach((key) => {
    stringifiedObject = stringifiedObject.replace(new RegExp(`"${key}":"(.*?)"`), `"${key}":"***"`);
  });

  return JSON.parse(stringifiedObject);
}

/**
 * We convert the error field, if there is one in data to be a string.
 * @param obj
 */
function sanitizeError(obj: any): any {
  if (!obj) {
    return {};
  } else if (!obj.error) {
    return obj;
  }

  return {
    ...obj,
    error: typeof obj.error.toString === "function" ? obj.error.toString() : obj.error
  };
}

const logger = new Logger();

export default logger;
