import { ContentEntryDocument } from "../models/ContentEntry";
import { BankAccount, BankAccountDocument, WealthyhoodBankAccountStatusType } from "../models/BankAccount";
import { UserDocument } from "../models/User";

export default class BankAccountRepository {
  public static async updateWealthyhoodStatus(
    id: string,
    status: WealthyhoodBankAccountStatusType
  ): Promise<ContentEntryDocument | null> {
    return BankAccount.findByIdAndUpdate(id, { "providers.wealthyhood.status": status }, { new: true });
  }

  /**
   * Gets bank account by ID.
   *
   * @param id
   */
  public static async getBankAccountById(id: string): Promise<BankAccountDocument> {
    return BankAccount.findById(id);
  }

  /**
   * Gets bank account by IBAN and user.
   *
   * @param iban
   */
  public static async getBankAccountByIBAN(iban: string, user: UserDocument): Promise<BankAccountDocument> {
    return BankAccount.findOne({ iban, owner: user.id });
  }
}
