import mongoose from "mongoose";
import {
  DepositCashTransaction,
  DepositCashTransactionDocument,
  WithdrawalCashTransaction,
  WithdrawalCashTransactionInterface
} from "../models/Transaction";
import { DepositMethodEnum, WithdrawalMethodEnum } from "../types/transactions";
import DateUtil from "../utils/dateUtil";
import logger from "../external-services/loggerService";
import { captureMessage } from "@sentry/node";

export class TransactionRepository {
  public static async getDepositCashTransaction(id: string): Promise<DepositCashTransactionDocument> {
    return DepositCashTransaction.findById(id);
  }

  /**
   * Retrieves a withdrawal transaction by matching its bank reference within a description string.
   *
   * This method performs a case-insensitive search to find withdrawal transactions where the bank reference is
   * contained anywhere within the provided description.

   * @example
   * If a withdrawal in DB has bankReference "ABC123"
   * This would match even with description "Payment from Wealthkernel Ltd: ABC123"
   */
  public static async getPendingWithdrawalWithIntermediaryByContainedBankReference(
    description: string
  ): Promise<WithdrawalCashTransactionInterface & { _id: mongoose.Types.ObjectId }> {
    const lowercaseDescription = description.toLowerCase();

    const matches = await WithdrawalCashTransaction.aggregate([
      {
        $match: {
          bankReference: { $ne: null },
          withdrawalMethod: WithdrawalMethodEnum.WITH_INTERMEDIARY,
          "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": {
            $nin: ["confirmed", "rejected", "canceled", "reversed"]
          }
        }
      },
      {
        $addFields: {
          lowercaseBankReference: { $toLower: "$bankReference" },
          lowercaseDescription: lowercaseDescription
        }
      },
      {
        $match: {
          $expr: {
            $regexMatch: {
              input: "$lowercaseDescription",
              regex: "$lowercaseBankReference"
            }
          }
        }
      }
    ]);

    if (matches.length > 1) {
      throw new Error(`We have received more than 1 match for Devengo withdrawal with description ${description}`);
    }

    return matches.length > 0 ? matches[0] : null;
  }

  public static async getPendingBankTransferDepositsForBankAccount(
    bankAccountId: string
  ): Promise<DepositCashTransactionDocument[]> {
    return DepositCashTransaction.find({
      bankAccount: bankAccountId,
      status: "Pending",
      depositMethod: DepositMethodEnum.BANK_TRANSFER,
      "providers.wealthkernel.status": { $ne: "Settled" },
      "providers.wealthkernel.id": { $exists: true }
    });
  }

  /**
   * Fallback matching for withdrawals received in the Devengo payouts account when description reference matching fails.
   *
   * Matches by exact consideration amount and creation date within last N working days.
   * Ensures the withdrawal is not already matched to an incoming payment.
   *
   * Returns null if no match or more than one match is found.
   */
  public static async getUnmatchedWithdrawalWithIntermediaryByAmount(
    amountInCents: number,
    maxWorkDaysAgo = 3
  ): Promise<(WithdrawalCashTransactionInterface & { _id: mongoose.Types.ObjectId }) | null> {
    const thresholdDate = DateUtil.getDateNWorkDaysAgo(new Date(), maxWorkDaysAgo);

    const matches = await WithdrawalCashTransaction.find({
      "consideration.amount": amountInCents,
      withdrawalMethod: WithdrawalMethodEnum.WITH_INTERMEDIARY,
      "providers.wealthkernel.status": "Settled",
      createdAt: { $gte: thresholdDate },
      transferWithIntermediary: { $exists: false }
    });

    if (matches.length > 1) {
      logger.error(`We have received more than 1 match for withdrawal with the same amount`, {
        module: "TransactionRepository",
        method: "getUnmatchedWithdrawalWithIntermediaryByAmount",
        data: {
          transactions: matches.map((match) => {
            return {
              transactionId: match.id,
              bankReference: match.bankReference
            };
          })
        }
      });
      captureMessage(`We have received more than 1 match for withdrawal with the same amount`, {
        level: "error",
        extra: {
          transactions: matches.map((match) => {
            return {
              transactionId: match.id,
              bankReference: match.bankReference
            };
          })
        }
      });
    }

    return matches?.length === 1 ? matches[0] : null;
  }
}
