import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildUser } from "../../tests/utils/generateModels";
import { UserRepository } from "../userRepository";

describe("UserRepository", () => {
  beforeAll(async () => {
    await connectDb("UserRepository");
  });
  afterAll(async () => {
    await clearDb();
    await closeDb();
  });

  describe("getUsersByTaxResidencyValues", () => {
    describe("when none of the users with tax residency value exist", () => {
      it("should return an empty array", async () => {
        const result = await UserRepository.getUsersByTaxResidencyValues(["invalid-value-1", "invalid-value-2"]);

        expect(result).toEqual([]);
      });
    });

    describe("when some users with tax residency value exist", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser();
      });

      it("should return an array with one document", async () => {
        const result = await UserRepository.getUsersByTaxResidencyValues([
          user.taxResidency.value,
          "invalid-value-2"
        ]);

        expect(result.length).toBe(1);
        expect(result[0].taxResidency.value).toBe(user.taxResidency.value);
      });
    });
  });
});
