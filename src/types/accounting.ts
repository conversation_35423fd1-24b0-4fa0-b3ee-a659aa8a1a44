export enum LedgerAccounts {
  // Client Accounts
  CLIENT_DOMESTIC = "30-00-00-0000", // Πελάτης Εσωτερικού (Greek resident)
  CLIENT_EU_EEA = "30-01-00-0000", // Πελάτης Ζώνης Ευρώ (EU/EEA resident excluding Greece)
  CLIENT_INTERNATIONAL = "30-02-00-0000", // Πελάτης Λοιπές Χώρες (Rest-of-World resident)

  // Omnibus + Intermediary + Bonus Accounts
  INTERMEDIARY_DEPOSITS_1 = "38-03-00-0001", // Intermediary Deposits #1
  INTERMEDIARY_DEPOSITS_2 = "38-03-00-0002", // Intermediary Deposits #2
  INTERMEDIARY_WITHDRAWALS = "38-03-00-0003", // Intermediary Withdrawals
  CLIENTS_ACCOUNTS_OMNIBUS = "38-03-02-0000", // Clients Accounts (Omnibus)
  BONUS_ACCOUNT = "38-03-00-0006", // Wealthyhood Bonus Account
  BONUS_EXPENSE = "64-10-00-0000", // Wealthyhood Bonus Expense

  // Asset Accounts
  ASSETS_ACTIVE = "01-99-00-0000", // Assets – Active
  ASSETS_PASSIVE = "05-99-00-0000", // Assets – Passive

  // Broker Payable + Expense Accounts
  PAYABLES_TO_BROKER = "53-99-00-0000", // Payables to Broker
  BROKER_FEE_EXPENSE = "63-05-01-0001", // Broker Fee Expense

  // Income Fees Accounts
  CUSTODY_FEES_WH = "38-03-00-0004", // Custody Fees Wealthyhood
  COMMISSION_FEES_WH = "73-00-00-0000", // Wealthyhood Commission Fees
  MMF_DIVIDEND_FEES_WH = "76-03-00-0000" // MMF Dividend Fees Wealthyhood
}

export enum AccountingClientSegment {
  DOMESTIC_GR = "DOMESTIC_GR", // Greek resident
  EU_EEA_EXCLUDING_GR = "EU_EEA_EXCLUDING_GR", // EU/EEA resident (excluding Greece)
  REST_OF_WORLD = "REST_OF_WORLD" // Rest-of-World resident
}

// Event type values for record description
export enum AccountingEventType {
  BANK_TRANSACTION_DEPOSIT = "bank transaction (deposit)",
  BANK_TRANSACTION_WITHDRAWAL = "bank transaction (withdrawal)",
  CUSTODY_FEE = "custody fee",
  COMMISSION_FEE = "commission fee", // General commission
  BROKER_FEE = "broker fee",
  ASSET_DIVIDEND = "asset dividend", // For stock & MMF dividends
  ASSET_DIVIDEND_COMMISSION = "asset dividend commission",
  MMF_DIVIDEND_COMMISSION = "MMF dividend commission",
  ASSET_BUY = "asset buy",
  ASSET_SELL = "asset sell",
  BONUS = "bank transaction (bonus)"
}

export interface AccountingEntry {
  account: LedgerAccounts;
  amount: number; // in cents
  type: "debit" | "credit";
}

// Customer configuration mapping for XML export (only client accounts)
export const ACCOUNTING_CUSTOMER_CONFIG: Record<
  LedgerAccounts.CLIENT_DOMESTIC | LedgerAccounts.CLIENT_EU_EEA | LedgerAccounts.CLIENT_INTERNATIONAL,
  {
    id: string;
    name: string;
    vat: string;
    doyCode: string;
    isKepyo: string;
  }
> = {
  [LedgerAccounts.CLIENT_DOMESTIC]: {
    id: "11263",
    name: "ΠΕΛΑΤΕΣ ΕΣΩΤΕΡΙΚΟΥ ΧΟΝΔΡΙΚΗΣ",
    vat: "*********",
    doyCode: "",
    isKepyo: "0"
  },
  [LedgerAccounts.CLIENT_EU_EEA]: {
    id: "11665",
    name: "ΠΕΛΑΤΕΣ ΕΕ",
    vat: "*********",
    doyCode: "",
    isKepyo: "0"
  },
  [LedgerAccounts.CLIENT_INTERNATIONAL]: {
    id: "11666",
    name: "ΠΕΛΑΤΕΣ ΤΡΙΤΩΝ ΧΩΡΩΝ",
    vat: "*********",
    doyCode: "",
    isKepyo: "0"
  }
};
