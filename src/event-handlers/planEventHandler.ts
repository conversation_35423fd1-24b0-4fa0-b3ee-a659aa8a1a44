import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import analytics, { TrackPlanType } from "../external-services/segmentAnalyticsService";
import logger from "../external-services/loggerService";
import SubmissionTechEventService, {
  SubmissionTechEventEnum
} from "../external-services/submissionTechEventService";
import DbUtil from "../utils/dbUtil";
import { plansConfig } from "@wealthyhood/shared-configs";

const { getPlanConfig } = plansConfig;

class PlanEventHandler {
  constructor() {
    eventEmitter.on(events.plan.planDowngradeInit.eventId, this._handlePlanDowngradeInit.bind(this));
    eventEmitter.on(events.plan.planDowngradeCompletion.eventId, this._handlePlanDowngradeCompletion.bind(this));
    eventEmitter.on(events.plan.planUpgrade.eventId, this._handlePlanUpgrade.bind(this));
  }

  private async _handlePlanDowngradeInit(user: UserDocument, properties: TrackPlanType): Promise<void> {
    logger.info(`Plan downgrade initialised for user ${user.email} from ${properties.from} to ${properties.to}`, {
      module: "PlanEventHandler",
      method: "_handlePlanDowngradeInit"
    });

    const PLAN_CONFIG = getPlanConfig(user.companyEntity);

    analytics.track(
      user,
      events.plan.planDowngradeInit.name,
      { All: false, Mixpanel: true, Slack: true, SlackActions: true },
      {
        ...properties,
        category: "downgrade",
        displayFrom: properties.from ? PLAN_CONFIG[properties.from].name : "Basic",
        displayTo: PLAN_CONFIG[properties.to].name
      }
    );
  }

  private async _handlePlanDowngradeCompletion(user: UserDocument, properties: TrackPlanType): Promise<void> {
    logger.info(`Plan downgrade completed for user ${user.email} from ${properties.from} to ${properties.to}`, {
      module: "PlanEventHandler",
      method: "_handlePlanDowngradeCompletion"
    });
    const plan = properties.to;
    analytics.identify(user, { plan }, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
  }

  private async _handlePlanUpgrade(user: UserDocument, properties: TrackPlanType): Promise<void> {
    logger.info(`Plan updated for user ${user.email} from ${properties.from} to ${properties.to}`, {
      module: "PlanEventHandler",
      method: "_handlePlanUpgrade"
    });

    const PLAN_CONFIG = getPlanConfig(user.companyEntity);

    const plan = properties.to;
    const planRecurrence = properties.toRecurrence;

    analytics.identify(
      user,
      { plan, planRecurrence },
      { All: false, MailChimp: true, Mixpanel: true, Intercom: true }
    );
    analytics.track(
      user,
      events.plan.planUpgrade.name,
      { All: false, Mixpanel: true, Slack: true, SlackActions: true },
      {
        ...properties,
        category: "upgrade",
        displayFrom: properties.from ? PLAN_CONFIG[properties.from].name : "Basic",
        displayTo: PLAN_CONFIG[properties.to].name
      }
    );

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.PARTICIPANT);
    if (user.participant?.metadata?.submissionTech?.clickId) {
      switch (plan) {
        case "paid_low":
          await SubmissionTechEventService.trackEvent(
            SubmissionTechEventEnum.PLUS_SUBSCRIPTION,
            user.participant?.metadata?.submissionTech?.clickId
          );
          break;
        case "paid_mid":
          await SubmissionTechEventService.trackEvent(
            SubmissionTechEventEnum.GOLD_SUBSCRIPTION,
            user.participant?.metadata?.submissionTech?.clickId
          );
          break;
      }
    }
  }
}

export default PlanEventHandler;
