import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import GoogleAnalyticsService, { EventActionEnum } from "../external-services/googleAnalyticsService";
import analytics, {
  TrackAssetDividendSuccessPropertiesType,
  TrackCustodyChargeSuccess,
  TrackDepositPropertiesType,
  TrackDirectDebitDepositChargebackPropertiesType,
  TrackNotificationDividendSuccess,
  TrackNotificationOrderSettled,
  TrackNotificationSavingsDividendCreation,
  TrackNotificationWealthyhoodDividendSuccess,
  TrackSavingsDividendChargeSuccess,
  TrackSubscriptionChargeSuccess,
  TrackTransactionInfoType,
  TrackWealthyhoodDividendPropertiesType,
  TrackWithdrawalPropertiesType,
  UserTraitType
} from "../external-services/segmentAnalyticsService";
import logger from "../external-services/loggerService";
import FacebookAppEventService from "../external-services/facebookAppEventService";
import FinanceAdsService from "../external-services/financeAdsService";
import Decimal from "decimal.js";
import { TransactionalNotificationEventEnum } from "./notificationEvents";
import { currenciesConfig, dividendsConfig, rewardsConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../utils/configUtil";
import CurrencyUtil from "../utils/currencyUtil";
import NotificationService from "../services/notificationService";

const { WEALTHYHOOD_DIVIDEND_RATES } = dividendsConfig;
const { MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY } = rewardsConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;

class TransactionEventHandler {
  constructor() {
    eventEmitter.on(events.transaction.firstDepositCreation.eventId, this._handleFirstDepositCreation.bind(this));
    eventEmitter.on(events.transaction.depositCreation.eventId, this._handleDepositCreation.bind(this));
    eventEmitter.on(events.transaction.depositFailure.eventId, this._handleDepositFailure.bind(this));
    eventEmitter.on(events.transaction.depositSuccess.eventId, this._handleDepositSuccess.bind(this));
    eventEmitter.on(events.transaction.depositAvailable.eventId, this._handleDepositAvailable.bind(this));
    eventEmitter.on(
      events.transaction.directDebitDepositChargeback.eventId,
      this._handleDirectDebitDepositChargeback.bind(this)
    );
    eventEmitter.on(events.transaction.investmentCreation.eventId, this._handleInvestmentCreation.bind(this));
    eventEmitter.on(
      events.transaction.firstInvestmentCreation.eventId,
      this._handleFirstInvestmentCreation.bind(this)
    );
    eventEmitter.on(events.transaction.investmentSuccess.eventId, this._handleAssetTransactionSuccess.bind(this));
    eventEmitter.on(events.transaction.withdrawalCreation.eventId, this._handleWithdrawalCreation.bind(this));
    eventEmitter.on(
      events.transaction.rebalanceTransactionSuccess.eventId,
      this._handleRebalanceTransactionSuccess.bind(this)
    );
    eventEmitter.on(
      events.transaction.savingsDividendCreation.eventId,
      this._handleSavingsDividendCreation.bind(this)
    );
    eventEmitter.on(events.transaction.dividendSuccess.eventId, this._handleDividendSuccess.bind(this));
    eventEmitter.on(events.transaction.assetDividendSuccess.eventId, this._handleAssetDividendSuccess.bind(this));
    eventEmitter.on(
      events.transaction.wealthyhoodDividendSuccess.eventId,
      this._handleWealthyhoodDividendSuccess.bind(this)
    );
    eventEmitter.on(events.transaction.custodyChargeSuccess.eventId, this._handleCustodyChargeSuccess.bind(this));
    eventEmitter.on(
      events.transaction.subscriptionChargeSuccess.eventId,
      this._handleSubscriptionChargeSuccess.bind(this)
    );
    eventEmitter.on(
      events.transaction.savingsDividendChargeSuccess.eventId,
      this._handleSavingsDividendChargeSuccess.bind(this)
    );
  }

  private async _handleFirstDepositCreation(user: UserDocument): Promise<void> {
    logger.info(`First deposit created for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleFirstDepositCreation"
    });
    analytics.track(user, events.transaction.firstDepositCreation.name);
    await GoogleAnalyticsService.trackEventGA4({
      user,
      event: EventActionEnum.FIRST_DEPOSIT_CREATED
    });
  }

  private async _handleDepositCreation(
    user: UserDocument,
    properties: { isFirst: boolean } & TrackDepositPropertiesType,
    options: {
      triggeredByAutomation?: boolean;
    } = { triggeredByAutomation: false }
  ): Promise<void> {
    logger.info(`Deposit created for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleDepositCreation",
      data: properties
    });
    analytics.identify(
      user,
      { status: events.transaction.depositCreation.name },
      {
        All: false,
        MailChimp: !options.triggeredByAutomation,
        Mixpanel: true,
        Intercom: !options.triggeredByAutomation
      }
    );
    analytics.track(user, events.transaction.depositCreation.name, { Slack: !properties.isFirst }, properties);
    await Promise.allSettled([
      GoogleAnalyticsService.trackEventGA4({
        user,
        event: EventActionEnum.DEPOSIT_CREATED
      }),
      FacebookAppEventService.trackEvent(events.transaction.depositCreation.name, user.lastLoginPlatform, {
        email: user.email
      })
    ]);
  }

  private async _handleDepositFailure(user: UserDocument): Promise<void> {
    logger.info(`Deposit failed for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleDepositFailure"
    });

    await NotificationService.createAppNotification(
      user.id,
      { notificationId: TransactionalNotificationEventEnum.DEPOSIT_FAILURE },
      { sendImmediately: true }
    );
  }

  private async _handleDepositSuccess(user: UserDocument, properties: TrackDepositPropertiesType): Promise<void> {
    logger.info(`Deposit succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleDepositSuccess",
      data: properties
    });

    const { noAssetTransactionPending } = properties;

    if (noAssetTransactionPending) {
      analytics.identify(
        user,
        { status: events.transaction.depositSuccess.name },
        { All: false, MailChimp: true, Mixpanel: true, Intercom: true }
      );
      analytics.track(
        user,
        events.transaction.depositSuccess.name,
        {},
        { ...properties, displayCurrencySymbol: CURRENCY_SYMBOLS[properties.currency] }
      );
    }
  }

  private async _handleDepositAvailable(
    user: UserDocument,
    properties: TrackDepositPropertiesType
  ): Promise<void> {
    logger.info(`Deposit available for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleDepositAvailable",
      data: properties
    });

    const { noAssetTransactionPending } = properties;

    if (noAssetTransactionPending) {
      analytics.identify(
        user,
        { status: events.transaction.depositAvailable.name },
        { All: false, Mixpanel: true }
      );
      analytics.track(
        user,
        events.transaction.depositAvailable.name,
        {
          All: false,
          Mixpanel: true,
          SlackActions: true
        },
        { ...properties, displayCurrencySymbol: CURRENCY_SYMBOLS[properties.currency] }
      );

      const formattedAmount = CurrencyUtil.formatCurrency(
        properties.amount,
        user.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      );

      await NotificationService.createAppNotification(
        user.id,
        {
          notificationId: TransactionalNotificationEventEnum.DEPOSIT_AVAILABLE,
          properties: new Map(
            Object.entries({
              amount: formattedAmount
            })
          )
        },
        { sendImmediately: true }
      );
    }
  }

  private async _handleDirectDebitDepositChargeback(
    user: UserDocument,
    properties: TrackDirectDebitDepositChargebackPropertiesType
  ): Promise<void> {
    logger.info(`Deposit charged back for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleDirectDebitDepositChargeback",
      data: properties
    });

    analytics.track(
      user,
      events.transaction.directDebitDepositChargeback.name,
      {
        All: false,
        SlackActions: true
      },
      properties
    );
  }

  private async _handleRebalanceTransactionSuccess(
    user: UserDocument,
    properties: TrackTransactionInfoType,
    options: {
      triggeredByAutomation?: boolean;
    } = { triggeredByAutomation: false }
  ): Promise<void> {
    logger.info(`Rebalance succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleRebalanceTransactionSuccess",
      data: properties
    });

    analytics.track(user, events.transaction.rebalanceTransactionSuccess.name, {}, properties);
    if (options.triggeredByAutomation) {
      await Promise.all([
        NotificationService.createAppNotification(
          user.id,
          { notificationId: TransactionalNotificationEventEnum.AUTOMATED_REBALANCE_COMPLETED },
          { sendImmediately: true }
        ),
        NotificationService.createEmailNotification(
          user.id,
          { notificationId: "automatedRebalance" },
          { sendImmediately: true }
        )
      ]);
    } else {
      await NotificationService.createAppNotification(
        user.id,
        { notificationId: TransactionalNotificationEventEnum.REBALANCE_COMPLETED },
        { sendImmediately: true }
      );
    }
  }

  private async _handleFirstInvestmentCreation(user: UserDocument): Promise<void> {
    logger.info(`First investment created for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleFirstInvestmentCreation"
    });
    analytics.track(user, events.transaction.firstInvestmentCreation.name);
    await GoogleAnalyticsService.trackEventGA4({
      user,
      event: EventActionEnum.FIRST_INVESTMENT_CREATED
    });
  }

  private async _handleInvestmentCreation(
    user: UserDocument,
    properties: { isFirst: boolean } & TrackTransactionInfoType
  ): Promise<void> {
    logger.info(`Investment created for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleInvestmentCreation",
      data: properties
    });

    const traits: UserTraitType = {
      status: events.transaction.investmentCreation.name
    };

    if (properties.isFirst) {
      traits.hasInvstd = "Created";
    }

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(
      user,
      events.transaction.investmentCreation.name,
      { Slack: !properties.isFirst },
      { ...properties, displayCurrencySymbol: CURRENCY_SYMBOLS[properties.currency] }
    );

    await Promise.allSettled([
      GoogleAnalyticsService.trackEventGA4({
        user,
        event: EventActionEnum.INVESTMENT_CREATED
      }),
      FacebookAppEventService.trackEvent(events.transaction.investmentCreation.name, user.lastLoginPlatform, {
        email: user.email
      })
    ]);
  }

  private async _handleAssetTransactionSuccess(
    user: UserDocument,
    properties: {
      isFirst: boolean;
    } & TrackTransactionInfoType,
    options: {
      type?: "reward" | "assetTransaction";
    },
    notificationProperties?: TrackNotificationOrderSettled
  ): Promise<void> {
    logger.info(`Investment succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleAssetTransactionSuccess",
      data: { properties, options, notificationProperties }
    });

    const traits: UserTraitType = {
      status: events.transaction.investmentSuccess.name
    };

    if (properties.isFirst) {
      traits.hasInvstd = "Succeeded";

      if (!user.participant) {
        logger.error(`Participant should be populated for ${user.email}`, {
          module: "TransactionEventHandler",
          method: "_handleAssetTransactionSuccess"
        });
        await user.populate("participant");
      }
      const influencerId = user.participant?.metadata?.financeAds?.influencerId;
      if (
        influencerId &&
        new Decimal(properties.amount).greaterThanOrEqualTo(MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY)
      ) {
        await FinanceAdsService.notify({
          influencerId,
          category: "deposit",
          orderValue: properties.amount,
          orderId: user.id
        });
      }
    }

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(
      user,
      events.transaction.investmentSuccess.name,
      {},
      { ...properties, displayCurrencySymbol: CURRENCY_SYMBOLS[properties.currency] }
    );

    /**
     * If type is "assetTransaction" we sent a notification based on frequency
     */
    if (options?.type === "assetTransaction") {
      if (properties.frequency === "one-off") {
        if (!notificationProperties) {
          logger.warn(`Cannot send order settled event with undefined properties for user ${user.email}`, {
            module: "TransactionEventHandler",
            method: "_handleAssetTransactionSuccess"
          });
          return;
        }
        await NotificationService.createAppNotification(
          user.id,
          {
            notificationId: TransactionalNotificationEventEnum.ORDER_SETTLED,
            properties: new Map(Object.entries(notificationProperties))
          },
          { sendImmediately: true }
        );
      } else if (properties.frequency === "repeating") {
        await Promise.all([
          NotificationService.createAppNotification(
            user.id,
            {
              notificationId: TransactionalNotificationEventEnum.REPEATING_INVESTMENT_SETTLED,
              properties: new Map(
                Object.entries({
                  amount: notificationProperties.amount
                })
              )
            },
            { sendImmediately: true }
          ),
          NotificationService.createEmailNotification(
            user.id,
            {
              notificationId: "repeatingInvestment",
              properties: new Map(
                Object.entries({
                  amount: notificationProperties.amount
                })
              )
            },
            { sendImmediately: true }
          )
        ]);
      } else {
        logger.error(`Expected frequency property for user ${user.email} at investment success event.`, {
          module: "TransactionEventHandler",
          method: "_handleAssetTransactionSuccess"
        });
      }
    }
  }

  private async _handleWithdrawalCreation(
    user: UserDocument,
    properties: TrackWithdrawalPropertiesType
  ): Promise<void> {
    logger.info(`Withdrawal created for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleWithdrawalCreation"
    });
    analytics.track(
      user,
      events.transaction.withdrawalCreation.name,
      {},
      { ...properties, displayCurrencySymbol: CURRENCY_SYMBOLS[properties.currency] }
    );
  }

  private async _handleSavingsDividendCreation(
    user: UserDocument,
    properties: TrackNotificationSavingsDividendCreation
  ): Promise<void> {
    logger.info(`Savings dividend created for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleSavingsDividendCreation"
    });

    await NotificationService.createAppNotification(
      user.id,
      {
        notificationId: TransactionalNotificationEventEnum.SAVINGS_DIVIDEND_RECEIVED,
        properties: new Map(Object.entries(properties))
      },
      { sendImmediately: true }
    );
  }

  private async _handleDividendSuccess(
    user: UserDocument,
    properties: TrackNotificationDividendSuccess
  ): Promise<void> {
    logger.info(`Dividend succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleDividendSuccess"
    });

    await NotificationService.createAppNotification(
      user.id,
      {
        notificationId: TransactionalNotificationEventEnum.DIVIDEND_RECEIVED,
        properties: new Map(Object.entries(properties))
      },
      { sendImmediately: true }
    );
  }

  private async _handleAssetDividendSuccess(
    user: UserDocument,
    properties: TrackAssetDividendSuccessPropertiesType
  ): Promise<void> {
    logger.info(`Asset dividend succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleAssetDividendSuccess"
    });

    analytics.track(
      user,
      events.transaction.assetDividendSuccess.name,
      { All: false, Mixpanel: true },
      properties
    );
  }

  private async _handleWealthyhoodDividendSuccess(
    user: UserDocument,
    properties: TrackWealthyhoodDividendPropertiesType
  ): Promise<void> {
    logger.info(`Wealthyhood dividend settled for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleWealthyhoodDividendSuccess"
    });

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    analytics.track(
      user,
      events.transaction.wealthyhoodDividendSuccess.name,
      { All: false, Mixpanel: true },
      properties
    );

    const formattedAmount = CurrencyUtil.formatCurrency(
      properties.amount,
      user.currency,
      ConfigUtil.getDefaultUserLocale(user.residencyCountry)
    );

    const dividendRate = `${Decimal.mul(WEALTHYHOOD_DIVIDEND_RATES[properties.plan], 100).toNumber()}%`;

    await Promise.all([
      NotificationService.createAppNotification(
        user.id,
        {
          notificationId: TransactionalNotificationEventEnum.WEALTHYHOOD_DIVIDEND_CREATED,
          properties: new Map(
            Object.entries({
              ...properties,
              plan: PLAN_CONFIG[properties.plan].name,
              amount: formattedAmount
            } as TrackNotificationWealthyhoodDividendSuccess)
          )
        },
        { sendImmediately: true }
      ),
      NotificationService.createEmailNotification(
        user.id,
        {
          notificationId: "wealthyhoodDividendCreation",
          properties: new Map(
            Object.entries({
              dividend_amount: formattedAmount,
              plan: PLAN_CONFIG[properties.plan].name,
              dividend_rate: dividendRate
            })
          )
        },
        { sendImmediately: true }
      )
    ]);
  }

  private async _handleCustodyChargeSuccess(
    user: UserDocument,
    properties: TrackCustodyChargeSuccess
  ): Promise<void> {
    logger.info(`Custody charge succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleCustodyChargeSuccess"
    });
    analytics.track(user, events.transaction.custodyChargeSuccess.name, {}, properties);
  }

  private async _handleSubscriptionChargeSuccess(
    user: UserDocument,
    properties: TrackSubscriptionChargeSuccess
  ): Promise<void> {
    logger.info(`Subscription charge succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleSubscriptionChargeSuccess"
    });
    analytics.track(user, events.transaction.subscriptionChargeSuccess.name, {}, properties);
  }

  private async _handleSavingsDividendChargeSuccess(
    user: UserDocument,
    properties: TrackSavingsDividendChargeSuccess
  ): Promise<void> {
    logger.info(`Savings dividend charge succeeded for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleSavingsDividendChargeSuccess"
    });
    analytics.track(user, events.transaction.savingsDividendChargeSuccess.name, {}, properties);
  }
}

export default TransactionEventHandler;
