// Load env variables
import "../../loaders/environment";

// Initiate mongo db loader to connect to db
import Mongo<PERSON>oader from "../../loaders/mongo";
import { MongoReadPreferenceEnum } from "../../utils/dbUtil";
const mongoLoader = new MongoLoader(MongoReadPreferenceEnum.SECONDARY);
mongoLoader.init();

import { App } from "@slack/bolt";
import registerListeners from "./listeners";
import { envIsProd } from "../../utils/environmentUtil";
import logger from "../../external-services/loggerService";

const loggerOptions = {
  module: "WealthyhoodOperationsApp",
  method: "main"
};

const app = new App({
  token: process.env.SLACK_BOT_TOKEN,
  signingSecret: process.env.SLACK_SIGNING_SECRET,
  appToken: process.env.SLACK_APP_TOKEN,
  socketMode: true
});

registerListeners(app);

if (envIsProd()) {
  (async () => {
    try {
      await app.start();
      logger.info("⚡️ Slack Bolt app is running! ⚡️", loggerOptions);
    } catch {
      logger.error("🥺 Unable to start Slack Bolt app", loggerOptions);
    }
  })();
} else {
  logger.warn(
    "🙀 Slack Bolt app will not start since we are not running on the production environment",
    loggerOptions
  );
}
