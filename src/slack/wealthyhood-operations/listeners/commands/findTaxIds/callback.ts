import type { AllMiddlewareArgs, SlackCommandMiddlewareArgs } from "@slack/bolt";

import { parseCommaSeparatedList } from "../../../../utils/inputParsingUtil";
import { UserRepository } from "../../../../../repositories/userRepository";

const _getExistingTaxIds = async (inputTaxIds: string[]): Promise<string[]> => {
  const existingUsers = await UserRepository.getUsersByTaxResidencyValues(inputTaxIds);
  return existingUsers.map((user) => user.taxResidency.value);
};

const _createResponseMessage = (inputTaxIds: string[], existingTaxIds: string[]) => {
  return inputTaxIds.length > 0
    ? inputTaxIds
        .map((taxId) => `${taxId} → ${existingTaxIds.includes(taxId) ? "✅ Exists" : "❌ Not Found"}`)
        .join("\n")
    : "No records found.";
};

export const findTaxIdsCallback = async ({
  ack,
  respond,
  command
}: AllMiddlewareArgs & SlackCommandMiddlewareArgs) => {
  await ack();

  try {
    const inputTaxIds = parseCommaSeparatedList(command.text);
    const existingTaxIds = await _getExistingTaxIds(inputTaxIds);
    const message = _createResponseMessage(inputTaxIds, existingTaxIds);

    await respond(message);
  } catch (err) {
    await respond(`⚠️ Error parsing or checking records: ${err instanceof Error ? err.message : "Unknown"}`);
  }
};
