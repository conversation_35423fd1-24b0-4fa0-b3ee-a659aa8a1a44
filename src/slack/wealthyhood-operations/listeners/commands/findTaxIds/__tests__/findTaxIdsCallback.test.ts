import { findTaxIdsCallback } from "../callback";
import { clearDb, closeDb, connectDb } from "../../../../../../tests/utils/db";
import { buildUser } from "../../../../../../tests/utils/generateModels";

const ack = jest.fn();
const respond = jest.fn();

describe("findTaxIdsCallback", () => {
  beforeAll(async () => {
    await connectDb("findTaxIdsCallback");
  });
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterEach(async () => {
    await clearDb();
  });
  afterAll(async () => {
    await closeDb();
  });

  it("should parse CSV and respond with tax ID lookup results", async () => {
    const user = await buildUser();
    const commandText = `123, 456, ${user.taxResidency.value}, 789`;
    await findTaxIdsCallback({ ack, respond, command: { text: commandText } } as any);

    expect(ack).toHaveBeenCalled();
    expect(respond).toHaveBeenCalledWith(
      `123 → ❌ Not Found\n456 → ❌ Not Found\n${user.taxResidency.value} → ✅ Exists\n789 → ❌ Not Found`
    );
  });

  it("should propagate errors from the lookup", async () => {
    const commandText: string = undefined;
    await findTaxIdsCallback({ ack, respond, command: { text: commandText } } as any);

    expect(ack).toHaveBeenCalled();
    expect(respond).toHaveBeenCalledWith(
      "⚠️ Error parsing or checking records: Cannot read properties of undefined (reading 'trim')"
    );
  });
});
