import { parseCommaSeparatedList } from "../inputParsingUtil";

describe("inputParsingUtil", () => {
  describe("parseCommaSeparatedList", () => {
    it("should split a comma-separated string into an array", () => {
      const input = "apple,banana,orange";
      const result = parseCommaSeparatedList(input);
      expect(result).toEqual(["apple", "banana", "orange"]);
    });

    it("should trim whitespace around values", () => {
      const input = " apple , banana , orange ";
      const result = parseCommaSeparatedList(input);
      expect(result).toEqual(["apple", "banana", "orange"]);
    });

    it("should handle a single value without commas", () => {
      const input = "apple";
      const result = parseCommaSeparatedList(input);
      expect(result).toEqual(["apple"]);
    });

    it("should return an empty array for an empty string", () => {
      const input = "";
      const result = parseCommaSeparatedList(input);
      expect(result).toEqual([""]);
    });

    it("should handle strings with extra commas", () => {
      const input = "apple,,banana, ,orange,";
      const result = parseCommaSeparatedList(input);
      expect(result).toEqual(["apple", "", "banana", "", "orange", ""]);
    });

    it("should trim the entire input string", () => {
      const input = "   apple,banana   ";
      const result = parseCommaSeparatedList(input);
      expect(result).toEqual(["apple", "banana"]);
    });
  });
});
